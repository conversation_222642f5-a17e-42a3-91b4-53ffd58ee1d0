#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
参数优化回测脚本 - 贝叶斯优化版本

使用 scikit-optimize 库进行高效的参数优化。
相比穷举法，能够用更少的回测次数找到更好的参数组合。
"""

import numpy as np
import pandas as pd
import time
from typing import Dict, List, Tuple, Any
import warnings
import os
import pickle
from multiprocessing import Pool, cpu_count
from functools import partial
from tqdm import tqdm
from itertools import product
from dataclasses import dataclass
import gc
import sys
from datetime import datetime

# --- 猴子补丁开始 ---
if not hasattr(np, 'NaN'):
    np.NaN = np.nan
# --- 猴子补丁结束 ---

# --- 导入贝叶斯优化库 ---
from skopt import gp_minimize
from skopt.space import Real, Integer
from skopt.utils import use_named_args
from skopt.space import Categorical

warnings.filterwarnings('ignore')

# 导入必要的模块和函数 (请确保这些文件在当前目录下)
try:
    import pandas_ta as ta
    from huice import sub_kline_time, write_analysis_to_kline, batch_judge_trade
    from ku import (smooth_rsi_with_gaussian, get_local_kline_data,
                    fast_rsi_calculation, fast_gaussian_smooth, fast_rsi_numpy)
    from config import calculate_kline_needed
    import trend_analysis_processorV2
    from tradeing import check_trading_single, judge_signal, calc_price
    from datetime import datetime, timedelta
    import re
    # 尝试导入可视化库
    try:
        import matplotlib.pyplot as plt
        import seaborn as sns
        VISUALIZATION_AVAILABLE = True
        # 设置matplotlib后端为非交互式
        plt.switch_backend('Agg')
    except ImportError:
        VISUALIZATION_AVAILABLE = False
        print("警告: 无法导入 matplotlib 或 seaborn。可视化功能将不可用。")

except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保以下模块文件 (huice.py, ku.py, config.py, trend_analysis_processorV2.py, tradeing.py) 在当前目录或Python路径中。")
    # 为了让 skopt 能够正常运行，需要将导入失败的模块路径添加到 sys.path
    # 假设您的模块都在 biquan/geminiCode/极值量化 目录下
    module_dir = os.path.join(os.path.dirname(__file__), '..', '..', '..')
    if os.path.exists(module_dir) and module_dir not in sys.path:
        sys.path.append(module_dir)
        print(f"已将 '{module_dir}' 添加到 Python 路径。请再次尝试运行。")
    exit(1)


@dataclass
class TrendConfig:
    """
    趋势分析配置，使用 dataclass 结构化参数。
    """
    extrema_sensitivity: float = 0.30
    extrema_credibility_threshold: float = 42.0
    high_credibility_threshold: float = 75.0
    drawdown_threshold: float = -1.5
    severe_drawdown_threshold: float = -8.0
    trend_consistency_threshold: float = 70.0
    trend_strength_strong_threshold: float = 4.0
    trend_strength_medium_threshold: float = 2.0
    
def calculate_trade_metrics(kline_df: pd.DataFrame) -> Dict[str, float]:
    """
    计算并返回回测的各项关键指标。
    """
    pct_series = pd.to_numeric(kline_df['达成百分比'], errors='coerce').fillna(0)
    valid_pct = pct_series[pct_series != 0]
    
    if valid_pct.empty:
        return {
            'profit': 0.0,
            'max_drawdown': 0.0,
            'sharpe_ratio': 0.0,
            'calmar_ratio': 0.0,
            'num_trades': 0
        }
        
    total_profit = valid_pct.sum()
    
    cumulative_returns = (1 + valid_pct / 100).cumprod()
    peak = cumulative_returns.cummax()
    drawdown = (cumulative_returns - peak) / peak
    max_drawdown = abs(drawdown.min()) * 100 if not drawdown.empty else 0
    
    daily_returns = (valid_pct / 100).to_numpy()
    if daily_returns.std() == 0:
        sharpe_ratio = 0.0
    else:
        sharpe_ratio = daily_returns.mean() / daily_returns.std()
        
    calmar_ratio = total_profit / max_drawdown if max_drawdown > 0 else 0.0
    
    return {
        'profit': total_profit,
        'max_drawdown': max_drawdown,
        'sharpe_ratio': sharpe_ratio,
        'calmar_ratio': calmar_ratio,
        'num_trades': len(valid_pct)
    }

def run_single_backtest_optimized(kline_df: pd.DataFrame, params: Dict[str, Any], base_my_pram: Dict[str, Any], base_config_15K: TrendConfig, needed_klines: int) -> pd.DataFrame:
    """
    运行单次回测，返回带有交易结果的 DataFrame。
    """
    my_pram = base_my_pram.copy()
    for key, value in params.items():
        if key in my_pram:
            my_pram[key] = value

    config_dict = base_config_15K.__dict__.copy()
    for key, value in params.items():
        if key in config_dict:
            config_dict[key] = value
    config_15K = TrendConfig(**config_dict)
    
    analyzer = trend_analysis_processorV2.TrendAnalyzer(config_15K)
    verifier = trend_analysis_processorV2.UniqueTrendVerifier(config_15K)
    
    df_length = len(kline_df)
    kline_df_with_analysis = kline_df.copy()
    
    invest_advice = np.full(df_length, '', dtype=object)
    trading_signals = np.zeros(df_length, dtype=int)
    now_signals = np.zeros(df_length, dtype=int)
    buy_prices = np.full(df_length, np.nan, dtype=np.float64)
    
    tradingSingle = 0
    close_values = kline_df['close'].values
    
    # 提前计算 RSI 和 Gaussian 平滑值，减少循环内部的重复计算
    all_rsi = fast_rsi_numpy(close_values, my_pram["rsi_length"])
    all_gaussian_rsi = np.full(len(all_rsi), np.nan)
    valid_rsi_indices = ~np.isnan(all_rsi)
    if valid_rsi_indices.any():
        all_gaussian_rsi[valid_rsi_indices] = fast_gaussian_smooth(all_rsi[valid_rsi_indices], my_pram["my_gaussian_sigma"])
    
    for i in range(needed_klines, df_length):
        nowPrice = close_values[i]
        smooth_rsi_window = all_gaussian_rsi[i - needed_klines + 1 : i + 1]
        
        if len(smooth_rsi_window) < 8:
            continue
            
        result = analyzer.analyze_trend(smooth_rsi_window.tolist(), params.get('malen', 10))
        analysis_result = verifier.verify_and_advise([result])
        
        nowSingle = judge_signal(analysis_result['建议操作'])
        tradingSingle, nowSingle = check_trading_single(tradingSingle, nowSingle)
        
        invest_advice[i] = analysis_result.get('建议操作', '')
        trading_signals[i] = tradingSingle
        now_signals[i] = nowSingle
        
        if nowSingle != 0:
            buy_prices[i] = calc_price(nowSingle, nowPrice, 0)
    
    kline_df_with_analysis['RSI'] = all_rsi
    kline_df_with_analysis['RSI_Gaussian_2.0'] = all_gaussian_rsi
    kline_df_with_analysis['投资建议'] = invest_advice
    kline_df_with_analysis['tradingSingle'] = trading_signals
    kline_df_with_analysis['nowSingle'] = now_signals
    kline_df_with_analysis['buyPrice'] = buy_prices
    
    kline_df_with_analysis = batch_judge_trade(kline_df_with_analysis, params['zhiyin'], params['zhisun'], my_pram['windowsTime'], my_pram["Ktime"])
    
    del invest_advice, trading_signals, now_signals, buy_prices, all_rsi, all_gaussian_rsi
    gc.collect()
    
    return kline_df_with_analysis

# ==================== 贝叶斯优化核心逻辑 (重构) ====================

def objective_function_wrapper_final(params_list, kline_df_shared, my_pram_shared, base_config_shared, needed_klines_shared, dimension_names_shared, callback_info=None):
    """
    贝叶斯优化的目标函数。
    接收一组参数（列表形式），并返回我们想要最小化的指标。
    
    使用 functools.partial 来绑定共享数据，避免全局变量初始化问题。
    """
    # 将参数列表转换为字典
    # 这里的 `dimension_names_shared` 已经通过 partial 绑定，不会是 None
    params = dict(zip(dimension_names_shared, params_list))
    
    try:
        # 运行回测，获取完整的 DataFrame
        result_df = run_single_backtest_optimized(
            kline_df_shared, params, my_pram_shared, base_config_shared, needed_klines_shared
        )
        # 计算关键指标
        metrics = calculate_trade_metrics(result_df)
        
        # 贝叶斯优化器默认寻找最小值，所以我们返回夏普比率的负值
        objective_value = -metrics['sharpe_ratio']
        
        # 更新回调信息
        if callback_info is not None:
            callback_info['current_iter'] += 1
            callback_info['best_sharpe'] = max(callback_info['best_sharpe'], metrics['sharpe_ratio'])
            
            # 每10次迭代或找到更好结果时打印详细信息
            if callback_info['current_iter'] % 10 == 0 or metrics['sharpe_ratio'] == callback_info['best_sharpe']:
                elapsed = time.time() - callback_info['start_time']
                avg_time_per_iter = elapsed / callback_info['current_iter']
                remaining_time = avg_time_per_iter * (callback_info['total_iters'] - callback_info['current_iter'])
                
                print(f"\n📊 [{callback_info['period_name']}] 进度: {callback_info['current_iter']}/{callback_info['total_iters']} "
                      f"({callback_info['current_iter']/callback_info['total_iters']*100:.1f}%)")
                print(f"   当前夏普: {metrics['sharpe_ratio']:.4f} | 最佳夏普: {callback_info['best_sharpe']:.4f}")
                print(f"   预计剩余时间: {remaining_time/60:.1f} 分钟")
        
        return objective_value
    except Exception as e:
        import traceback
        print(f"参数组合 {params} 出错: {e}")
        print(traceback.format_exc())
        # 返回一个很大的正值，让优化器知道这个参数组合很差
        return 999999

def analyze_and_save_results_bo(result, kline_df_main, my_pram_main, base_config_main, needed_klines_main, period_name, output_dir):
    """
    分析和保存贝叶斯优化的最终结果。
    """
    if result.x is None:
        print("优化失败，未找到有效结果。")
        return None
        
    print("\n" + "="*100)
    print(f"📈 时间段 {period_name} 贝叶斯优化完成！")
    print("="*100)
    
    # 获取最佳参数和最佳目标值
    best_params_list = result.x
    best_sharpe_ratio = -result.fun # 记住我们优化的是负值
    
    # 将列表形式的最优参数转换为字典
    best_params = {dim.name: value for dim, value in zip(result.space.dimensions, best_params_list)}
    
    print("🥇 最优参数组合:")
    for param, value in best_params.items():
        if isinstance(value, float):
            print(f"  - {param:<25}: {value:.4f}")
        else:
            print(f"  - {param:<25}: {value}")
            
    print(f"\n✨ 最佳夏普比率 (最优指标): {best_sharpe_ratio:.4f}")
    
    # 为了验证，我们可以用最优参数再回测一次，获取所有指标
    final_df = run_single_backtest_optimized(kline_df_main, best_params, my_pram_main, base_config_main, needed_klines_main)
    final_metrics = calculate_trade_metrics(final_df)
    
    print("\n📊 最优参数组合的回测详细指标:")
    print(f"  - 总收益率 (Profit)         : {final_metrics['profit']:.2f}%")
    print(f"  - 最大回撤 (Max Drawdown)   : {final_metrics['max_drawdown']:.2f}%")
    print(f"  - 交易次数 (Num Trades)     : {final_metrics['num_trades']}")
    print(f"  - 夏普比率 (Sharpe Ratio)   : {final_metrics['sharpe_ratio']:.4f}")
    print(f"  - 卡玛比率 (Calmar Ratio)   : {final_metrics['calmar_ratio']:.2f}")
    
    # 可视化优化过程
    if VISUALIZATION_AVAILABLE:
        from skopt.plots import plot_convergence, plot_evaluations
        
        # 绘制收敛图
        plot_convergence(result, yscale='linear')
        plt.title(f'贝叶斯优化收敛图 - {period_name}', fontsize=16)
        plt.xlabel('回测次数', fontsize=12)
        plt.ylabel('最佳夏普比率负值', fontsize=12)
        convergence_path = os.path.join(output_dir, f'convergence_{period_name}.png')
        plt.savefig(convergence_path, dpi=300)
        plt.close()

        # 绘制参数关系图
        plot_evaluations(result, bins=20)
        plt.tight_layout()
        evaluations_path = os.path.join(output_dir, f'evaluations_{period_name}.png')
        plt.savefig(evaluations_path, dpi=300)
        plt.close()
        
    # 保存所有历史结果
    all_results = [{'params': dict(zip(result.space.dimension_names, p)), 'objective_value': f, 'sharpe_ratio': -f} for p, f in zip(result.x_iters, result.func_vals)]
    results_df = pd.DataFrame(all_results)
    history_path = os.path.join(output_dir, f'optimization_history_{period_name}.csv')
    results_df.to_csv(history_path, index=False, encoding='utf-8-sig', float_format='%.4f')
    
    # 返回最佳结果信息
    return {
        'period': period_name,
        'best_params': best_params,
        'best_sharpe_ratio': best_sharpe_ratio,
        'profit': final_metrics['profit'],
        'max_drawdown': final_metrics['max_drawdown'],
        'num_trades': final_metrics['num_trades'],
        'calmar_ratio': final_metrics['calmar_ratio']
    }

def optimize_single_period(period, my_pram, space_dimensions, base_config_15K, n_jobs, output_dir):
    """
    优化单个时间段
    """
    start_time, end_time = period
    period_name = f"{start_time}_to_{end_time}".replace("-", "")
    
    print(f"\n🔄 开始处理时间段: {start_time} 到 {end_time}")
    period_start_time = time.time()
    
    # 更新时间参数
    my_pram_copy = my_pram.copy()
    my_pram_copy["strTime"] = start_time
    my_pram_copy["endTime"] = end_time
    
    # 计算所需K线数量
    needed_klines = calculate_kline_needed(my_pram_copy)
    strTime = sub_kline_time(my_pram_copy["strTime"], needed_klines, my_pram_copy["Ktime"])
    
    try:
        # 获取数据
        kline_df = get_local_kline_data(my_pram_copy["Uname"], my_pram_copy["Ktime"], strTime, my_pram_copy["endTime"])
        if kline_df is None or kline_df.empty:
            raise ValueError("获取到的K线数据为空。")
        print(f"✅ 成功获取到 {len(kline_df)} 条K线数据")
        
        # 启动贝叶斯优化
        dimension_names = [dim.name for dim in space_dimensions]
        
        # 创建回调信息字典
        callback_info = {
            'current_iter': 0,
            'total_iters': 100,
            'best_sharpe': -float('inf'),
            'start_time': time.time(),
            'period_name': period_name
        }
        
        func_with_data = partial(
            objective_function_wrapper_final,
            kline_df_shared=kline_df,
            my_pram_shared=my_pram_copy,
            base_config_shared=base_config_15K,
            needed_klines_shared=needed_klines,
            dimension_names_shared=dimension_names,
            callback_info=callback_info
        )
        
        print(f"\n🚀 开始贝叶斯优化 - {period_name}")
        print(f"   参数维度: {len(space_dimensions)}")
        print(f"   总迭代次数: 100")
        print(f"   并行进程数: {n_jobs}")
        
        # 贝叶斯优化
        result = gp_minimize(
            func=func_with_data,
            dimensions=space_dimensions,
            n_calls=200,
            n_random_starts=20,
            acq_func="gp_hedge",
            n_jobs=n_jobs,
            random_state=42,
        )
        
        # 分析和保存结果
        best_result = analyze_and_save_results_bo(result, kline_df, my_pram_copy, base_config_15K, needed_klines, period_name, output_dir)
        
        period_elapsed_time = time.time() - period_start_time
        if best_result:
            best_result['execution_time'] = period_elapsed_time
            
        print(f"✅ 时间段 {period_name} 处理完成，耗时: {period_elapsed_time:.2f} 秒")
        
        return best_result
        
    except Exception as e:
        print(f"❌ 时间段 {period_name} 处理失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    # --- 1. 基础参数配置 ---
    my_pram = {
        "rsi_length": 14,
        "macd_length": 26,
        "max_lookback_kline_count": 100,
        "my_sma_length": 5,
        "my_ema_length": 5,
        "my_gaussian_sigma": 2.0,
        "windowsTime": 16,
        "Uname": "doge",
        "Ktime": "15m",
        "strTime": "2025-4-1",
        "endTime": "2025-5-2"
    }
    
    # --- 2. 时间段配置 ---
    TIME_PERIODS = [
        # ("2025-6-1", "2025-6-6"),
        ("2024-2-1", "2024-3-1"),
        # ("2024-3-1", "2024-4-1"),
        # ("2025-1-1", "2025-1-15"),
        # ("2025-1-15", "2025-1-31"),
    ]
    
    # --- 3. CPU算力设置 ---
    total_cpu_cores = cpu_count()
    # 根据CPU核心数动态设置最大并行数
    if total_cpu_cores <= 4:
        max_parallel_jobs = max(1, total_cpu_cores - 1)
    elif total_cpu_cores <= 8:
        max_parallel_jobs = total_cpu_cores - 2
    else:
        # 对于高核心数CPU，限制最大并行数避免内存溢出
        max_parallel_jobs = min(total_cpu_cores - 2, 16)
    
    print(f"🖥️  系统CPU核心数: {total_cpu_cores}")
    print(f"⚙️  设置最大并行任务数: {max_parallel_jobs}")
    
    # --- 4. 参数搜索空间定义 ---
    space_dimensions = [
    # 交易参数
    Real(0.5, 3.0, name="zhiyin", prior="uniform"),    # 步长由优化器自适应
    Real(0.1, 3.0, name="zhisun", prior="uniform"),
    Integer(8, 60, name="malen"),

    # 策略指标参数
    Integer(10, 20, name="rsi_length"),
    Real(1.0, 3.0, name="my_gaussian_sigma", prior="uniform"),

    # TrendConfig 参数
    Real(0.2, 0.5, name="extrema_sensitivity", prior="uniform"),
    Integer(30, 50, name="extrema_credibility_threshold"),
    Integer(60, 90, name="high_credibility_threshold"),
    Real(-3.0, -1.0, name="drawdown_threshold", prior="uniform"),
    Real(-12.0, -5.0, name="severe_drawdown_threshold", prior="uniform"),
    Integer(60, 95, name="trend_consistency_threshold"),
    Real(3.0, 5.0, name="trend_strength_strong_threshold", prior="uniform"),
    Real(1.0, 3.0, name="trend_strength_medium_threshold", prior="uniform"),
]
   
    # --- 5. 创建输出文件夹 ---
    output_dir = "optimization"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"📁 创建输出文件夹: {output_dir}")
    
    # --- 6. 基础配置 ---
    base_config_15K = TrendConfig(
        extrema_sensitivity=0.30,
        extrema_credibility_threshold=42.0,
        high_credibility_threshold=75.0,
        drawdown_threshold=-1.5,
        severe_drawdown_threshold=-8.0,
        trend_consistency_threshold=70.0,
        trend_strength_strong_threshold=4.0,
        trend_strength_medium_threshold=2.0
    )
    
    # --- 7. 执行优化 ---
    print("\n🚀 开始批量贝叶斯优化")
    print(f"📊 总共需要处理 {len(TIME_PERIODS)} 个时间段")
    
    # 询问是否使用并行处理
    use_parallel_periods = input("\n是否并行处理多个时间段？(y/n，默认n): ").strip().lower() == 'y'
    
    total_start_time = time.time()
    all_results = []
    
    if use_parallel_periods and len(TIME_PERIODS) > 1:
        # 并行处理时间段
        print(f"\n⚡ 使用并行模式处理 {len(TIME_PERIODS)} 个时间段")
        
        # 决定用于时间段并行的进程数
        period_workers = min(len(TIME_PERIODS), max(1, total_cpu_cores // 4))
        print(f"🔧 分配 {period_workers} 个进程用于时间段并行")
        
        # 每个时间段内部的并行数需要相应减少
        jobs_per_period = max(1, max_parallel_jobs // period_workers)
        print(f"🔧 每个时间段内部使用 {jobs_per_period} 个进程")
        
        # 创建进程池
        with Pool(processes=period_workers) as pool:
            # 准备任务
            tasks = []
            for period in TIME_PERIODS:
                task = pool.apply_async(
                    optimize_single_period,
                    args=(period, my_pram, space_dimensions, base_config_15K, jobs_per_period, output_dir)
                )
                tasks.append(task)
            
            # 监控进度
            completed = 0
            while completed < len(tasks):
                time.sleep(5)  # 每5秒检查一次
                new_completed = sum(1 for task in tasks if task.ready())
                if new_completed > completed:
                    completed = new_completed
                    elapsed = time.time() - total_start_time
                    if completed > 0:
                        avg_time = elapsed / completed
                        remaining = avg_time * (len(tasks) - completed)
                        print(f"\n⏳ 总进度: {completed}/{len(TIME_PERIODS)} 完成 "
                              f"({completed/len(TIME_PERIODS)*100:.1f}%)")
                        print(f"   预计剩余时间: {remaining/60:.1f} 分钟")
            
            # 收集结果
            for task in tasks:
                result = task.get()
                if result:
                    all_results.append(result)
    else:
        # 串行处理时间段（原有逻辑）
        print(f"\n📝 使用串行模式逐个处理时间段")
        for idx, period in enumerate(TIME_PERIODS):
            print(f"\n{'='*80}")
            print(f"📈 进度: {idx + 1}/{len(TIME_PERIODS)}")
            
            # 估算剩余时间
            if idx > 0:
                avg_time_per_period = (time.time() - total_start_time) / idx
                estimated_remaining_time = avg_time_per_period * (len(TIME_PERIODS) - idx)
                print(f"⏱️  预计剩余时间: {estimated_remaining_time/60:.1f} 分钟")
            
            # 优化单个时间段
            result = optimize_single_period(period, my_pram, space_dimensions, base_config_15K, max_parallel_jobs, output_dir)
            if result:
                all_results.append(result)
    
    # --- 8. 汇总结果 ---
    if all_results:
        summary_df = pd.DataFrame(all_results)
        summary_path = os.path.join(output_dir, 'optimization_summary.csv')
        summary_df.to_csv(summary_path, index=False, encoding='utf-8-sig', float_format='%.4f')
        
        print("\n" + "="*100)
        print("📊 所有时间段优化完成！")
        print("="*100)
        print("\n📈 最终汇总结果:")
        print(summary_df[['period', 'best_sharpe_ratio', 'profit', 'max_drawdown', 'num_trades', 'execution_time']])
        
        # 找出最佳时间段
        best_period_idx = summary_df['best_sharpe_ratio'].idxmax()
        best_period_result = summary_df.iloc[best_period_idx]
        print(f"\n🏆 最佳时间段: {best_period_result['period']}")
        print(f"   夏普比率: {best_period_result['best_sharpe_ratio']:.4f}")
        print(f"   总收益率: {best_period_result['profit']:.2f}%")
        print(f"   最大回撤: {best_period_result['max_drawdown']:.2f}%")
        
        print(f"\n📁 所有结果已保存到 '{output_dir}' 文件夹")
    
    total_elapsed_time = time.time() - total_start_time
    print(f"\n⏱️  总耗时: {total_elapsed_time/60:.2f} 分钟")
    print("\n✅ 优化脚本运行完毕。")