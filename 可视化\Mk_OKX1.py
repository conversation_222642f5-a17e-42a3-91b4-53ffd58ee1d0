import os
import okx.Account as Account
import okx.Trade as Trade
from dotenv import load_dotenv
import numpy as np # 导入 numpy 模块，以确保 np.NaN 可用
from datetime import datetime

from MK_OKX_sz import OKXInstrumentInfoManager  ##获取下单sz

# --- 猴子补丁开始 ---
if not hasattr(np, 'NaN'):
    np.NaN = np.nan
# --- 猴子补丁结束 ---

def smart_print_result(result, context="", show_full=False):
    """
    智能打印 OKX API 返回的 result，自动识别类型并打印关键信息

    Args:
        result: OKX API 返回的结果
        context: 上下文描述，用于标识这是什么操作的结果
        show_full: 是否显示完整的 result（调试用）
    """
    if show_full:
        print(f"\n=== 完整 Result ({context}) ===")
        print(result)
        print("=" * 50)
        return

    if not result:
        print(f"❌ {context}: 返回结果为空")
        return

    # 检查基本的 OKX API 响应格式
    if not isinstance(result, dict):
        print(f"⚠️ {context}: 返回结果不是字典格式")
        print(f"Result: {result}")
        return

    code = result.get("code", "")
    msg = result.get("msg", "")
    data = result.get("data", [])

    print(f"\n📊 {context} 结果:")
    print(f"状态码: {code}")

    if code != "0":
        print(f"❌ 操作失败: {msg}")
        return

    print(f"✅ 操作成功: {msg if msg else '成功'}")

    if not data:
        print("📝 无数据返回")
        return

    # 根据数据内容智能识别类型并打印关键信息
    if isinstance(data, list) and len(data) > 0:
        first_item = data[0]

        # 识别订单信息
        if 'ordId' in first_item or 'clOrdId' in first_item:
            _print_order_info(data, context)

        # 识别算法订单信息
        elif 'algoId' in first_item:
            _print_algo_order_info(data, context)

        # 识别持仓信息
        elif 'instId' in first_item and 'pos' in first_item:
            _print_position_info(data, context)

        # 识别账户余额信息
        elif 'details' in first_item:
            _print_balance_info(data, context)

        # 其他类型的数据
        else:
            _print_generic_info(data, context)

    else:
        print(f"📝 数据: {data}")

def _print_order_info(data, context):
    """打印订单信息"""
    print(f"📋 找到 {len(data)} 个订单:")
    for i, order in enumerate(data):
        print(f"  订单 {i+1}:")
        print(f"    🏷️  产品: {order.get('instId', 'N/A')}")
        print(f"    � 产品类型: {order.get('instType', 'N/A')}")
        print(f"    �🆔 订单ID: {order.get('ordId', 'N/A')}")
        print(f"    🏪 客户订单ID: {order.get('clOrdId', 'N/A')}")
        print(f"    📊 订单状态: {order.get('state', 'N/A')}")
        print(f"    � 订单类型: {order.get('ordType', 'N/A')}")
        print(f"    �💰 交易方向: {order.get('side', 'N/A')}")
        print(f"    🎯 持仓方向: {order.get('posSide', 'N/A')}")
        print(f"    📏 订单数量: {order.get('sz', 'N/A')}")
        print(f"    💵 订单价格: {order.get('px', 'N/A')}")
        print(f"    ✅ 成交数量: {order.get('accFillSz', 'N/A')}")
        print(f"    💸 成交金额: {order.get('fillNotionalUsd', 'N/A')}")
        print(f"    📈 平均成交价: {order.get('avgPx', 'N/A')}")
        print(f"    🔧 杠杆倍数: {order.get('lever', 'N/A')}")
        print(f"    📊 交易模式: {order.get('tdMode', 'N/A')}")
        print(f"    🔒 只减仓: {order.get('reduceOnly', 'N/A')}")

        # 手续费信息
        fee = order.get('fee', 'N/A')
        fee_ccy = order.get('feeCcy', 'N/A')
        if fee != 'N/A' and fee_ccy != 'N/A':
            print(f"    💳 手续费: {fee} {fee_ccy}")

        # 时间转换
        c_time = order.get('cTime')
        u_time = order.get('uTime')
        if c_time:
            try:
                c_time_str = datetime.fromtimestamp(int(c_time)/1000).strftime('%Y-%m-%d %H:%M:%S')
                print(f"    🕐 创建时间: {c_time_str}")
            except:
                print(f"    🕐 创建时间: {c_time}")
        if u_time:
            try:
                u_time_str = datetime.fromtimestamp(int(u_time)/1000).strftime('%Y-%m-%d %H:%M:%S')
                print(f"    🔄 更新时间: {u_time_str}")
            except:
                print(f"    🔄 更新时间: {u_time}")
        print()

def _print_algo_order_info(data, context):
    """打印算法订单信息"""
    print(f"🤖 找到 {len(data)} 个算法订单:")
    for i, order in enumerate(data):
        print(f"  算法订单 {i+1}:")
        print(f"    🏷️  产品: {order.get('instId', 'N/A')}")
        print(f"    � 产品类型: {order.get('instType', 'N/A')}")
        print(f"    �🆔 算法ID: {order.get('algoId', 'N/A')}")
        print(f"    📊 订单状态: {order.get('state', 'N/A')}")
        print(f"    🔧 订单类型: {order.get('ordType', 'N/A')}")
        print(f"    💰 交易方向: {order.get('side', 'N/A')}")
        print(f"    🎯 持仓方向: {order.get('posSide', 'N/A')}")
        print(f"    📏 数量: {order.get('sz', 'N/A')}")

        # 止损止盈信息
        if order.get('slTriggerPx'):
            print(f"    🛑 止损触发价: {order.get('slTriggerPx', 'N/A')}")
            print(f"    🔴 止损触发价类型: {order.get('slTriggerPxType', 'N/A')}")
            print(f"    💰 止损委托价: {order.get('slOrdPx', 'N/A')}")
        if order.get('tpTriggerPx'):
            print(f"    🎯 止盈触发价: {order.get('tpTriggerPx', 'N/A')}")
            print(f"    🟢 止盈触发价类型: {order.get('tpTriggerPxType', 'N/A')}")
            print(f"    💰 止盈委托价: {order.get('tpOrdPx', 'N/A')}")

        # 其他重要信息
        print(f"    🔒 只减仓: {order.get('reduceOnly', 'N/A')}")
        print(f"    🔧 杠杆倍数: {order.get('lever', 'N/A')}")
        print(f"    📈 交易模式: {order.get('tdMode', 'N/A')}")
        print(f"    💵 最新价格: {order.get('last', 'N/A')}")

        # 时间转换
        c_time = order.get('cTime')
        u_time = order.get('uTime')
        if c_time:
            try:
                c_time_str = datetime.fromtimestamp(int(c_time)/1000).strftime('%Y-%m-%d %H:%M:%S')
                print(f"    🕐 创建时间: {c_time_str}")
            except:
                print(f"    🕐 创建时间: {c_time}")
        if u_time:
            try:
                u_time_str = datetime.fromtimestamp(int(u_time)/1000).strftime('%Y-%m-%d %H:%M:%S')
                print(f"    🔄 更新时间: {u_time_str}")
            except:
                print(f"    🔄 更新时间: {u_time}")
        print()

def _print_position_info(data, context):
    """打印持仓信息"""
    print(f"💼 找到 {len(data)} 个持仓:")
    for i, pos in enumerate(data):
        print(f"  持仓 {i+1}:")
        print(f"    🏷️  产品: {pos.get('instId', 'N/A')}")
        print(f"    🏭 产品类型: {pos.get('instType', 'N/A')}")
        print(f"    📊 持仓数量: {pos.get('pos', 'N/A')}")
        print(f"    💰 持仓方向: {pos.get('posSide', 'N/A')}")
        print(f"    💵 开仓均价: {pos.get('avgPx', 'N/A')}")
        print(f"    📈 标记价格: {pos.get('markPx', 'N/A')}")
        print(f"    � 杠杆倍数: {pos.get('lever', 'N/A')}")

        # 订单编号相关信息
        pos_id = pos.get('posId', 'N/A')
        trade_id = pos.get('tradeId', 'N/A')
        last_trade_id = pos.get('last', 'N/A')

        print(f"    🆔 持仓ID: {pos_id}")
        if trade_id != 'N/A':
            print(f"    🔄 最新成交ID: {trade_id}")
        if last_trade_id != 'N/A':
            print(f"    � 最新成交编号: {last_trade_id}")

        # 财务信息
        margin = pos.get('margin', 'N/A')
        upl = pos.get('upl', 'N/A')
        upl_ratio = pos.get('uplRatio', 'N/A')
        liq_px = pos.get('liqPx', 'N/A')

        print(f"    💳 保证金: {margin}")
        print(f"    💸 未实现盈亏: {upl}")
        print(f"    � 盈亏比例: {upl_ratio}")
        print(f"    ⚠️  强平价格: {liq_px}")

        # 时间信息（如果有的话）
        c_time = pos.get('cTime')
        u_time = pos.get('uTime')
        if c_time:
            try:
                c_time_str = datetime.fromtimestamp(int(c_time)/1000).strftime('%Y-%m-%d %H:%M:%S')
                print(f"    🕐 创建时间: {c_time_str}")
            except:
                print(f"    🕐 创建时间: {c_time}")
        if u_time:
            try:
                u_time_str = datetime.fromtimestamp(int(u_time)/1000).strftime('%Y-%m-%d %H:%M:%S')
                print(f"    🔄 更新时间: {u_time_str}")
            except:
                print(f"    🔄 更新时间: {u_time}")

        print()

def _print_balance_info(data, context):
    """打印账户余额信息"""
    print(f"💰 账户余额信息:")
    for account in data:
        details = account.get('details', [])
        for detail in details:
            ccy = detail.get('ccy', 'N/A')
            avail_bal = detail.get('availBal', 'N/A')
            frozen_bal = detail.get('frozenBal', 'N/A')
            if float(avail_bal) > 0 or float(frozen_bal) > 0:  # 只显示非零余额
                print(f"    💎 {ccy}: 可用 {avail_bal}, 冻结 {frozen_bal}")

def _print_generic_info(data, context):
    """打印通用信息"""
    print(f"📝 数据项数: {len(data)}")
    if len(data) > 0:
        first_item = data[0]
        print(f"    主要字段: {list(first_item.keys())[:10]}")  # 显示前10个字段

        # 尝试找到一些常见的关键字段
        key_fields = ['instId', 'ordId', 'algoId', 'state', 'side', 'sz', 'px']
        for field in key_fields:
            if field in first_item:
                print(f"    {field}: {first_item[field]}")
    print()

def get_usdt_balance(apikey: str, secretkey: str, passphrase: str, flag: str = "1") -> dict:
    try:
        # 初始化账户 API
        # False 表示不需要代理 (use_proxy)
        accountAPI = Account.AccountAPI(apikey, secretkey, passphrase, False, flag)

        # 查看账户余额
        result = accountAPI.get_account_balance()

        if result and result.get("code") == "0" and result.get("data"):
            # 遍历 details 列表，查找 USDT 的余额信息
            for detail in result["data"][0].get("details", []):
                if detail.get("ccy") == "USDT":
                    # 提取 USDT 的可用余额和冻结余额
                    usdt_avail_bal = detail.get("availBal", "0")
                    usdt_frozen_bal = detail.get("frozenBal", "0")
                    return {
                        "availBal": usdt_avail_bal,
                        "frozenBal": usdt_frozen_bal
                    }
            print("在账户余额中未找到 USDT 信息。")
            return {}
        else:
            print(f"查询账户余额失败: {result.get('msg', '未知错误')}")
            return {}
    except Exception as e:
        print(f"查询过程中发生错误: {e}")
        return {}
    

def get_okx_positions(apikey: str, secretkey: str, passphrase: str, flag: str = "1") -> list:
    """
    查询 OKX 账户的所有持仓信息。

    Args:
        apikey (str): 你的 OKX API Key。
        secretkey (str): 你的 OKX Secret Key。
        passphrase (str): 你的 OKX Passphrase。
        flag (str): 交易环境，"0" 为实盘，"1" 为模拟盘。默认为 "1" (模拟盘)。

    Returns:
        list: 包含所有持仓信息的列表，每个元素是一个持仓字典。
              如果查询失败或没有持仓，则返回空列表。
    """
    try:
        # 初始化账户 API
        # False 表示不需要代理 (use_proxy)
        accountAPI = Account.AccountAPI(apikey, secretkey, passphrase, False, flag)

        # 查看持仓信息
        result = accountAPI.get_positions()

        if result and result.get("code") == "0" and result.get("data"):
            # 返回持仓数据列表
            return result["data"]
        else:
            print(f"查询持仓信息失败: {result.get('msg', '未知错误')}")
            return []
    except Exception as e:
        print(f"查询过程中发生错误: {e}")
        return []


def get_okx_positions_by_coin(apikey: str, secretkey: str, passphrase: str, coin_symbol: str, flag: str = "1") -> list:
    """
    查询 OKX 账户中指定虚拟币的持仓信息。

    Args:
        apikey (str): 你的 OKX API Key。
        secretkey (str): 你的 OKX Secret Key。
        passphrase (str): 你的 OKX Passphrase。
        coin_symbol (str): 虚拟币符号，如 "DOGE"、"BTC"、"ETH" 等。
        flag (str): 交易环境，"0" 为实盘，"1" 为模拟盘。默认为 "1" (模拟盘)。

    Returns:
        list: 包含指定币种持仓信息的列表，每个元素是一个持仓字典。
              如果查询失败或没有该币种持仓，则返回空列表。
    """
    try:
        # 获取所有持仓信息
        all_positions = get_okx_positions(apikey, secretkey, passphrase, flag)

        # 过滤出指定币种的持仓
        coin_positions = []
        for position in all_positions:
            inst_id = position.get('instId', '')
            # 检查是否包含指定的币种符号
            if coin_symbol.upper() in inst_id.upper():
                coin_positions.append(position)

        return coin_positions

    except Exception as e:
        print(f"查询指定币种持仓过程中发生错误: {e}")
        return []



def main():
    """
    主函数，用于加载环境变量并测试 get_usdt_balance 函数。
    """
    # 指定 .env 文件的路径
    # 请根据你的实际文件路径进行修改
    dotenv_path = r'C:\Users\<USER>\Desktop\my_python\claudeV1\量化\123.env'

    # 检查 .env 文件是否存在并加载
    if os.path.exists(dotenv_path):
        load_dotenv(dotenv_path=dotenv_path)
        print(f"已加载 .env 文件: {dotenv_path}")
    else:
        print(f"错误: .env 文件不存在于路径: {dotenv_path}")
        print("请确保 .env 文件存在，并包含您的 OKX_API_KEY, OKX_SECRET_KEY, OKX_PASSPHRASE。")
        return

    # 从环境变量中获取 API 凭据
    # 确保你的 .env 文件中有这些变量
    apikey = os.getenv("OKX_API_KEY")
    secretkey = os.getenv("OKX_SECRET_KEY")
    passphrase = os.getenv("OKX_PASSPHRASE")

    # 检查凭据是否已加载
    if not all([apikey, secretkey, passphrase]):
        print("错误: 无法从环境变量中加载 OKX API 凭据。")
        print("请确保 .env 文件包含 OKX_API_KEY, OKX_SECRET_KEY, OKX_PASSPHRASE。")
        return
    # 设置交易环境： "0" 为实盘, "1" 为模拟盘
    # 根据你的需求修改此标志
    env_flag = "0" # 例如，设置为模拟盘
    
     # 实例化产品信息管理器  
    info_manager = OKXInstrumentInfoManager(apikey, secretkey, passphrase, env_flag)
    

    
    # 查询 USDT 余额
    print(f"\n🔍 查询 OKX 账户信息 (环境: {'模拟盘' if env_flag == '1' else '实盘'})...")
    usdt_balances = get_usdt_balance(apikey, secretkey, passphrase, env_flag)
    if usdt_balances:
        print(f"💰 USDT 可用余额: {usdt_balances['availBal']}, 冻结余额: {usdt_balances['frozenBal']}")

    # 查询所有持仓信息
    positions = get_okx_positions(apikey, secretkey, passphrase, env_flag)
    if positions:
        # 使用智能打印函数显示持仓信息
        fake_result = {"code": "0", "msg": "", "data": positions}
        smart_print_result(fake_result, "账户持仓信息")

    # 查询指定币种持仓（DOGE 示例）
    doge_positions = get_okx_positions_by_coin(apikey, secretkey, passphrase, "DOGE", env_flag)
    if doge_positions:
        fake_result = {"code": "0", "msg": "", "data": doge_positions}
        smart_print_result(fake_result, "DOGE 持仓信息")
        
    # 下单数量计算演示
    Uname = "DOGE-USDT-SWAP"
    TradeMone = 50.0
    BuyPrice = 0.450

    print(f"\n📏 计算下单数量演示:")
    sz_sol = info_manager.calculate_order_sz(Uname, TradeMone, BuyPrice)
    if sz_sol:
        print(f"✅ {Uname}: {TradeMone} USDT @ {BuyPrice} = {sz_sol} 张")
    else:
        print(f"❌ 未能计算 {Uname} 的下单数量")
        

    print("\n" + "="*60)
    print("🔧 交易操作演示")
    print("="*60)

    tradeAPI = Trade.TradeAPI(apikey, secretkey, passphrase, False, env_flag)
    # 通过 ordId 查询订单（如果已经取消也会显示）
    client_order_id='17532638597679c66'
    result = tradeAPI.get_order(
        instId=Uname,
        clOrdId=client_order_id
    )
    smart_print_result(result, "查询指定订单")
    
    ###获取未成交的委托订单
    
    # 查询所有未触发的单向止盈止损策略订单
    result = tradeAPI.order_algos_list(
        instId= "SUI-USDT-SWAP",
        ordType="conditional"
    )
    smart_print_result(result, "查询算法订单列表")
    ###撤销委托订单
    algo_orders = [
    {"instId": "SUI-USDT-SWAP", "algoId": "2719434046868381696"},
    {"instId": "BTC-USDT", "algoId": "590920138287841222"}    ]
    result = tradeAPI.cancel_algo_order(algo_orders)
    smart_print_result(result, "撤销算法订单")

    # 查询所有未成交订单
    result = tradeAPI.get_order_list(
        instId=Uname,
        instType="SWAP"
    )
    smart_print_result(result, "查询未成交订单列表")
    
    ##撤单
    result = tradeAPI.cancel_order(
        instId=Uname, clOrdId = client_order_id)
    smart_print_result(result, "撤销订单")


if __name__ == "__main__":
    main()