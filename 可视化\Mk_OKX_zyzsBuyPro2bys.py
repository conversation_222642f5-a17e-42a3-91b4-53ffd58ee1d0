
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多重确认阶段性止盈止损策略 (单次交易回测模型)
作者: AI Assistant
版本: 3.2 (修正了 testBuy 函数中的 NameError bug)
适用场景: 提供一个价格序列，执行一次完整的、基于K线价格的多头策略回测
"""

import numpy as np
import pandas as pd
from typing import Dict, Optional, List, Tuple
from datetime import datetime
import json
import warnings
warnings.filterwarnings('ignore', category=UserWarning)

# --- 猴子补丁开始 ---
if not hasattr(np, 'NaN'):
    np.NaN = np.nan
# --- 猴子补丁结束 ---

class UltimateTradingStrategy:
    """
    终极交易算法策略类 - 基于经验丰富、纪律严明且善于把握战机的交易员思维

    核心思想：
    第一阶段：布局与试探 (The Setup)
    - 设定最大亏损底线，这是唯一的生命线

    第二阶段：巩固与升级 (The Ascent)
    - 初级盈利时：稍微收紧防线
    - 盈利可观时：防线推过成本价，确保不亏钱
    - 盈利丰厚时：紧紧跟随价格，锁住大部分浮动利润

    第三阶段：收割与储备 (The Harvest)
    - 在预设节点卖出部分仓位，转化为真实利润
    - 利润存入"战争基金"，用于后续加仓

    第四阶段：反击与加仓 (The Counter-Attack)
    - 等待回调机会，用赚来的钱加仓
    - 绝不投入新本金，只用利润进攻

    最终守护：双重安全网 (The Dual Safety Net)
    - "财务总监"：保护本金，确保不超过最大亏损
    - "前线指挥官"：保护利润，跟随市场变化
    """
    
    def __init__(self,
                 # 第一阶段：布局与试探参数
                 max_loss_ratio: float = 0.04,  # 最大亏损底线（生命线）

                 # 第二阶段：巩固与升级参数
                 initial_profit_threshold: float = 0.01,    # 初级盈利阈值 1%
                 significant_profit_threshold: float = 0.025, # 盈利可观阈值 2.5%
                 rich_profit_threshold: float = 0.05,       # 盈利丰厚阈值 5%

                 # 防线收紧参数
                 initial_tighten_ratio: float = 0.008,      # 初级盈利时收紧比例
                 break_even_buffer: float = 0.005,         # 推过成本价的缓冲
                 aggressive_trailing_ratio: float = 0.25,   # 紧跟价格的跟踪比例

                 # 第三阶段：收割与储备参数
                 harvest_levels: Optional[List[Tuple[float, float]]] = None,  # 收割节点 (盈利水平, 卖出比例)

                 # 第四阶段：反击与加仓参数
                 enable_counter_attack: bool = True,        # 是否启用反击加仓
                 pullback_threshold: float = 0.015,        # 回调阈值 1.5%
                 max_add_position_ratio: float = 0.5,      # 最大加仓比例

                 # 双重安全网参数
                 enable_logging: bool = True,
                 volatility_window: int = 5):
        # 终极交易算法参数
        # 第一阶段：布局与试探
        self.max_loss_ratio = max_loss_ratio  # 最大亏损底线（生命线）

        # 第二阶段：巩固与升级
        self.initial_profit_threshold = initial_profit_threshold
        self.significant_profit_threshold = significant_profit_threshold
        self.rich_profit_threshold = rich_profit_threshold

        # 防线收紧参数
        self.initial_tighten_ratio = initial_tighten_ratio
        self.break_even_buffer = break_even_buffer
        self.aggressive_trailing_ratio = aggressive_trailing_ratio

        # 第三阶段：收割与储备
        self.harvest_levels = sorted(harvest_levels, key=lambda x: x[0]) if harvest_levels else [
            (0.02, 0.20),   # 盈利2%时收割20%
            (0.04, 0.25),   # 盈利4%时收割25%
            (0.07, 0.30),   # 盈利7%时收割30%
        ]

        # 第四阶段：反击与加仓
        self.enable_counter_attack = enable_counter_attack
        self.pullback_threshold = pullback_threshold
        self.max_add_position_ratio = max_add_position_ratio

        # 双重安全网
        self.enable_logging = enable_logging
        self.volatility_window = volatility_window
        
        # 参数验证
        self._validate_parameters()

        # 交易状态
        self.reset()

    def _validate_parameters(self):
        """验证终极交易算法参数的合理性"""
        if self.max_loss_ratio <= 0 or self.max_loss_ratio > 0.2:
            raise ValueError("最大亏损比例应该在 (0, 0.2] 范围内")

        if not (self.initial_profit_threshold < self.significant_profit_threshold < self.rich_profit_threshold):
            raise ValueError("盈利阈值应该递增：初级 < 可观 < 丰厚")

        if self.aggressive_trailing_ratio <= 0 or self.aggressive_trailing_ratio > 1:
            raise ValueError("紧跟价格的跟踪比例应该在 (0, 1] 范围内")

        # 验证收割节点设置
        for i, (profit_level, sell_ratio) in enumerate(self.harvest_levels):
            if profit_level <= 0:
                raise ValueError(f"harvest_levels[{i}] 的盈利水平必须大于0")
            if sell_ratio <= 0 or sell_ratio > 1:
                raise ValueError(f"harvest_levels[{i}] 的卖出比例必须在 (0, 1] 范围内")
    
    def reset(self):
        """重置终极交易算法状态"""
        # 基础交易状态
        self.position_size = 0.0
        self.entry_price = 0.0
        self.stop_loss = 0.0
        self.high_water_mark = 0.0
        self.entry_time = None
        self.tick_count = 0

        # 终极算法特有状态
        self.current_phase = "SETUP"  # SETUP, ASCENT, HARVEST, COUNTER_ATTACK
        self.defense_level = "INITIAL"  # INITIAL, TIGHTENED, BREAK_EVEN, AGGRESSIVE
        self.war_fund = 0.0  # 战争基金（已实现利润）
        self.original_position_size = 1.0  # 原始仓位大小
        self.harvest_triggers_hit = [False] * len(self.harvest_levels)
        self.can_add_position = True  # 是否可以加仓
        self.last_peak_price = 0.0  # 上次高点价格，用于检测回调

        # 双重安全网状态
        self.financial_director_stop = 0.0  # 财务总监止损线
        self.field_commander_stop = 0.0    # 前线指挥官止损线

        # 历史记录
        self.price_history = []
        self.stop_loss_history = []
        self.phase_history = []
        self.profit_history = []
        self.position_size_history = []
        self.trade_log = []
        self.harvest_log = []
        self.add_position_log = []

        if self.enable_logging:
            self._log("终极交易算法状态已重置")
    
    def _log(self, message: str, level: str = "INFO"):
        """内部日志记录"""
        if self.enable_logging:
            timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
            log_entry = {'timestamp': timestamp, 'tick': self.tick_count, 'level': level, 'message': message}
            self.trade_log.append(log_entry)
    
    def enter_position(self, price: float, signal_info: Optional[Dict] = None) -> Dict:
        """第一阶段：布局与试探 - 开仓操作"""
        if self.position_size > 0:
            self._log("警告: 已有持仓，无法重复开仓", "WARNING")
            return {'success': False, 'reason': 'ALREADY_IN_POSITION', 'message': '已有持仓'}

        self.reset()

        # 第一阶段：布局与试探
        self.position_size = 1.0
        self.original_position_size = 1.0
        self.entry_price = price
        self.current_phase = "SETUP"
        self.defense_level = "INITIAL"

        # 设定最大亏损底线（生命线）
        self.financial_director_stop = price * (1 - self.max_loss_ratio)
        self.field_commander_stop = self.financial_director_stop
        self.stop_loss = self.financial_director_stop

        self.high_water_mark = price
        self.last_peak_price = price
        self.entry_time = datetime.now()

        # 记录历史
        self.price_history.append(price)
        self.stop_loss_history.append(self.stop_loss)
        self.phase_history.append(self.current_phase)
        self.profit_history.append(0.0)
        self.position_size_history.append(self.position_size)

        result = {
            'success': True, 'action': 'ENTER', 'position_type': 'LONG',
            'price': price, 'stop_loss': self.stop_loss, 'phase': self.current_phase,
            'defense_level': self.defense_level, 'position_size': self.position_size,
            'max_loss_ratio': self.max_loss_ratio,
            'message': f"布局与试探: 价格={price:.6f}, 生命线={self.stop_loss:.6f}"
        }
        self._log(f"第一阶段-布局与试探: 价格={price:.6f}, 仓位={self.position_size:.2f}, 生命线={self.stop_loss:.6f}")
        return result
    
    def process_tick(self, price: float, timestamp: Optional[datetime] = None) -> Dict:
        """终极交易算法核心处理逻辑"""
        self.tick_count += 1

        if self.position_size <= 0:
            return {'success': False, 'action': 'NO_POSITION', 'price': price, 'message': '当前无持仓'}

        self.price_history.append(price)
        self.high_water_mark = max(self.high_water_mark, price)
        current_profit_pct = (price - self.entry_price) / self.entry_price

        # 第三阶段：收割与储备
        self._execute_harvest(price, current_profit_pct)
        if self.position_size <= 0:
            return self._exit_position(price, 'ALL_HARVESTED', timestamp)

        # 第二阶段：巩固与升级 - 更新防线
        old_phase = self.current_phase
        old_defense = self.defense_level
        self._update_defense_system(price, current_profit_pct)

        # 第四阶段：反击与加仓 - 检查加仓机会
        self._check_counter_attack_opportunity(price, current_profit_pct)

        # 双重安全网 - 更新止损
        old_stop_loss = self.stop_loss
        self._update_dual_safety_net(price, current_profit_pct)

        # 记录历史
        self.stop_loss_history.append(self.stop_loss)
        self.phase_history.append(self.current_phase)
        self.profit_history.append(current_profit_pct * 100)
        self.position_size_history.append(self.position_size)

        # 检查生命线（绝对不能突破的底线）
        if price <= self.financial_director_stop:
            return self._exit_position(price, 'LIFE_LINE_BREACH', timestamp)

        # 检查最终止损线
        if price <= self.stop_loss:
            return self._exit_position(price, 'STOP_LOSS', timestamp)

        result = {
            'success': True, 'action': 'HOLD', 'position_type': 'LONG',
            'price': price, 'profit_pct': current_profit_pct * 100,
            'stop_loss': self.stop_loss, 'phase': self.current_phase,
            'defense_level': self.defense_level, 'position_size': self.position_size,
            'war_fund': self.war_fund, 'high_water_mark': self.high_water_mark,
            'phase_changed': old_phase != self.current_phase,
            'defense_changed': old_defense != self.defense_level,
            'message': f'{self.current_phase}阶段-{self.defense_level}防线'
        }

        if result['phase_changed']:
            self._log(f"阶段升级: {old_phase} -> {self.current_phase}, 价格={price:.6f}")
        if result['defense_changed']:
            self._log(f"防线升级: {old_defense} -> {self.defense_level}, 价格={price:.6f}")
        if abs(old_stop_loss - self.stop_loss) > 1e-8:
            self._log(f"止损更新: {old_stop_loss:.6f} -> {self.stop_loss:.6f}")

        return result

    def _execute_harvest(self, price: float, profit_pct: float):
        """第三阶段：收割与储备 - 将浮动盈利转化为真实利润"""
        # 按顺序检查收割节点，每次只触发一个
        for i, (target_profit, harvest_ratio) in enumerate(self.harvest_levels):
            if not self.harvest_triggers_hit[i] and profit_pct >= target_profit:
                # 基于原始仓位计算收割数量
                size_to_harvest = self.original_position_size * harvest_ratio
                # 确保不会卖出超过当前持有的仓位
                size_to_harvest = min(size_to_harvest, self.position_size)

                if size_to_harvest > 0:  # 只有当有仓位可收割时才执行
                    # 计算这部分的实际利润并存入战争基金
                    realized_profit = size_to_harvest * profit_pct
                    self.war_fund += realized_profit
                    self.position_size -= size_to_harvest
                    self.harvest_triggers_hit[i] = True

                    # 更新阶段为收割
                    if self.current_phase != "HARVEST":
                        self.current_phase = "HARVEST"

                    log_entry = {
                        'tick': self.tick_count, 'price': price,
                        'profit_pct_at_harvest': profit_pct * 100,
                        'target_profit': target_profit * 100,
                        'harvest_ratio': harvest_ratio, 'size_harvested': size_to_harvest,
                        'remaining_size': self.position_size,
                        'war_fund_added': realized_profit,
                        'total_war_fund': self.war_fund
                    }
                    self.harvest_log.append(log_entry)
                    self._log(f"收割与储备: 盈利{profit_pct*100:.2f}%达到{target_profit*100:.2f}%节点. "
                              f"收割仓位 {size_to_harvest:.2f} (原始仓位的{harvest_ratio*100:.0f}%), "
                              f"战争基金增加 {realized_profit:.4f}, 剩余仓位 {self.position_size:.2f}", "HARVEST")

                    # 每次只执行一个收割节点，避免同时触发多个
                    break

    def _update_defense_system(self, price: float, profit_pct: float):
        """第二阶段：巩固与升级 - 更新防线系统"""
        old_phase = self.current_phase
        old_defense = self.defense_level

        # 阶段升级逻辑
        if profit_pct >= self.rich_profit_threshold:
            self.current_phase = "ASCENT"
            self.defense_level = "AGGRESSIVE"
        elif profit_pct >= self.significant_profit_threshold:
            self.current_phase = "ASCENT"
            self.defense_level = "BREAK_EVEN"
        elif profit_pct >= self.initial_profit_threshold:
            self.current_phase = "ASCENT"
            self.defense_level = "TIGHTENED"
        else:
            self.current_phase = "SETUP"
            self.defense_level = "INITIAL"
    
    def _check_counter_attack_opportunity(self, price: float, profit_pct: float):
        """第四阶段：反击与加仓 - 检查回调加仓机会"""
        if not self.enable_counter_attack or self.war_fund <= 0 or not self.can_add_position:
            return

        # 检测是否有足够的回调
        if self.high_water_mark > self.last_peak_price:
            self.last_peak_price = self.high_water_mark

        # 计算从最高点的回调幅度
        pullback_from_peak = (self.high_water_mark - price) / self.high_water_mark

        # 如果回调达到阈值且有战争基金，考虑加仓
        if (pullback_from_peak >= self.pullback_threshold and
            profit_pct > 0 and  # 仍然盈利
            self.war_fund > 0):

            # 计算加仓数量（使用战争基金，不超过最大加仓比例）
            max_add_size = self.original_position_size * self.max_add_position_ratio
            affordable_size = self.war_fund / profit_pct  # 战争基金能买多少仓位
            add_size = min(max_add_size, affordable_size)

            if add_size > 0.01:  # 最小加仓单位
                # 执行加仓
                self.position_size += add_size
                used_fund = add_size * profit_pct
                self.war_fund -= used_fund
                self.can_add_position = False  # 每次回调只加仓一次
                self.current_phase = "COUNTER_ATTACK"

                log_entry = {
                    'tick': self.tick_count, 'price': price,
                    'pullback_pct': pullback_from_peak * 100,
                    'add_size': add_size, 'used_fund': used_fund,
                    'new_position_size': self.position_size,
                    'remaining_war_fund': self.war_fund
                }
                self.add_position_log.append(log_entry)
                self._log(f"反击与加仓: 回调{pullback_from_peak*100:.2f}%，加仓 {add_size:.2f}，"
                          f"使用战争基金 {used_fund:.4f}，新仓位 {self.position_size:.2f}", "COUNTER_ATTACK")

    def _calculate_volatility(self) -> float:
        """计算最近价格的波动率"""
        if len(self.price_history) < self.volatility_window:
            return 0.02  # 默认2%波动率

        recent_prices = self.price_history[-self.volatility_window:]
        returns = [abs((recent_prices[i] - recent_prices[i-1]) / recent_prices[i-1])
                  for i in range(1, len(recent_prices))]
        return np.mean(returns) if returns else 0.02

    def _update_dual_safety_net(self, price: float, profit_pct: float):
        """双重安全网系统 - 财务总监 + 前线指挥官"""

        # 财务总监：保护本金，确保不超过最大亏损底线
        # 这个止损线永远不变，是绝对的生命线
        self.financial_director_stop = self.entry_price * (1 - self.max_loss_ratio)

        # 前线指挥官：根据当前防线等级设置战术止损
        if self.defense_level == "INITIAL":
            # 初始防线：保持生命线
            self.field_commander_stop = self.financial_director_stop

        elif self.defense_level == "TIGHTENED":
            # 收紧防线：稍微向上移动
            tightened_stop = self.entry_price * (1 - self.initial_tighten_ratio)
            self.field_commander_stop = max(self.field_commander_stop, tightened_stop)

        elif self.defense_level == "BREAK_EVEN":
            # 推过成本价：确保不亏钱
            break_even_stop = self.entry_price * (1 + self.break_even_buffer)
            self.field_commander_stop = max(self.field_commander_stop, break_even_stop)

        elif self.defense_level == "AGGRESSIVE":
            # 紧跟价格：动态跟踪止损
            trailing_distance = (self.high_water_mark - self.entry_price) * self.aggressive_trailing_ratio
            aggressive_stop = self.high_water_mark - trailing_distance
            # 确保不低于盈亏平衡点
            break_even_stop = self.entry_price * (1 + self.break_even_buffer)
            self.field_commander_stop = max(self.field_commander_stop, aggressive_stop, break_even_stop)

        # 最终止损线：取两位顾问建议中更严格（价格更高）的那一个
        self.stop_loss = max(self.financial_director_stop, self.field_commander_stop)
    
    def manual_exit(self, price: float, reason: str = 'MANUAL', timestamp: Optional[datetime] = None) -> Dict:
        """手动平仓"""
        if self.position_size <= 0:
            return {'success': False, 'action': 'NO_POSITION', 'message': '当前无持仓'}
        return self._exit_position(price, reason, timestamp)
    
    def _exit_position(self, price: float, reason: str, timestamp: Optional[datetime] = None) -> Dict:
        """终极交易算法平仓处理"""
        if self.position_size <= 0 and reason not in ['ALL_HARVESTED', 'ALL_PARTS_TAKEN']:
            return {'success': False, 'message': '无持仓'}

        # 计算最终收益
        final_profit_on_remaining = (price - self.entry_price) / self.entry_price if self.position_size > 0 else 0
        final_realized_profit = self.war_fund + (self.position_size * final_profit_on_remaining)
        average_profit_pct = final_realized_profit / self.original_position_size * 100

        max_profit = (self.high_water_mark - self.entry_price) / self.entry_price
        exit_time = timestamp or datetime.now()
        holding_duration = exit_time - self.entry_time if self.entry_time else None

        result = {
            'success': True, 'action': 'EXIT', 'reason': reason, 'position_type': 'LONG',
            'price': price, 'entry_price': self.entry_price,
            'final_profit_pct': average_profit_pct,
            'max_profit_pct': max_profit * 100, 'final_phase': self.current_phase,
            'final_defense_level': self.defense_level,
            'war_fund_total': self.war_fund,
            'original_position_size': self.original_position_size,
            'final_position_size': self.position_size,
            'holding_duration': str(holding_duration), 'exit_time': exit_time.isoformat(),
            'harvest_log': self.harvest_log,
            'add_position_log': self.add_position_log,
            'message': f"战役结束：{reason}, 最终收益={average_profit_pct:.2f}%"
        }

        final_position_size = self.position_size
        self.position_size = 0.0 # 清仓

        self._log(f"战役结束: 原因={reason}, 价格={price:.6f}, 剩余仓位={final_position_size:.2f}, "
                  f"战争基金={self.war_fund:.4f}, 最终收益={average_profit_pct:.2f}%, "
                  f"最大浮盈={max_profit*100:.2f}%", "EXIT")

        return result

def translate_keys_to_chinese(data: Dict) -> Dict:
    """递归地将字典中的英文键翻译为中文。"""
    translation_map = {
        "day": "天", "reference_price": "参考价格", "k_line_high": "K线最高价", 
        "k_line_low": "K线最低价", "strategy_signal": "策略信号", "action": "动作", 
        "message": "信息", "current_strategy_status": "当前策略状态", "entry_price": "入场价格", 
        "stage": "阶段", "stop_loss": "止损价格", "realtime_profit": "实时盈利", 
        "max_profit": "最大浮盈", "current_day_high_vs_entry_ratio_growth": "当日最高价相对入场价增长率",
        "success": "成功", "reason": "原因", "position_type": "仓位类型", "price": "价格", 
        "final_profit_pct": "最终平均盈利%", "max_profit_pct": "最大浮盈%", "final_stage": "最终阶段", 
        "total_ticks": "总Tick数", "holding_duration": "持仓时长", "exit_time": "平仓时间", 
        "high_water_mark": "最高水位", "has_position": "有持仓", "current_price": "当前价格", 
        "profit_pct": "盈利%", "distance_to_stop_pct": "距止损%", "current_profit_pct": "当前盈利%", 
        "current_drawdown_pct": "当前回撤%", "price_volatility": "价格波动率", "stage_distribution": "阶段分布", 
        "error": "错误", "entry_time": "入场时间", "stock_name": "股票名称", "position_size": "当前仓位",
        "partial_profits_log": "分批止盈记录", "size_sold": "卖出份额", "remaining_size": "剩余份额"
    }
    if isinstance(data, dict):
        return {translation_map.get(k, k): translate_keys_to_chinese(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [translate_keys_to_chinese(elem) for elem in data] 
    return data

def simulate_single_trade_backtest(price_series: List[float], strategy_params: Optional[Dict] = None, verbose: bool = True) -> Dict:
    if not price_series or len(price_series) < 1: return {'final_profit_pct': 0.0, 'message': '价格序列无效'}
    if strategy_params is None: strategy_params = {}
    strategy = UltimateTradingStrategy(enable_logging=False, **strategy_params)
    entry_price = price_series[0]
    strategy.enter_position(entry_price)
    trade_outcome_info = None
    for day_idx, current_price in enumerate(price_series[1:], 1):
        if strategy.position_size <= 0: break
        if pd.isna(current_price): continue
        process_result = strategy.process_tick(current_price)
        if process_result.get('action') == 'EXIT':
            trade_outcome_info = process_result
            break
    if strategy.position_size > 0:
        trade_outcome_info = strategy.manual_exit(price_series[-1], 'END_OF_DATA')
    if trade_outcome_info is None:
        return {'final_profit_pct': 0.0, 'message': '交易未产生平仓事件', 'strategy_instance': strategy}
    return {'final_profit_pct': trade_outcome_info.get('final_profit_pct', 0.0), 'strategy_instance': strategy, 'final_result_details': trade_outcome_info}

def testBuy(buysellHistory_list, strategy_params, max_output_trades=50, detailed_output=True):
    total_net_profit, all_trade_results = 0.0, []
    print("\n=== 终极交易算法多组数据回测 ===")

    total_trades = len(buysellHistory_list[0])
    print(f"总交易数: {total_trades}")
    print(f"输出策略: 前20个详细 + 重要交易(盈亏>2%或每10个)")

    processed_count = 0

    for i in range(total_trades):
        trade_type = buysellHistory_list[0][i]
        
        if trade_type != 1:
            continue

        test_prices_for_this_trade = buysellHistory_list[1][i]

        if not test_prices_for_this_trade:
            continue

        processed_count += 1

        # 控制详细输出 - 只显示有盈利或亏损的重要交易
        profit = 0  # 先计算profit
        single_trade_result = simulate_single_trade_backtest(
            test_prices_for_this_trade,
            strategy_params=strategy_params,
            verbose=False
        )
        profit = single_trade_result['final_profit_pct']

        # 只显示重要交易：前20个，或者盈利>2%，或者亏损>2%
        show_details = (detailed_output and
                       (i < 20 or abs(profit) > 2.0 or i % 10 == 0))

        if show_details:
            print(f"\n--- 开始模拟第 {i+1} 组交易 ---")
        elif i == 20:
            print(f"\n... 后续只显示重要交易（大盈亏或每10个交易） ...")
        
        # profit已经在上面计算过了
        total_net_profit += profit
        all_trade_results.append(profit)

        if show_details:
            print(f"本次交易模拟结束。最终平均收益: {profit:.2f}%")

            # 显示收割与储备详情
            if single_trade_result.get('final_result_details') and single_trade_result['final_result_details'].get('harvest_log'):
                print("收割与储备详情:")
                total_harvested = 0
                for j, log in enumerate(single_trade_result['final_result_details']['harvest_log'], 1):
                    harvest_pct = log['harvest_ratio'] * 100
                    total_harvested += log['size_harvested']
                    print(f"  第{j}次收割: 盈利{log['profit_pct_at_harvest']:.2f}%时, 收割{log['size_harvested']:.2f}仓位({harvest_pct:.0f}%), "
                          f"战争基金+{log['war_fund_added']:.4f}, 剩余{log['remaining_size']:.2f}")
                print(f"  总计收割: {total_harvested:.2f}, 战争基金总额: {single_trade_result['final_result_details'].get('war_fund_total', 0):.4f}")

            # 显示反击与加仓详情
            if single_trade_result.get('final_result_details') and single_trade_result['final_result_details'].get('add_position_log'):
                print("反击与加仓详情:")
                for j, log in enumerate(single_trade_result['final_result_details']['add_position_log'], 1):
                    print(f"  第{j}次加仓: 回调{log['pullback_pct']:.2f}%时, 加仓{log['add_size']:.2f}, "
                          f"使用基金{log['used_fund']:.4f}, 新仓位{log['new_position_size']:.2f}")

            # 显示交易结果详情
            if single_trade_result.get('final_result_details'):
                details = single_trade_result['final_result_details']
                print(f"交易详情: 入场{details.get('entry_price', 0):.4f} -> 出场{details.get('price', 0):.4f}")
                print(f"最大浮盈: {details.get('max_profit_pct', 0):.2f}%, 平仓原因: {details.get('reason', 'UNKNOWN')}")

            print(f"当前累计总净盈亏: {total_net_profit:.2f}%")
            print("-" * 80)

        # 显示进度
        if len(all_trade_results) % 50 == 0 and len(all_trade_results) > 0:
            progress = len(all_trade_results) / total_trades * 100
            print(f"\n进度: {len(all_trade_results)}/{total_trades} ({progress:.1f}%) - 当前累计: {total_net_profit:.2f}%")

    print("\n" + "="*80)
    print("=== 回测结果统计 ===")
    print("="*80)

    # 基本统计
    total_trades = len(all_trade_results)
    win_trades = sum(1 for p in all_trade_results if p > 0)
    loss_trades = sum(1 for p in all_trade_results if p < 0)
    flat_trades = total_trades - win_trades - loss_trades
    win_rate = win_trades / total_trades * 100 if total_trades > 0 else 0

    # 盈亏统计
    profits = [p for p in all_trade_results if p > 0]
    losses = [p for p in all_trade_results if p < 0]
    avg_profit = np.mean(profits) if profits else 0
    avg_loss = np.mean(losses) if losses else 0
    max_profit = max(all_trade_results) if all_trade_results else 0
    max_loss = min(all_trade_results) if all_trade_results else 0

    # 盈亏比
    profit_loss_ratio = abs(avg_profit / avg_loss) if avg_loss != 0 else float('inf')

    print(f"总交易数: {total_trades}")
    print(f"胜率: {win_rate:.2f}% ({win_trades}胜 / {loss_trades}负 / {flat_trades}平)")
    print(f"平均盈利: {avg_profit:.2f}% | 平均亏损: {avg_loss:.2f}%")
    print(f"最大盈利: {max_profit:.2f}% | 最大亏损: {max_loss:.2f}%")
    print(f"盈亏比: {profit_loss_ratio:.2f}")
    print(f"总净盈亏: {total_net_profit:.2f}%")
    print(f"平均每笔收益: {total_net_profit/total_trades:.2f}%" if total_trades > 0 else "平均每笔收益: 0.00%")
    print("="*80)

def main():
    print("\n\n" + "="*60)
    print("=== 终极交易算法演示: 经验丰富交易员的智慧结晶 ===")
    print("="*60)

    # --- 终极交易算法参数 ---
    # 基于经验丰富、纪律严明且善于把握战机的交易员思维设计
    ultimate_strategy_params = {
        # 第一阶段：布局与试探参数
        'max_loss_ratio': 0.04,  # 最大亏损底线4%（生命线）

        # 第二阶段：巩固与升级参数
        'initial_profit_threshold': 0.01,      # 初级盈利阈值 1%
        'significant_profit_threshold': 0.025, # 盈利可观阈值 2.5%
        'rich_profit_threshold': 0.05,         # 盈利丰厚阈值 5%

        # 防线收紧参数
        'initial_tighten_ratio': 0.008,        # 初级盈利时收紧比例 0.8%
        'break_even_buffer': 0.005,           # 推过成本价的缓冲 0.5%
        'aggressive_trailing_ratio': 0.25,     # 紧跟价格的跟踪比例 25%

        # 第三阶段：收割与储备参数
        'harvest_levels': [
            (0.02, 0.20),   # 盈利2%时收割20%仓位
            (0.04, 0.25),   # 盈利4%时收割25%仓位
            (0.07, 0.30),   # 盈利7%时收割30%仓位
            (0.10, 0.25),   # 盈利10%时收割剩余25%仓位
        ],

        # 第四阶段：反击与加仓参数
        'enable_counter_attack': True,          # 启用反击加仓
        'pullback_threshold': 0.015,           # 回调阈值 1.5%
        'max_add_position_ratio': 0.5,         # 最大加仓比例 50%

        # 双重安全网参数
        'volatility_window': 5,                 # 波动率计算窗口
    }
    print("\n使用的终极交易算法参数:")
    print(json.dumps(translate_keys_to_chinese(ultimate_strategy_params), indent=2, ensure_ascii=False))

    # --- 为演示创建模拟数据 ---
    # 在您的环境中，请注释掉这段模拟数据，并使用您自己的真实数据加载
    print("\n*** 注意: 正在使用模拟数据进行演示。 ***")
    price_series_1 = [100, 101, 102.5, 101.5, 103.6, 105, 103, 107.1] # 触发分批止盈
    price_series_2 = [100, 101, 99, 98, 97.5] # 初始止损
    price_series_3 = [100, 102.1, 101.5, 102.2, 101.8, 102.3] # 触发阶段1确认并收紧止损
    buysellHistory_data = [
        [1, 1, 1], # 交易类型 (1=多头)
        [price_series_1, price_series_2, price_series_3], # K线最高价序列
        [[], [], []]  # K线最低价序列 (未使用)
    ]
    # --- 模拟数据结束 ---
    
    from MkKu import load_json
    buysellHistory_data = load_json('buysellHistory.json')

    # 调用 testBuy 函数来执行多组数据回测
    # 限制详细输出前50个交易，避免输出过长
    testBuy(buysellHistory_data, ultimate_strategy_params, max_output_trades=50, detailed_output=True)
    
if __name__ == "__main__":
    main()
