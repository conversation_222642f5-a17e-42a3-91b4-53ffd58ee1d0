import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from collections import Counter
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def advanced_trade_analysis(trade_data, initial_capital=10000, risk_free_rate=0.02):
    """
    高级交易决策分析系统
    
    参数:
    trade_data: 交易数据列表 - 可以是 any_good_bad 格式的列表
    initial_capital: 初始资金
    risk_free_rate: 无风险利率
    
    返回:
    包含交易建议和策略优化的详细分析
    """
    
    # 检查输入数据格式并处理
    if not trade_data:
        return {"error": "数据为空"}
    
    # 处理不同的输入格式
    if isinstance(trade_data[0], list) and len(trade_data[0]) == 3:
        # 标准格式: [direction, max_profit, max_drawdown]
        pass
    else:
        # 如果是其他格式，尝试转换
        print(f"警告: 输入数据格式不符合预期，数据样本: {trade_data[0] if trade_data else 'None'}")
        return {"error": "数据格式不正确"}
    
    def percent_to_float(percent_str):
        """将百分比字符串转换为浮点数"""
        if isinstance(percent_str, str):
            return float(percent_str.replace('%', ''))
        return float(percent_str)  # 如果已经是数字
    """
    高级交易决策分析系统
    
    参数:
    trade_data: 交易数据列表
    initial_capital: 初始资金
    risk_free_rate: 无风险利率
    
    返回:
    包含交易建议和策略优化的详细分析
    """
    
    def percent_to_float(percent_str):
        return float(percent_str.replace('%', ''))
    
    # 数据预处理
    processed_data = []
    for i, trade in enumerate(trade_data):
        direction = trade[0]
        max_profit = percent_to_float(trade[1])
        max_drawdown = percent_to_float(trade[2])  # 这已经是负数
        
        # 注意：最大收益和最大回撤是独立的指标，不应该相加
        # 这里我们假设交易的最终收益需要其他方式计算，或者用其他指标
        # 暂时用最大收益作为潜在收益，最大回撤作为风险指标
        
        processed_data.append({
            'trade_id': i + 1,
            'direction': direction,
            'direction_name': '多仓' if direction == 1 else '空仓',
            'max_profit': max_profit,
            'max_drawdown': max_drawdown,  # 保持原始负值
            'profit_potential': max_profit,  # 盈利潜力
            'risk_exposure': abs(max_drawdown),  # 风险暴露
            'risk_reward_ratio': max_profit / abs(max_drawdown) if max_drawdown != 0 else float('inf'),
            'is_high_profit': max_profit > 2.0,  # 高收益交易（>2%）
            'is_high_risk': abs(max_drawdown) > 2.0,  # 高风险交易（回撤>2%）
        })
    
    df = pd.DataFrame(processed_data)
    
    # 1. 基础统计分析
    def basic_analysis():
        total_trades = len(df)
        long_trades = df[df['direction'] == 1]
        short_trades = df[df['direction'] == -1]
        
        stats = {
            'total_trades': total_trades,
            'long_count': len(long_trades),
            'short_count': len(short_trades),
            'avg_max_profit': df['max_profit'].mean(),
            'avg_max_drawdown': df['max_drawdown'].mean(),
            'avg_risk_exposure': df['risk_exposure'].mean(),
            'long_avg_profit': long_trades['max_profit'].mean() if len(long_trades) > 0 else 0,
            'short_avg_profit': short_trades['max_profit'].mean() if len(short_trades) > 0 else 0,
            'long_avg_drawdown': long_trades['max_drawdown'].mean() if len(long_trades) > 0 else 0,
            'short_avg_drawdown': short_trades['max_drawdown'].mean() if len(short_trades) > 0 else 0,
            'best_profit': df['max_profit'].max(),
            'worst_drawdown': df['max_drawdown'].min(),
            'high_profit_ratio': df['is_high_profit'].mean() * 100,
            'high_risk_ratio': df['is_high_risk'].mean() * 100
        }
        return stats
    
    # 2. 风险分析
    def risk_analysis():
        profits = df['max_profit'].values
        drawdowns = df['max_drawdown'].values
        risk_exposures = df['risk_exposure'].values
        
        # 计算风险指标
        profit_volatility = np.std(profits)
        drawdown_volatility = np.std(drawdowns)
        avg_risk_reward = df['risk_reward_ratio'].mean()
        
        # 分位数分析
        profit_75th = np.percentile(profits, 75)
        profit_25th = np.percentile(profits, 25)
        drawdown_75th = np.percentile(drawdowns, 25)  # 注意：回撤越小越好
        drawdown_25th = np.percentile(drawdowns, 75)
        
        return {
            'profit_volatility': profit_volatility,
            'drawdown_volatility': drawdown_volatility,
            'avg_risk_reward': avg_risk_reward,
            'max_profit_observed': np.max(profits),
            'max_drawdown_observed': np.min(drawdowns),
            'profit_75th_percentile': profit_75th,
            'profit_25th_percentile': profit_25th,
            'drawdown_75th_percentile': drawdown_75th,
            'drawdown_25th_percentile': drawdown_25th,
            'avg_risk_exposure': np.mean(risk_exposures)
        }
    
    # 3. 策略效能分析
    def strategy_performance():
        long_trades = df[df['direction'] == 1]
        short_trades = df[df['direction'] == -1]
        
        performance = {}
        
        for direction, trades, name in [(1, long_trades, '多仓'), (-1, short_trades, '空仓')]:
            if len(trades) > 0:
                performance[name] = {
                    'count': len(trades),
                    'avg_max_profit': trades['max_profit'].mean(),
                    'avg_max_drawdown': trades['max_drawdown'].mean(),
                    'avg_risk_reward': trades['risk_reward_ratio'].mean(),
                    'best_profit': trades['max_profit'].max(),
                    'worst_drawdown': trades['max_drawdown'].min(),
                    'profit_consistency': trades['max_profit'].std(),
                    'drawdown_consistency': trades['max_drawdown'].std(),
                    'high_profit_ratio': trades['is_high_profit'].mean() * 100,
                    'high_risk_ratio': trades['is_high_risk'].mean() * 100
                }
        
        return performance
    
    # 4. 交易模式分析
    def pattern_analysis():
        # 风险收益比质量分析
        excellent_trades = df[df['risk_reward_ratio'] > 3]
        good_trades = df[(df['risk_reward_ratio'] >= 2) & (df['risk_reward_ratio'] <= 3)]
        fair_trades = df[(df['risk_reward_ratio'] >= 1) & (df['risk_reward_ratio'] < 2)]
        poor_trades = df[df['risk_reward_ratio'] < 1]
        
        # 收益分布分析
        high_profit_trades = df[df['max_profit'] > 2.0]
        medium_profit_trades = df[(df['max_profit'] >= 1.0) & (df['max_profit'] <= 2.0)]
        low_profit_trades = df[df['max_profit'] < 1.0]
        
        # 风险分布分析
        high_risk_trades = df[df['risk_exposure'] > 2.0]
        medium_risk_trades = df[(df['risk_exposure'] >= 1.0) & (df['risk_exposure'] <= 2.0)]
        low_risk_trades = df[df['risk_exposure'] < 1.0]
        
        return {
            'excellent_trades_count': len(excellent_trades),
            'good_trades_count': len(good_trades),
            'fair_trades_count': len(fair_trades),
            'poor_trades_count': len(poor_trades),
            'high_profit_count': len(high_profit_trades),
            'medium_profit_count': len(medium_profit_trades),
            'low_profit_count': len(low_profit_trades),
            'high_risk_count': len(high_risk_trades),
            'medium_risk_count': len(medium_risk_trades),
            'low_risk_count': len(low_risk_trades),
            'avg_excellent_profit': excellent_trades['max_profit'].mean() if len(excellent_trades) > 0 else 0,
            'avg_excellent_drawdown': excellent_trades['max_drawdown'].mean() if len(excellent_trades) > 0 else 0
        }
    
    # 5. 资金管理分析
    def money_management():
        # 基于风险收益比的仓位建议
        avg_risk_reward = df['risk_reward_ratio'].mean()
        avg_profit = df['max_profit'].mean()
        avg_drawdown = abs(df['max_drawdown'].mean())
        
        # 简化的Kelly公式（基于风险收益比）
        if avg_risk_reward > 1:
            # 假设胜率基于风险收益比估算
            estimated_win_rate = min(0.7, avg_risk_reward / (avg_risk_reward + 1))
            kelly_fraction = estimated_win_rate - (1 - estimated_win_rate) / avg_risk_reward
            kelly_fraction = max(0, min(kelly_fraction, 0.25))  # 限制在0-25%之间
        else:
            kelly_fraction = 0
        
        # 保守的仓位建议
        conservative_position = kelly_fraction * 0.5
        recommended_position = min(conservative_position, 0.05)  # 最大不超过5%
        
        return {
            'avg_risk_reward': avg_risk_reward,
            'avg_profit_potential': avg_profit,
            'avg_risk_exposure': avg_drawdown,
            'kelly_fraction': kelly_fraction,
            'conservative_position': conservative_position,
            'recommended_position': recommended_position
        }
    
    # 6. 交易建议生成
    def generate_recommendations():
        basic_stats = basic_analysis()
        risk_stats = risk_analysis()
        strategy_perf = strategy_performance()
        patterns = pattern_analysis()
        money_mgmt = money_management()
        
        recommendations = []
        
        # 策略选择建议 - 基于平均收益对比
        if '多仓' in strategy_perf and '空仓' in strategy_perf:
            long_avg_profit = strategy_perf['多仓']['avg_max_profit']
            short_avg_profit = strategy_perf['空仓']['avg_max_profit']
            long_avg_drawdown = abs(strategy_perf['多仓']['avg_max_drawdown'])
            short_avg_drawdown = abs(strategy_perf['空仓']['avg_max_drawdown'])
            
            if long_avg_profit > short_avg_profit + 0.5:  # 收益差距>0.5%
                recommendations.append({
                    'type': '策略优化',
                    'priority': 'HIGH',
                    'suggestion': f"多仓策略收益更优秀（平均{long_avg_profit:.2f}% vs {short_avg_profit:.2f}%），建议增加多仓交易频率",
                    'action': '重点关注多仓机会，减少空仓操作'
                })
            elif short_avg_profit > long_avg_profit + 0.5:
                recommendations.append({
                    'type': '策略优化',
                    'priority': 'HIGH',
                    'suggestion': f"空仓策略收益更优秀（平均{short_avg_profit:.2f}% vs {long_avg_profit:.2f}%），建议增加空仓交易频率",
                    'action': '重点关注空仓机会，减少多仓操作'
                })
        
        # 风险管理建议 - 基于风险收益比
        if risk_stats['avg_risk_reward'] < 1.5:
            recommendations.append({
                'type': '风险管理',
                'priority': 'HIGH',
                'suggestion': f"平均风险收益比过低（{risk_stats['avg_risk_reward']:.2f}），风险调整后收益不佳",
                'action': '需要提高交易筛选标准，只做高质量交易'
            })
        
        # 回撤控制建议
        if risk_stats['max_drawdown_observed'] < -5.0:
            recommendations.append({
                'type': '风险控制',
                'priority': 'CRITICAL',
                'suggestion': f"最大观察回撤过大（{risk_stats['max_drawdown_observed']:.2f}%），风险过高",
                'action': '立即降低仓位大小，加强止损管理'
            })
        
        # 仓位管理建议
        if money_mgmt['recommended_position'] > 0.01:
            recommendations.append({
                'type': '仓位管理',
                'priority': 'MEDIUM',
                'suggestion': f"建议仓位大小：{money_mgmt['recommended_position']*100:.1f}%",
                'action': f"基于风险收益比分析，建议谨慎控制仓位"
            })
        
        # 交易质量建议
        total_trades = patterns['excellent_trades_count'] + patterns['good_trades_count'] + patterns['fair_trades_count'] + patterns['poor_trades_count']
        quality_ratio = (patterns['excellent_trades_count'] + patterns['good_trades_count']) / total_trades if total_trades > 0 else 0
        
        if quality_ratio < 0.5:
            recommendations.append({
                'type': '交易质量',
                'priority': 'MEDIUM',
                'suggestion': f"高质量交易占比过低（{quality_ratio*100:.1f}%）",
                'action': '提高交易筛选标准，专注于风险收益比>2的机会'
            })
        
        # 收益潜力建议
        if basic_stats['avg_max_profit'] < 1.0:
            recommendations.append({
                'type': '收益改善',
                'priority': 'MEDIUM',
                'suggestion': f"平均收益潜力偏低（{basic_stats['avg_max_profit']:.2f}%）",
                'action': '寻找更高收益潜力的交易机会，或优化入场时机'
            })
        
        return recommendations
    
    # 7. 未来交易指导
    def future_trading_guide():
        strategy_perf = strategy_performance()
        basic_stats = basic_analysis()
        risk_stats = risk_analysis()
        
        # 确定推荐方向
        if '多仓' in strategy_perf and '空仓' in strategy_perf:
            long_avg_profit = strategy_perf['多仓']['avg_max_profit']
            short_avg_profit = strategy_perf['空仓']['avg_max_profit']
            long_risk_reward = strategy_perf['多仓']['avg_risk_reward']
            short_risk_reward = strategy_perf['空仓']['avg_risk_reward']
            
            # 综合考虑收益和风险收益比
            long_score = long_avg_profit * long_risk_reward
            short_score = short_avg_profit * short_risk_reward
            
            if long_score > short_score:
                preferred_direction = '多仓'
                avoid_direction = '空仓'
            else:
                preferred_direction = '空仓'
                avoid_direction = '多仓'
        else:
            preferred_direction = '未知'
            avoid_direction = '未知'
        
        guide = {
            'preferred_direction': preferred_direction,
            'avoid_direction': avoid_direction,
            'optimal_risk_reward': max(2.0, risk_stats['avg_risk_reward']),  # 至少2.0或当前平均值
            'stop_loss_level': risk_stats['drawdown_75th_percentile'],  # 75%分位数作为止损参考
            'take_profit_level': risk_stats['profit_75th_percentile'],   # 75%分位数作为止盈参考
            'min_profit_target': basic_stats['avg_max_profit'] * 1.5,  # 1.5倍平均收益作为最低目标
            'max_risk_tolerance': abs(basic_stats['avg_max_drawdown']) * 0.8  # 80%平均回撤作为最大容忍
        }
        
        return guide
    
    # 执行所有分析
    basic_stats = basic_analysis()
    risk_stats = risk_analysis()
    strategy_perf = strategy_performance()
    patterns = pattern_analysis()
    money_mgmt = money_management()
    recommendations = generate_recommendations()
    trading_guide = future_trading_guide()
    
    # 综合评分
    def calculate_strategy_score():
        basic_stats = basic_analysis()
        risk_stats = risk_analysis()
        strategy_perf = strategy_performance()
        
        score = 0
        
        # 平均收益评分 (0-25分)
        avg_profit = basic_stats['avg_max_profit']
        if avg_profit > 3.0:
            score += 25
        elif avg_profit > 2.0:
            score += 20
        elif avg_profit > 1.0:
            score += 15
        elif avg_profit > 0.5:
            score += 10
        else:
            score += 5
        
        # 风险收益比评分 (0-25分)
        avg_rr = risk_stats['avg_risk_reward']
        if avg_rr > 3.0:
            score += 25
        elif avg_rr > 2.0:
            score += 20
        elif avg_rr > 1.5:
            score += 15
        elif avg_rr > 1.0:
            score += 10
        else:
            score += 5
        
        # 风险控制评分 (0-25分)
        max_drawdown = abs(risk_stats['max_drawdown_observed'])
        if max_drawdown < 2.0:
            score += 25
        elif max_drawdown < 3.0:
            score += 20
        elif max_drawdown < 5.0:
            score += 15
        elif max_drawdown < 8.0:
            score += 10
        else:
            score += 5
        
        # 一致性评分 (0-25分)
        profit_volatility = risk_stats['profit_volatility']
        if profit_volatility < 0.5:
            score += 25
        elif profit_volatility < 1.0:
            score += 20
        elif profit_volatility < 1.5:
            score += 15
        elif profit_volatility < 2.0:
            score += 10
        else:
            score += 5
        
        return min(score, 100)
    
    strategy_score = calculate_strategy_score()
    
    return {
        'basic_stats': basic_stats,
        'risk_analysis': risk_stats,
        'strategy_performance': strategy_perf,
        'pattern_analysis': patterns,
        'money_management': money_mgmt,
        'recommendations': recommendations,
        'trading_guide': trading_guide,
        'strategy_score': strategy_score,
        'raw_data': df
    }

def print_comprehensive_analysis(trade_data):
    """
    打印全面的分析报告
    
    参数:
    trade_data: 可以是原始交易数据列表，也可以是分析结果字典
    """
    # 检查输入数据类型
    if isinstance(trade_data, dict) and 'strategy_score' in trade_data:
        # 如果已经是分析结果，直接使用
        analysis_result = trade_data
    else:
        # 如果是原始数据，先进行分析
        print("正在分析交易数据...")
        analysis_result = advanced_trade_analysis(trade_data)
        
        # 检查分析是否成功
        if 'error' in analysis_result:
            print(f"分析失败: {analysis_result['error']}")
            return
    
    print("=" * 80)
    print("🎯 高级交易决策分析报告")
    print("=" * 80)
    
    # 策略评分
    score = analysis_result['strategy_score']
    score_level = "优秀" if score >= 80 else "良好" if score >= 60 else "一般" if score >= 40 else "需要改进"
    print(f"\n📊 策略综合评分: {score:.1f}/100 ({score_level})")
    
    # 基础统计
    basic = analysis_result['basic_stats']
    print(f"\n📈 基础统计:")
    print(f"  总交易次数: {basic['total_trades']}")
    print(f"  多仓交易: {basic['long_count']}次，空仓交易: {basic['short_count']}次")
    print(f"  平均最大收益: {basic['avg_max_profit']:.3f}%")
    print(f"  平均最大回撤: {basic['avg_max_drawdown']:.3f}%")
    print(f"  多仓平均收益: {basic['long_avg_profit']:.3f}%")
    print(f"  空仓平均收益: {basic['short_avg_profit']:.3f}%")
    print(f"  多仓平均回撤: {basic['long_avg_drawdown']:.3f}%")
    print(f"  空仓平均回撤: {basic['short_avg_drawdown']:.3f}%")
    print(f"  最佳收益: {basic['best_profit']:.3f}%")
    print(f"  最大回撤: {basic['worst_drawdown']:.3f}%")
    print(f"  高收益交易占比: {basic['high_profit_ratio']:.1f}%")
    print(f"  高风险交易占比: {basic['high_risk_ratio']:.1f}%")
    
    # 风险分析
    risk = analysis_result['risk_analysis']
    print(f"\n⚠️  风险分析:")
    print(f"  平均风险收益比: {risk['avg_risk_reward']:.2f}")
    print(f"  收益波动性: {risk['profit_volatility']:.3f}%")
    print(f"  回撤波动性: {risk['drawdown_volatility']:.3f}%")
    print(f"  最大观察收益: {risk['max_profit_observed']:.3f}%")
    print(f"  最大观察回撤: {risk['max_drawdown_observed']:.3f}%")
    print(f"  收益75%分位数: {risk['profit_75th_percentile']:.3f}%")
    print(f"  回撤75%分位数: {risk['drawdown_75th_percentile']:.3f}%")
    
    # 策略表现
    strategy = analysis_result['strategy_performance']
    print(f"\n🔍 策略表现对比:")
    for direction, perf in strategy.items():
        print(f"  {direction}:")
        print(f"    交易次数: {perf['count']}")
        print(f"    平均最大收益: {perf['avg_max_profit']:.3f}%")
        print(f"    平均最大回撤: {perf['avg_max_drawdown']:.3f}%")
        print(f"    平均风险收益比: {perf['avg_risk_reward']:.2f}")
        print(f"    最佳收益: {perf['best_profit']:.3f}%")
        print(f"    最差回撤: {perf['worst_drawdown']:.3f}%")
        print(f"    高收益交易占比: {perf['high_profit_ratio']:.1f}%")
    
    # 交易建议
    recommendations = analysis_result['recommendations']
    print(f"\n💡 交易建议:")
    for i, rec in enumerate(recommendations, 1):
        priority_icon = "🔴" if rec['priority'] == 'CRITICAL' else "🟠" if rec['priority'] == 'HIGH' else "🟡"
        print(f"  {i}. {priority_icon} [{rec['type']}] {rec['suggestion']}")
        print(f"     行动建议: {rec['action']}")
    
    # 未来交易指导
    guide = analysis_result['trading_guide']
    print(f"\n🎯 未来交易指导:")
    print(f"  推荐方向: {guide['preferred_direction']}")
    print(f"  避免方向: {guide['avoid_direction']}")
    print(f"  最低风险收益比: {guide['optimal_risk_reward']:.1f}")
    print(f"  建议止损水平: {guide['stop_loss_level']:.2f}%")
    print(f"  建议止盈水平: {guide['take_profit_level']:.2f}%")
    print(f"  最低收益目标: {guide['min_profit_target']:.2f}%")
    print(f"  最大风险容忍: {guide['max_risk_tolerance']:.2f}%")
    
    # 仓位管理
    money = analysis_result['money_management']
    print(f"\n💰 仓位管理建议:")
    print(f"  推荐仓位大小: {money['recommended_position']*100:.1f}%")
    print(f"  平均风险收益比: {money['avg_risk_reward']:.2f}")
    print(f"  平均收益潜力: {money['avg_profit_potential']:.2f}%")
    print(f"  平均风险暴露: {money['avg_risk_exposure']:.2f}%")
    
    print("\n" + "=" * 80)

# 示例使用
if __name__ == "__main__":
    # 你的交易数据
    trade_data = [
        [-1, '2.810%', '-0.123%'], [-1, '0.857%', '-1.207%'], [-1, '0.644%', '-0.647%'], 
        [-1, '1.732%', '-0.237%'], [-1, '0.300%', '-2.294%'], [-1, '0.602%', '-1.660%'], 
        [-1, '0.973%', '-0.999%'], [-1, '0.672%', '-0.977%'], [1, '0.448%', '-0.752%'], 
        [-1, '2.312%', '-0.981%'], [-1, '0.259%', '-1.078%'], [-1, '1.054%', '-1.146%'], 
        [-1, '1.996%', '-0.057%'], [-1, '1.454%', '-3.012%'], [-1, '2.597%', '-0.975%'], 
        [-1, '2.366%', '-2.813%'], [-1, '1.123%', '-1.196%'], [1, '0.739%', '-0.551%'], 
        [1, '0.350%', '-0.935%'], [-1, '1.202%', '-1.281%'], [-1, '2.924%', '-0.334%'], 
        [-1, '2.107%', '-0.060%'], [1, '0.214%', '-3.113%'], [1, '1.552%', '-0.674%'], 
        [1, '0.872%', '-1.085%'], [1, '1.519%', '-1.332%'], [1, '1.925%', '-2.649%'], 
        [-1, '1.468%', '-1.094%'], [-1, '1.096%', '-1.456%'], [-1, '0.654%', '-1.887%']
    ]
    
    # 进行高级分析
    result = advanced_trade_analysis(trade_data)
    
    # 打印分析报告
    print_comprehensive_analysis(result)