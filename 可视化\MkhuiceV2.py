from okx_sdkV1 import get_kline_data, OKXClient
from MkKu import analyze_numerical_series,get_local_kline_data
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import os
from datetime import datetime

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.ticker import FuncFormatter
import pandas as pd
import numpy as np
from datetime import datetime
import os
from collections import deque


import pandas as pd
import numpy as np
from typing import Optional, Dict,Tuple


def calculate_ema(df: pd.DataFrame, periods: list = [10, 20, 50], names: Optional[list] = None,
                  price_column: str = 'close', inplace: bool = True) -> Optional[pd.DataFrame]:
    """
    计算指数移动平均线 (EMA)
    
    Parameters:
    -----------
    df : pd.DataFrame
        包含价格数据的DataFrame
    periods : list, default [10, 20, 50]
        EMA周期列表
    names : list, optional
        与周期对应的列名列表
    price_column : str, default 'close'
        用于计算EMA的价格列名
    inplace : bool, default True
        是否直接修改原DataFrame
    
    Returns:
    --------
    pd.DataFrame or None
        如果inplace=False，返回修改后的DataFrame；否则返回None
    
    Raises:
    -------
    ValueError: 当数据不足或参数错误时抛出异常
    """
    # 数据验证
    if df.empty:
        raise ValueError("DataFrame不能为空")
    
    if price_column not in df.columns:
        raise ValueError(f"列 '{price_column}' 不存在于DataFrame中")
    
    # 检查数据量是否足够
    max_period = max(periods)
    if len(df) < max_period:
        raise ValueError(f"数据量不足：需要至少 {max_period} 条数据，当前只有 {len(df)} 条")
    
    # 检查价格数据是否有效
    if df[price_column].isnull().any():
        raise ValueError(f"价格列 '{price_column}' 包含空值")
    
    # 确保价格为数值类型
    try:
        df[price_column] = pd.to_numeric(df[price_column], errors='raise')
    except ValueError:
        raise ValueError(f"价格列 '{price_column}' 包含非数值数据")
    
    # 选择目标DataFrame
    target_df = df if inplace else df.copy()
    
    # 计算各周期的EMA
    if names and len(names) != len(periods):
        raise ValueError("The length of 'names' must match the length of 'periods'")

    for i, period in enumerate(periods):
        if period <= 0:
            raise ValueError(f"EMA周期必须为正数，当前为: {period}")
        
        column_name = names[i] if names else f'ema_{period}'
        target_df[column_name] = target_df[price_column].ewm(span=period, adjust=False).mean()
    
    if not inplace:
        return target_df
    
    return None


def calculate_bollinger_bands(df: pd.DataFrame, period: int = 20, std_dev: float = 2, 
                             price_column: str = 'close', inplace: bool = True) -> Optional[pd.DataFrame]:

    # 数据验证
    if df.empty:
        raise ValueError("DataFrame不能为空")
    
    if price_column not in df.columns:
        raise ValueError(f"列 '{price_column}' 不存在于DataFrame中")
    
    # 检查数据量是否足够
    if len(df) < period:
        raise ValueError(f"数据量不足：需要至少 {period} 条数据，当前只有 {len(df)} 条")
    
    # 参数验证
    if period <= 0:
        raise ValueError(f"周期必须为正数，当前为: {period}")
    
    if std_dev <= 0:
        raise ValueError(f"标准差倍数必须为正数，当前为: {std_dev}")
    
    # 检查价格数据是否有效
    if df[price_column].isnull().any():
        raise ValueError(f"价格列 '{price_column}' 包含空值")
    
    # 确保价格为数值类型
    try:
        df[price_column] = pd.to_numeric(df[price_column], errors='raise')
    except ValueError:
        raise ValueError(f"价格列 '{price_column}' 包含非数值数据")
    
    # 选择目标DataFrame
    target_df = df if inplace else df.copy()
    
    # 计算布林带
    # 中轨：简单移动平均线
    target_df['BB_middle'] = target_df[price_column].rolling(window=period).mean()
    
    # 标准差
    rolling_std = target_df[price_column].rolling(window=period).std()
    
    # 上轨和下轨
    target_df['BB_upper'] = target_df['BB_middle'] + (rolling_std * std_dev)
    target_df['BB_down'] = target_df['BB_middle'] - (rolling_std * std_dev)
    
    # 计算布林带宽度和位置
    target_df['BB_width'] = target_df['BB_upper'] - target_df['BB_down']
    target_df['BB_position'] = ((target_df[price_column] - target_df['BB_down']) /
                                         target_df['BB_width']) * 100
    
    if not inplace:
        return target_df
    
    return None


def calculate_adx(df: pd.DataFrame, period: int = 14, price_columns: dict = None,
                  inplace: bool = True) -> Optional[pd.DataFrame]:
    """
    计算平均方向指数 (ADX)、正方向指标 (+DI) 和负方向指标 (-DI)
    
    Parameters:
    -----------
    df : pd.DataFrame
        包含价格数据的DataFrame
    period : int, default 14
        ADX计算周期
    price_columns : dict, default None
        价格列名字典，格式：{'high': 'high', 'low': 'low', 'close': 'close'}
        如果为None，使用默认列名
    inplace : bool, default True
        是否直接修改原DataFrame
    
    Returns:
    --------
    pd.DataFrame or None
        如果inplace=False，返回修改后的DataFrame；否则返回None
        添加的列：'+DI', '-DI', 'ADX'
    
    Raises:
    -------
    ValueError: 当数据不足或参数错误时抛出异常
    """
    # 默认列名
    if price_columns is None:
        price_columns = {'high': 'high', 'low': 'low', 'close': 'close'}
    
    # 数据验证
    if df.empty:
        raise ValueError("DataFrame不能为空")
    
    required_columns = ['high', 'low', 'close']
    for col_type in required_columns:
        col_name = price_columns.get(col_type, col_type)
        if col_name not in df.columns:
            raise ValueError(f"列 '{col_name}' 不存在于DataFrame中")
    
    # 检查数据量是否足够（ADX需要更多数据来稳定）
    min_required = period * 2
    if len(df) < min_required:
        raise ValueError(f"数据量不足：需要至少 {min_required} 条数据，当前只有 {len(df)} 条")
    
    # 参数验证
    if period <= 0:
        raise ValueError(f"周期必须为正数，当前为: {period}")
    
    # 检查价格数据是否有效
    for col_type in required_columns:
        col_name = price_columns.get(col_type, col_type)
        if df[col_name].isnull().any():
            raise ValueError(f"价格列 '{col_name}' 包含空值")
        
        try:
            df[col_name] = pd.to_numeric(df[col_name], errors='raise')
        except ValueError:
            raise ValueError(f"价格列 '{col_name}' 包含非数值数据")
    
    # 选择目标DataFrame
    target_df = df if inplace else df.copy()
    
    # 获取列名
    high_col = price_columns['high']
    low_col = price_columns['low']
    close_col = price_columns['close']
    
    # 计算True Range (TR)
    target_df['H-L'] = target_df[high_col] - target_df[low_col]
    target_df['H-PC'] = abs(target_df[high_col] - target_df[close_col].shift(1))
    target_df['L-PC'] = abs(target_df[low_col] - target_df[close_col].shift(1))
    target_df['TR'] = target_df[['H-L', 'H-PC', 'L-PC']].max(axis=1)
    
    # 计算方向移动 (Directional Movement)
    target_df['H-PH'] = target_df[high_col] - target_df[high_col].shift(1)
    target_df['PL-L'] = target_df[low_col].shift(1) - target_df[low_col]
    
    # 计算+DM和-DM
    target_df['+DM'] = np.where(
        (target_df['H-PH'] > target_df['PL-L']) & (target_df['H-PH'] > 0),
        target_df['H-PH'], 0
    )
    target_df['-DM'] = np.where(
        (target_df['PL-L'] > target_df['H-PH']) & (target_df['PL-L'] > 0),
        target_df['PL-L'], 0
    )
    
    # 计算平滑的TR、+DM、-DM
    target_df['TR_smooth'] = target_df['TR'].ewm(span=period, adjust=False).mean()
    target_df['+DM_smooth'] = target_df['+DM'].ewm(span=period, adjust=False).mean()
    target_df['-DM_smooth'] = target_df['-DM'].ewm(span=period, adjust=False).mean()
    
    # 计算方向指标 (+DI, -DI) - 这些列将被保留
    target_df['+DI'] = 100 * (target_df['+DM_smooth'] / target_df['TR_smooth'])
    target_df['-DI'] = 100 * (target_df['-DM_smooth'] / target_df['TR_smooth'])
    
    # 计算DX
    target_df['DX'] = 100 * (abs(target_df['+DI'] - target_df['-DI']) /
                             (target_df['+DI'] + target_df['-DI']))
    
    # 计算ADX
    target_df['ADX'] = target_df['DX'].ewm(span=period, adjust=False).mean()
    
    # 清理临时列（注意：+DI和-DI不在清理列表中）
    temp_columns = ['H-L', 'H-PC', 'L-PC', 'TR', 'H-PH', 'PL-L', '+DM', '-DM',
                    'TR_smooth', '+DM_smooth', '-DM_smooth', 'DX']
    target_df.drop(columns=temp_columns, inplace=True)
    
    if not inplace:
        return target_df
    
    return None


def apply_all_indicators(df: pd.DataFrame, 
                        ema_periods: list = [10, 20, 50],
                        bb_period: int = 20,
                        bb_std_dev: float = 2,
                        adx_period: int = 14,
                        price_columns: dict = None,
                        inplace: bool = True) -> Optional[pd.DataFrame]:
    """
    一次性应用所有技术指标
    
    Parameters:
    -----------
    df : pd.DataFrame
        包含价格数据的DataFrame
    ema_periods : list, default [10, 20, 50]
        EMA周期列表
    bb_period : int, default 20
        布林带周期
    bb_std_dev : float, default 2
        布林带标准差倍数
    adx_period : int, default 14
        ADX周期
    price_columns : dict, default None
        价格列名字典
    inplace : bool, default True
        是否直接修改原DataFrame
    
    Returns:
    --------
    pd.DataFrame or None
        如果inplace=False，返回修改后的DataFrame；否则返回None
    """
    # 选择目标DataFrame
    target_df = df if inplace else df.copy()
    
    # 应用EMA
    calculate_ema(target_df, periods=ema_periods, inplace=True)
    
    # 应用布林带
    calculate_bollinger_bands(target_df, period=bb_period, std_dev=bb_std_dev, inplace=True)
    
    # 应用ADX
    calculate_adx(target_df, period=adx_period, price_columns=price_columns, inplace=True)
    
    if not inplace:
        return target_df
    
    return None




def mark_invalid_indicators(df: pd.DataFrame, ema_periods: list = [10, 20, 50], 
                           bb_period: int = 20, adx_period: int = 14, inplace: bool = True) -> Optional[pd.DataFrame]:
    """标记技术指标的无效区域
    
    参数:
    df (pd.DataFrame): 包含技术指标的DataFrame
    ema_periods (list): EMA计算周期列表
    bb_period (int): 布林带计算周期  
    adx_period (int): ADX计算周期
    inplace (bool): 是否直接修改原DataFrame
    
    返回:
    pd.DataFrame or None: 如果inplace=False，返回修改后的DataFrame
    """
    target_df = df if inplace else df.copy()
    
    # 计算各指标的有效起始位置
    max_ema_period = max(ema_periods)
    ema_valid_start = max_ema_period - 1
    bb_valid_start = bb_period - 1
    adx_valid_start = adx_period * 2 - 1
    
    # 标记EMA指标无效区域
    ema_columns = ['ema_kuai', 'ema_zhong', 'ema_man']
    for col in ema_columns:
        if col in target_df.columns:
            target_df.loc[:ema_valid_start-1, col] = np.nan
    
    # 标记布林带指标无效区域
    bb_columns = ['BB_upper', 'BB_down', 'BB_middle']
    for col in bb_columns:
        if col in target_df.columns:
            target_df.loc[:bb_valid_start-1, col] = np.nan
    
    # 标记ADX指标无效区域
    adx_columns = ['ADX', '+DI', '-DI']
    for col in adx_columns:
        if col in target_df.columns:
            target_df.loc[:adx_valid_start-1, col] = np.nan
    
    if not inplace:
        return target_df
    
    return None


def print_validity_report(df: pd.DataFrame, ema_periods: list = [10, 20, 50], 
                         bb_period: int = 20, adx_period: int = 14):
    """打印指标有效性报告
    
    参数:
    df (pd.DataFrame): 包含技术指标的DataFrame
    ema_periods (list): EMA计算周期列表
    bb_period (int): 布林带计算周期
    adx_period (int): ADX计算周期
    """
    total_bars = len(df)
    max_ema_period = max(ema_periods)
    
    ema_valid_start = max_ema_period - 1
    bb_valid_start = bb_period - 1
    adx_valid_start = adx_period * 2 - 1
    
    ema_valid_count = max(0, total_bars - ema_valid_start)
    bb_valid_count = max(0, total_bars - bb_valid_start)
    adx_valid_count = max(0, total_bars - adx_valid_start)
    
    # print("=" * 50)
    # print("技术指标有效性报告")
    # print("=" * 50)
    # print(f"总K线数量: {total_bars}")
    # print()
    
    # print(f"EMA指标 (周期: {ema_periods}):")
    # print(f"  无效K线: 前{ema_valid_start}根")
    # print(f"  有效K线: {ema_valid_count}根")
    # print(f"  有效起始位置: 第{ema_valid_start + 1}根K线")
    # print()
    
    # print(f"布林带指标 (周期: {bb_period}):")
    # print(f"  无效K线: 前{bb_valid_start}根")
    # print(f"  有效K线: {bb_valid_count}根")
    # print(f"  有效起始位置: 第{bb_valid_start + 1}根K线")
    # print()
    
    # print(f"ADX指标 (周期: {adx_period}):")
    # print(f"  无效K线: 前{adx_valid_start}根")
    # print(f"  有效K线: {adx_valid_count}根")
    # print(f"  有效起始位置: 第{adx_valid_start + 1}根K线")
    # print("=" * 50)


def filter_valid_data(df: pd.DataFrame, indicator_type: str = 'all') -> pd.DataFrame:
    """过滤出有效的指标数据
    
    参数:
    df (pd.DataFrame): 包含技术指标的DataFrame
    indicator_type (str): 指标类型 ('ema', 'bb', 'adx', 'all')
    
    返回:
    pd.DataFrame: 过滤后的DataFrame
    """
    if indicator_type == 'ema':
        ema_cols = ['ema_kuai', 'ema_zhong', 'ema_man']
        existing_cols = [col for col in ema_cols if col in df.columns]
        mask = df[existing_cols].notna().all(axis=1)
        
    elif indicator_type == 'bb':
        bb_cols = ['BB_upper', 'BB_down']
        existing_cols = [col for col in bb_cols if col in df.columns]
        mask = df[existing_cols].notna().all(axis=1)
        
    elif indicator_type == 'adx':
        adx_cols = ['ADX', '+DI', '-DI']
        existing_cols = [col for col in adx_cols if col in df.columns]
        mask = df[existing_cols].notna().all(axis=1)
    
    elif indicator_type == 'di_enhanced':
        # DI增强指标（差值和比值）
        di_enhanced_cols = ['DI_Difference', 'DI_Ratio', 'DI_Direction', 'DI_Strength']
        existing_cols = [col for col in di_enhanced_cols if col in df.columns]
        mask = df[existing_cols].notna().all(axis=1)
        
    else:  # 'all'
        all_indicator_cols = ['ema_kuai', 'ema_zhong', 'ema_man', 
                     'BB_upper', 'BB_down', 'ADX', '+DI', '-DI',
                     'DI_Difference', 'DI_Ratio', 'DI_Direction', 'DI_Strength']
        existing_cols = [col for col in all_indicator_cols if col in df.columns]
        mask = df[existing_cols].notna().all(axis=1)
    
    return df[mask].copy()



def create_technical_charts(df, path_png=r"C:\Users\<USER>\Desktop\my_python\claudeV1\量化\可视化\png", 
                           filter_invalid=True):
    """创建技术指标可视化图表
    
    参数:
    df (pd.DataFrame): 包含价格和指标数据的DataFrame
    path_png (str): 图片保存路径
    filter_invalid (bool): 是否过滤无效数据
    """
    # 确保目录存在
    try:
        os.makedirs(path_png, exist_ok=True)
        print(f"图片将保存到: {path_png}")
    except OSError:
        print(f"错误：无法创建目录 {path_png}")
        return
    
    # 设置matplotlib为非交互模式
    plt.ioff()  # 关闭交互模式
    plt.rcParams['axes.unicode_minus'] = False
    
    # 如果需要过滤无效数据
    if filter_invalid:
        plot_df = filter_valid_data(df, indicator_type='all')
        print(f"过滤后有效数据: {len(plot_df)}行 (原始数据: {len(df)}行)")
    else:
        plot_df = df
    
    # 创建四个图表
    try:
        _create_ema_chart(plot_df, path_png)
        print("✓ EMA图表已保存")
        
        _create_bb_chart(plot_df, path_png)
        print("✓ 布林带图表已保存")
        
        _create_adx_chart(plot_df, path_png)
        print("✓ ADX图表已保存")
        
        _create_di_ratio_chart(plot_df, path_png)
        print("✓ DI比值图表已保存")
        
        _create_di_difference_chart(plot_df, path_png)
        print("✓ DI差值图表已保存")
        
        print("所有图表（5张）已成功保存到本地")
        
    except Exception as e:
        print(f"生成图表时出错: {e}")


def _create_ema_chart(df, path_png):
    """创建EMA指标图表"""
    fig, ax1 = plt.subplots(figsize=(12, 8))
    
    # 左轴：价格
    ax1.set_ylabel('Price', color='black')
    ax1.plot(df.index, df['close'], label='close', color='black', linewidth=2)
    
    # 检查EMA列是否存在
    if 'ema_man' in df.columns:
        ax1.plot(df.index, df['ema_man'], label='ema_man', color='blue', alpha=0.8)
    if 'ema_zhong' in df.columns:
        ax1.plot(df.index, df['ema_zhong'], label='ema_zhong', color='orange', alpha=0.8)
    if 'ema_kuai' in df.columns:
        ax1.plot(df.index, df['ema_kuai'], label='ema_kuai', color='red', alpha=0.8)
    
    ax1.tick_params(axis='y', labelcolor='black')
    ax1.grid(True, alpha=0.3)
    ax1.legend(loc='upper left')
    
    plt.title('EMA Indicator and Price Chart', fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    # 保存图片
    save_path = os.path.join(path_png, 'EMA_Chart.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()  # 关闭图形以释放内存


def _create_bb_chart(df, path_png):
    """创建布林带指标图表"""
    fig, ax1 = plt.subplots(figsize=(12, 8))
    
    # 左轴：价格
    ax1.set_ylabel('Price', color='black')
    ax1.plot(df.index, df['close'], label='close', color='black', linewidth=2)
    
    # 检查布林带列是否存在
    if 'BB_upper' in df.columns and 'BB_down' in df.columns:
        ax1.plot(df.index, df['BB_upper'], label='BB_upper', color='red', alpha=0.7)
        ax1.plot(df.index, df['BB_down'], label='BB_down', color='green', alpha=0.7)
        
        # 填充布林带区域
        ax1.fill_between(df.index, df['BB_upper'], df['BB_down'], 
                         alpha=0.2, color='gray', label='BB_zone')
    
    ax1.tick_params(axis='y', labelcolor='black')
    ax1.grid(True, alpha=0.3)
    ax1.legend(loc='upper left')
    
    plt.title('Bollinger Bands and Price Chart', fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    # 保存图片
    save_path = os.path.join(path_png, 'BB_Chart.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()  # 关闭图形以释放内存


def _create_adx_chart(df, path_png):
    """创建ADX指标图表（仅显示ADX，不显示+DI和-DI）"""
    fig, ax1 = plt.subplots(figsize=(12, 8))
    
    # 左轴：价格
    ax1.set_ylabel('Price', color='black')
    ax1.plot(df.index, df['close'], label='close', color='black', linewidth=2)
    ax1.tick_params(axis='y', labelcolor='black')
    ax1.grid(True, alpha=0.3)
    
    # 右轴：仅显示ADX指标
    ax2 = ax1.twinx()
    ax2.set_ylabel('ADX Values', color='blue')
    
    # 只检查和绘制ADX
    if 'ADX' in df.columns:
        ax2.plot(df.index, df['ADX'], label='ADX', color='blue', linewidth=2)
    
    ax2.tick_params(axis='y', labelcolor='blue')
    
    # 添加ADX参考线
    ax2.axhline(y=25, color='gray', linestyle='--', alpha=0.5, label='Strong Trend(25)')
    ax2.axhline(y=50, color='gray', linestyle='--', alpha=0.5, label='Very Strong(50)')
    
    # 合并图例
    lines1, labels1 = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
    
    plt.title('ADX Indicator and Price Chart', fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    # 保存图片
    save_path = os.path.join(path_png, 'ADX_Chart.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()  # 关闭图形以释放内存

def _create_di_ratio_chart(df, path_png):
    """创建DI比值指标图表（改进版：使用颜色区分方向）
    
    参数:
    df (pd.DataFrame): 包含价格和指标数据的DataFrame
    path_png (str): 图片保存路径
    """
    fig, ax1 = plt.subplots(figsize=(12, 8))
    
    # 左轴：价格
    ax1.set_ylabel('Price', color='black')
    ax1.plot(df.index, df['close'], label='close', color='black', linewidth=2)
    ax1.tick_params(axis='y', labelcolor='black')
    ax1.grid(True, alpha=0.3)
    
    # 右轴：DI比值
    ax2 = ax1.twinx()
    ax2.set_ylabel('DI Ratio', color='purple')
    
    # 检查DI列是否存在并计算比值
    if '+DI' in df.columns and '-DI' in df.columns:
        plus_di = df['+DI']
        minus_di = df['-DI']
        
        # 计算比值和方向
        bullish_ratios = []  # 看涨比值（+DI > -DI时）
        bearish_ratios = []  # 看跌比值（-DI > +DI时）
        bullish_indices = []
        bearish_indices = []
        
        for i in range(len(plus_di)):
            if plus_di.iloc[i] >= minus_di.iloc[i]:
                # 看涨：+DI >= -DI，计算+DI/-DI
                if minus_di.iloc[i] < 0.1:
                    ratio = min(5.0, plus_di.iloc[i] / 0.1)
                else:
                    ratio = min(5.0, plus_di.iloc[i] / minus_di.iloc[i])
                bullish_ratios.append(ratio)
                bullish_indices.append(df.index[i])
                bearish_ratios.append(None)
                bearish_indices.append(df.index[i])
            else:
                # 看跌：-DI > +DI，计算-DI/+DI
                if plus_di.iloc[i] < 0.1:
                    ratio = min(5.0, minus_di.iloc[i] / 0.1)
                else:
                    ratio = min(5.0, minus_di.iloc[i] / plus_di.iloc[i])
                bearish_ratios.append(ratio)
                bearish_indices.append(df.index[i])
                bullish_ratios.append(None)
                bullish_indices.append(df.index[i])
        
        # 绘制不同颜色的比值线
        ax2.plot(bullish_indices, bullish_ratios, label='+DI/-DI (Bullish)', 
                color='green', linewidth=2, alpha=0.8)
        ax2.plot(bearish_indices, bearish_ratios, label='-DI/+DI (Bearish)', 
                color='red', linewidth=2, alpha=0.8)
        
        # 添加参考线
        ax2.axhline(y=1.0, color='gray', linestyle='-', alpha=0.7, label='Neutral(1.0)')
        ax2.axhline(y=1.5, color='orange', linestyle='--', alpha=0.5, label='Moderate(1.5)')
        ax2.axhline(y=2.0, color='blue', linestyle='--', alpha=0.5, label='Strong(2.0)')
        ax2.axhline(y=3.0, color='purple', linestyle='--', alpha=0.5, label='Very Strong(3.0)')
        
        # 设置Y轴范围
        ax2.set_ylim(0, 5)
    
    ax2.tick_params(axis='y', labelcolor='purple')
    
    # 合并图例
    lines1, labels1 = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
    
    plt.title('DI Ratio Chart with Direction Colors', fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    # 保存图片
    save_path = os.path.join(path_png, 'DI_Ratio_Chart.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()


def _create_di_difference_chart(df, path_png):
    """创建DI差值指标图表
    
    参数:
    df (pd.DataFrame): 包含价格和指标数据的DataFrame
    path_png (str): 图片保存路径
    """
    fig, ax1 = plt.subplots(figsize=(12, 8))
    
    # 左轴：价格
    ax1.set_ylabel('Price', color='black')
    ax1.plot(df.index, df['close'], label='close', color='black', linewidth=2)
    ax1.tick_params(axis='y', labelcolor='black')
    ax1.grid(True, alpha=0.3)
    
    # 右轴：DI差值
    ax2 = ax1.twinx()
    ax2.set_ylabel('DI Difference', color='blue')
    
    # 检查DI列是否存在并计算差值
    if '+DI' in df.columns and '-DI' in df.columns:
        # 计算+DI - (-DI)的差值
        di_difference = df['+DI'] - df['-DI']
        
        # 根据差值正负使用不同颜色
        positive_diff = di_difference.where(di_difference >= 0)
        negative_diff = di_difference.where(di_difference < 0)
        
        # 绘制差值线
        ax2.plot(df.index, positive_diff, label='+DI > -DI (Bullish)', 
                color='green', linewidth=2, alpha=0.8)
        ax2.plot(df.index, negative_diff, label='+DI < -DI (Bearish)', 
                color='red', linewidth=2, alpha=0.8)
        
        # 填充区域
        ax2.fill_between(df.index, 0, positive_diff, where=(positive_diff >= 0), 
                        color='green', alpha=0.2, interpolate=True)
        ax2.fill_between(df.index, 0, negative_diff, where=(negative_diff < 0), 
                        color='red', alpha=0.2, interpolate=True)
        
        # 添加参考线
        ax2.axhline(y=0, color='gray', linestyle='-', alpha=0.7, label='Neutral(0)')
        ax2.axhline(y=5, color='green', linestyle='--', alpha=0.5, label='Strong Bull(+5)')
        ax2.axhline(y=-5, color='red', linestyle='--', alpha=0.5, label='Strong Bear(-5)')
        ax2.axhline(y=10, color='darkgreen', linestyle='--', alpha=0.5, label='Very Strong Bull(+10)')
        ax2.axhline(y=-10, color='darkred', linestyle='--', alpha=0.5, label='Very Strong Bear(-10)')
    
    ax2.tick_params(axis='y', labelcolor='blue')
    
    # 合并图例
    lines1, labels1 = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
    
    plt.title('DI Difference Chart (+DI minus -DI)', fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    # 保存图片
    save_path = os.path.join(path_png, 'DI_Difference_Chart.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

def calculate_di_enhanced_indicators(df: pd.DataFrame, inplace: bool = True) -> Optional[pd.DataFrame]:
    # 数据验证
    if df.empty:
        raise ValueError("DataFrame不能为空")
    
    required_columns = ['+DI', '-DI']
    for col in required_columns:
        if col not in df.columns:
            raise ValueError(f"列 '{col}' 不存在于DataFrame中")
    
    # 选择目标DataFrame
    target_df = df if inplace else df.copy()
    
    # 获取DI数据
    plus_di = target_df['+DI']
    minus_di = target_df['-DI']
    
    # 计算DI差值
    target_df['DI_Difference'] = plus_di - minus_di
    
    # 计算智能DI比值和方向
    di_ratio = []
    di_direction = []
    
    for i in range(len(plus_di)):
        if plus_di.iloc[i] >= minus_di.iloc[i]:
            # 看涨：+DI >= -DI
            direction = 1
            if minus_di.iloc[i] < 0.1:
                ratio = min(5.0, plus_di.iloc[i] / 0.1)
            else:
                ratio = min(5.0, plus_di.iloc[i] / minus_di.iloc[i])
        else:
            # 看跌：-DI > +DI
            direction = -1
            if plus_di.iloc[i] < 0.1:
                ratio = min(5.0, minus_di.iloc[i] / 0.1)
            else:
                ratio = min(5.0, minus_di.iloc[i] / plus_di.iloc[i])
        
        di_ratio.append(ratio)
        di_direction.append(direction)
    
    target_df['DI_Ratio'] = di_ratio
    target_df['DI_Direction'] = di_direction
    
    # 计算趋势强度分类
    def classify_strength(ratio, difference):
        """分类趋势强度"""
        abs_diff = abs(difference)
        
        if ratio >= 3.0 or abs_diff >= 15:
            return "极强"
        elif ratio >= 2.0 or abs_diff >= 10:
            return "强"
        elif ratio >= 1.5 or abs_diff >= 5:
            return "中等"
        elif ratio >= 1.2 or abs_diff >= 2:
            return "弱"
        else:
            return "震荡"
    
    target_df['DI_Strength'] = [
        classify_strength(ratio, diff) 
        for ratio, diff in zip(target_df['DI_Ratio'], target_df['DI_Difference'])
    ]
    
    if not inplace:
        return target_df
    
    return None
def process_technical_indicators(df, 
                                 ema_periods=[10, 20, 50],
                                 ema_names=['ema_kuai', 'ema_zhong', 'ema_man'],
                                 bb_period=20,
                                 bb_std_dev=2,
                                 adx_period=14,
                                 price_columns=None,
                                 filter_type='all',
                                 show_progress=True,
                                 show_samples=True):
    """
    综合处理技术指标：计算EMA、布林带、ADX和DI增强指标，返回有效数据
    
    参数:
    df (pd.DataFrame): 包含OHLC数据的DataFrame
    ema_periods (list): EMA周期列表，默认[10, 20, 50]
    ema_names (list): EMA列名列表，默认['ema_kuai', 'ema_zhong', 'ema_man']
    bb_period (int): 布林带周期，默认20
    bb_std_dev (float): 布林带标准差倍数，默认2
    adx_period (int): ADX周期，默认14
    price_columns (dict): 价格列名映射，默认None（使用标准列名）
    filter_type (str): 过滤类型，默认'all'（可选：'ema', 'bb', 'adx', 'di_enhanced', 'all'）
    show_progress (bool): 是否显示处理进度，默认True
    show_samples (bool): 是否显示数据样本，默认True
    
    返回:
    pd.DataFrame: 包含所有技术指标的有效数据DataFrame
    
    异常:
    ValueError: 当输入数据无效时抛出异常
    """
    # 数据验证
    if df.empty:
        raise ValueError("输入DataFrame不能为空")
    
    # 必需的列检查
    required_columns = ['open', 'high', 'low', 'close']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"缺少必需的列: {missing_columns}")
    
    # 检查EMA参数
    if len(ema_periods) != len(ema_names):
        raise ValueError("ema_periods和ema_names的长度必须相等")
    
    if show_progress:
        print("原始数据:")
        print(df.iloc[:,:6].tail())
        
        print("\n开始计算技术指标")
        print("-" * 30)
    
    try:
        # 1. 计算EMA指标
        if show_progress:
            print("正在计算EMA指标...")
        calculate_ema(df, periods=ema_periods, names=ema_names)
        
        # 2. 计算布林带指标
        if show_progress:
            print("正在计算布林带指标...")
        calculate_bollinger_bands(df, period=bb_period, std_dev=bb_std_dev)
        
        # 3. 计算ADX指标
        if show_progress:
            print("正在计算ADX指标...")
        calculate_adx(df, period=adx_period, price_columns=price_columns)
        
        if show_progress:
            print("基础技术指标计算完成")
            
        # 显示基础指标结果
        if show_samples:
            basic_cols = ['close'] + ema_names + ['BB_upper', 'BB_down', 'ADX', '+DI', '-DI']
            existing_basic_cols = [col for col in basic_cols if col in df.columns]
            print("\n基础指标数据样本:")
            print(df[existing_basic_cols].tail())
        
        # 4. 计算DI增强指标
        if show_progress:
            print("\n正在计算DI增强指标...")
            
        if '+DI' in df.columns and '-DI' in df.columns:
            calculate_di_enhanced_indicators(df, inplace=True)
            if show_progress:
                print("DI增强指标计算完成")
                
            # 显示增强指标结果
            if show_samples:
                enhanced_cols = ['close', '+DI', '-DI', 'DI_Difference', 'DI_Ratio', 'DI_Direction', 'DI_Strength']
                existing_enhanced_cols = [col for col in enhanced_cols if col in df.columns]
                print("\n增强指标数据样本:")
                print(df[existing_enhanced_cols].tail())
        else:
            if show_progress:
                print("警告：缺少+DI或-DI数据，跳过DI增强指标计算")
        
        # 5. 指标有效性分析
        if show_progress:
            # print("\n指标有效性分析")
            # print("-" * 30)
            print_validity_report(df, ema_periods=ema_periods, bb_period=bb_period, adx_period=adx_period)
        
        # 6. 标记无效数据
        # if show_progress:
        #     print("\n标记无效数据")
        #     print("-" * 30)
        mark_invalid_indicators(df, ema_periods=ema_periods, bb_period=bb_period, adx_period=adx_period)
        
        # 7. 获取有效数据
        valid_df = filter_valid_data(df, indicator_type=filter_type)
        
        if show_progress:
            print("\n最终数据统计")
            # print("-" * 30)
            print(f"原始数据行数: {len(df)}")
            print(f"有效数据行数: {len(valid_df)}")
            # print(f"数据有效率: {len(valid_df)/len(df)*100:.1f}%")
        
        # 8. 显示最终有效数据样本
        if show_samples:
            final_cols = ['close', 'ADX', '+DI', '-DI', 'DI_Difference', 'DI_Ratio', 'DI_Direction', 'DI_Strength']
            existing_final_cols = [col for col in final_cols if col in valid_df.columns]
            # if show_progress:
            #     print(f"\n最终有效数据样本（包含DI增强指标）:")
            # print(valid_df[existing_final_cols].tail())
        
        if show_progress:
            print("\n✓ 技术指标处理完成")
            
        return valid_df
        
    except Exception as e:
        print(f"计算技术指标时出错: {e}")
        raise

class FastBacktestMarker:
    """高性能回测标记器"""
    
    def __init__(self, ema_periods=[10, 20, 50], ema_names=['ema_kuai', 'ema_zhong', 'ema_man'],
                 bb_period=20, bb_std_dev=2, adx_period=14, lookback_window=100):
        """初始化回测标记器
        
        参数:
        ema_periods (list): EMA周期列表
        ema_names (list): EMA列名列表
        bb_period (int): 布林带周期
        bb_std_dev (float): 布林带标准差倍数
        adx_period (int): ADX周期
        lookback_window (int): 统计分析回望窗口
        """
        self.ema_periods = ema_periods
        self.ema_names = ema_names
        self.bb_period = bb_period
        self.bb_std_dev = bb_std_dev
        self.adx_period = adx_period
        self.lookback_window = lookback_window
        
        # 初始化滑动窗口数据结构
        self.adx_window = deque(maxlen=lookback_window)
        self.di_diff_window = deque(maxlen=lookback_window)
        self.di_ratio_window = deque(maxlen=lookback_window)
        
        # 缓存统计阈值（每N根K线更新一次）
        self.update_interval = 20  # 每20根K线更新一次统计阈值
        self.cached_thresholds = {}
        self.last_update_bar = 0
        
    def run_backtest(self, df: pd.DataFrame, start_delay=None) -> pd.DataFrame:
        """执行高性能回测
        
        参数:
        df (pd.DataFrame): 原始OHLC数据
        start_delay (int): 延后起始点
        
        返回:
        pd.DataFrame: 包含指标和信号的结果
        """
        # 数据验证
        if df.empty:
            raise ValueError("输入DataFrame不能为空")
        
        # 计算起始延迟
        if start_delay is None:
            max_ema_period = max(self.ema_periods)
            min_required = max(max_ema_period, self.bb_period, self.adx_period * 2)
            start_delay = min_required + self.lookback_window
        
        total_length = len(df)
        if total_length < start_delay + 10:
            raise ValueError(f"数据长度不足：需要至少{start_delay + 10}行，当前{total_length}行")
        
        print(f"开始高性能回测：总长度{total_length}，延后起始{start_delay}")
        
        # 复制数据并初始化列
        result_df = df.copy()
        self._initialize_columns(result_df)
        
        # 批量计算基础指标（使用向量化操作）
        self._batch_calculate_indicators(result_df)
        
        # 标记信号
        signal_count = self._mark_signals_fast(result_df, start_delay)
        
        print(f"高性能回测完成：共标记{signal_count}个信号点")
        return result_df
    
    def _initialize_columns(self, df: pd.DataFrame):
        """初始化所有列"""
        # 信号列
        df['signal_adx_low'] = False
        df['signal_adx_high'] = False
        df['signal_di_diff_bearish'] = False
        df['signal_di_diff_bullish'] = False
        df['signal_di_ratio_strong'] = False
        df['signal_combined'] = False
    
    def _batch_calculate_indicators(self, df: pd.DataFrame):
        """批量计算所有技术指标（使用向量化操作）"""
        # 1. 计算EMA（向量化）
        for i, period in enumerate(self.ema_periods):
            df[self.ema_names[i]] = df['close'].ewm(span=period, adjust=False).mean()
        
        # 2. 计算布林带（向量化）
        rolling = df['close'].rolling(window=self.bb_period)
        df['BB_middle'] = rolling.mean()
        bb_std = rolling.std()
        df['BB_upper'] = df['BB_middle'] + self.bb_std_dev * bb_std
        df['BB_down'] = df['BB_middle'] - self.bb_std_dev * bb_std
        
        # 3. 计算ADX和DI（向量化）
        self._calculate_adx_vectorized(df)
        
        # 4. 计算DI增强指标（向量化）
        self._calculate_di_enhanced_vectorized(df)
    
    def _calculate_adx_vectorized(self, df: pd.DataFrame):
        """向量化计算ADX指标"""
        # 计算True Range
        high_low = df['high'] - df['low']
        high_close = (df['high'] - df['close'].shift(1)).abs()
        low_close = (df['low'] - df['close'].shift(1)).abs()
        
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        df['TR'] = ranges.max(axis=1)
        
        # 计算方向移动
        df['H-PH'] = df['high'] - df['high'].shift(1)
        df['PL-L'] = df['low'].shift(1) - df['low']
        
        # 计算+DM和-DM
        df['+DM'] = np.where(
            (df['H-PH'] > df['PL-L']) & (df['H-PH'] > 0),
            df['H-PH'], 0
        )
        df['-DM'] = np.where(
            (df['PL-L'] > df['H-PH']) & (df['PL-L'] > 0),
            df['PL-L'], 0
        )
        
        # 平滑处理
        df['TR_smooth'] = df['TR'].ewm(span=self.adx_period, adjust=False).mean()
        df['+DM_smooth'] = df['+DM'].ewm(span=self.adx_period, adjust=False).mean()
        df['-DM_smooth'] = df['-DM'].ewm(span=self.adx_period, adjust=False).mean()
        
        # 计算DI
        df['+DI'] = 100 * (df['+DM_smooth'] / df['TR_smooth'])
        df['-DI'] = 100 * (df['-DM_smooth'] / df['TR_smooth'])
        
        # 计算DX和ADX
        df['DX'] = 100 * (df['+DI'] - df['-DI']).abs() / (df['+DI'] + df['-DI'])
        df['ADX'] = df['DX'].ewm(span=self.adx_period, adjust=False).mean()
        
        # 清理临时列
        temp_cols = ['TR', 'H-PH', 'PL-L', '+DM', '-DM', 'TR_smooth', '+DM_smooth', '-DM_smooth', 'DX']
        df.drop(columns=temp_cols, inplace=True)
    
    def _calculate_di_enhanced_vectorized(self, df: pd.DataFrame):
        """向量化计算DI增强指标"""
        # DI差值
        df['DI_Difference'] = df['+DI'] - df['-DI']
        
        # DI比值（向量化处理）
        bullish_mask = df['+DI'] >= df['-DI']
        bearish_mask = ~bullish_mask
        
        # 处理分母接近0的情况
        safe_plus_di = df['+DI'].clip(lower=0.1)
        safe_minus_di = df['-DI'].clip(lower=0.1)
        
        # 计算比值
        df['DI_Ratio'] = np.where(
            bullish_mask,
            (df['+DI'] / safe_minus_di).clip(upper=5.0),
            (df['-DI'] / safe_plus_di).clip(upper=5.0)
        )
        
        # 方向
        df['DI_Direction'] = np.where(bullish_mask, 1, -1)
    
    def _mark_signals_fast(self, df: pd.DataFrame, start_delay: int) -> int:
        """快速标记信号（使用缓存的统计阈值）"""
        signal_count = 0
        total_length = len(df)
        
        # 预计算所有需要的数据
        adx_values = df['ADX'].values
        di_diff_values = df['DI_Difference'].values
        di_ratio_values = df['DI_Ratio'].values
        
        # 使用numpy数组存储信号
        signal_adx_low = np.zeros(total_length, dtype=bool)
        signal_adx_high = np.zeros(total_length, dtype=bool)
        signal_di_diff_bearish = np.zeros(total_length, dtype=bool)
        signal_di_diff_bullish = np.zeros(total_length, dtype=bool)
        signal_di_ratio_strong = np.zeros(total_length, dtype=bool)
        
        for current_bar in range(start_delay, total_length):
            # 获取当前值
            current_adx = adx_values[current_bar]
            current_di_diff = di_diff_values[current_bar]
            current_di_ratio = di_ratio_values[current_bar]
            
            # 跳过无效值
            if np.isnan(current_adx) or np.isnan(current_di_diff) or np.isnan(current_di_ratio):
                continue
            
            # 更新滑动窗口
            self.adx_window.append(current_adx)
            self.di_diff_window.append(current_di_diff)
            self.di_ratio_window.append(current_di_ratio)
            
            # 确保有足够数据
            if len(self.adx_window) < 20:
                continue
            
            # 定期更新统计阈值
            if current_bar - self.last_update_bar >= self.update_interval:
                self._update_thresholds()
                self.last_update_bar = current_bar
            
            # 使用缓存的阈值进行信号判断
            if self.cached_thresholds:
                # ADX信号
                if current_adx < self.cached_thresholds['adx_lower']:
                    signal_adx_low[current_bar] = True
                    signal_count += 1
                elif current_adx > self.cached_thresholds['adx_upper']:
                    signal_adx_high[current_bar] = True
                    signal_count += 1
                
                # DI差值信号
                if current_di_diff < self.cached_thresholds['di_diff_lower']:
                    signal_di_diff_bearish[current_bar] = True
                    signal_count += 1
                elif current_di_diff > self.cached_thresholds['di_diff_upper']:
                    signal_di_diff_bullish[current_bar] = True
                    signal_count += 1
                
                # DI比值信号
                if current_di_ratio > self.cached_thresholds['di_ratio_upper']:
                    signal_di_ratio_strong[current_bar] = True
                    signal_count += 1
            
            # 进度报告
            if current_bar % 500 == 0:
                print(f"处理进度: {current_bar}/{total_length}, 信号数: {signal_count}")
        
        # 批量更新DataFrame
        df['signal_adx_low'] = signal_adx_low
        df['signal_adx_high'] = signal_adx_high
        df['signal_di_diff_bearish'] = signal_di_diff_bearish
        df['signal_di_diff_bullish'] = signal_di_diff_bullish
        df['signal_di_ratio_strong'] = signal_di_ratio_strong
        
        # 综合信号
        df['signal_combined'] = (df['signal_adx_low'] | df['signal_adx_high'] | 
                                df['signal_di_diff_bearish'] | df['signal_di_diff_bullish'] | 
                                df['signal_di_ratio_strong'])
        
        return signal_count
    
    def _update_thresholds(self):
        """更新缓存的统计阈值"""
        # 转换为numpy数组进行快速计算
        adx_array = np.array(self.adx_window)
        di_diff_array = np.array(self.di_diff_window)
        di_ratio_array = np.array(self.di_ratio_window)
        
        # 快速计算均值和标准差
        adx_mean = np.mean(adx_array)
        adx_std = np.std(adx_array)
        
        di_diff_mean = np.mean(di_diff_array)
        di_diff_std = np.std(di_diff_array)
        
        di_ratio_mean = np.mean(di_ratio_array)
        di_ratio_std = np.std(di_ratio_array)
        
        # 更新缓存的2σ阈值
        self.cached_thresholds = {
            'adx_lower': adx_mean - 2 * adx_std,
            'adx_upper': adx_mean + 2 * adx_std,
            'di_diff_lower': di_diff_mean - 2 * di_diff_std,
            'di_diff_upper': di_diff_mean + 2 * di_diff_std,
            'di_ratio_upper': di_ratio_mean + 2 * di_ratio_std
        }


def realtime_backtest_marker(df: pd.DataFrame, 
                                 ema_periods=[10, 20, 50],
                                 ema_names=['ema_kuai', 'ema_zhong', 'ema_man'],
                                 bb_period=20,
                                 bb_std_dev=2,
                                 adx_period=14,
                                 lookback_window=100,
                                 start_delay=None) -> pd.DataFrame:
    """高性能实时回测标记函数（使用类封装）
    
    参数:
    df (pd.DataFrame): 原始OHLC数据
    其他参数同原函数
    
    返回:
    pd.DataFrame: 包含技术指标和信号标记的完整DataFrame
    """
    # 创建回测标记器实例
    marker = FastBacktestMarker(
        ema_periods=ema_periods,
        ema_names=ema_names,
        bb_period=bb_period,
        bb_std_dev=bb_std_dev,
        adx_period=adx_period,
        lookback_window=lookback_window
    )
    
    # 执行回测
    return marker.run_backtest(df, start_delay)
        
def analyze_backtest_results(marked_df: pd.DataFrame) -> Dict:
    """
    分析回测标记结果
    
    参数:
    marked_df (pd.DataFrame): 包含信号标记的DataFrame
    
    返回:
    Dict: 分析结果统计
    """
    # 统计各类信号数量
    signal_columns = [col for col in marked_df.columns if col.startswith('signal_')]
    
    results = {}
    for col in signal_columns:
        signal_count = marked_df[col].sum()
        signal_rate = signal_count / len(marked_df) * 100
        results[col] = {
            'count': int(signal_count),
            'rate': round(signal_rate, 2)
        }
    
    # 找出信号发生的时间点
    signal_indices = marked_df[marked_df['signal_combined'] == True].index.tolist()
    
    results['signal_summary'] = {
        'total_bars': len(marked_df),
        'signal_bars': len(signal_indices),
        'signal_rate': round(len(signal_indices) / len(marked_df) * 100, 2),
        'signal_indices': signal_indices[:10]  # 只显示前10个信号点
    }
    
    return results
def print_backtest_summary(marked_df: pd.DataFrame):
    """
    打印回测结果摘要
    
    参数:
    marked_df (pd.DataFrame): 包含信号标记的DataFrame
    """
    results = analyze_backtest_results(marked_df)
    
    print("\n=== 回测信号标记结果摘要 ===")
    print(f"总K线数量: {results['signal_summary']['total_bars']}")
    print(f"信号K线数量: {results['signal_summary']['signal_bars']}")
    print(f"信号触发率: {results['signal_summary']['signal_rate']}%")
    
    print("\n--- 各指标信号统计 ---")
    for signal_type, stats in results.items():
        if signal_type != 'signal_summary':
            print(f"{signal_type}: {stats['count']}次 ({stats['rate']}%)")
    
    # 显示最近的几个信号样本
    recent_signals = marked_df[marked_df['signal_combined'] == True].tail(5)
    if not recent_signals.empty:
        print("\n--- 最近5个信号点样本 ---")
        display_cols = ['ADX', 'DI_Difference', 'DI_Ratio', 'signal_combined']
        existing_cols = [col for col in display_cols if col in recent_signals.columns]
        print(recent_signals[existing_cols])
   
   
def plot_kline_with_signals(df, signal_display=[1, 1, 1], save_path=None, figsize=(15, 10)):
    """绘制K线图并标记信号点
    
    参数:
    df (pd.DataFrame): 包含OHLC数据和信号标记的DataFrame
    signal_display (list): 控制显示的信号类型 [adx, di_diff, di_ratio]
                           1表示显示，0表示不显示，默认[1,1,1]全部显示
    save_path (str): 图片保存路径，默认None不保存
    figsize (tuple): 图片尺寸，默认(15, 10)
    """
    try:
        # 数据验证
        required_columns = ['open_time', 'open', 'high', 'low', 'close']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"缺少必需的列: {missing_columns}")
        
        # 验证signal_display参数
        if len(signal_display) != 3:
            raise ValueError("signal_display必须包含3个元素：[adx, di_diff, di_ratio]")
        
        # 检查是否有信号列
        signal_columns = [col for col in df.columns if col.startswith('signal_')]
        if not signal_columns:
            print("警告：未发现信号列，将只绘制K线图")
        
        # 设置matplotlib参数
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        plt.ioff()  # 关闭交互模式
        
        # 创建图形和子图
        fig, ax = plt.subplots(figsize=figsize)
        
        # 绘制K线图
        _draw_candlestick(ax, df)
        
        # 绘制信号标记
        if signal_columns:
            _draw_signal_markers(ax, df, signal_display)
        
        # 设置图表格式
        _format_chart(ax, df)
        
        # 保存图片
        if save_path:
            _save_chart(fig, save_path)
        
        # 显示图表
        plt.tight_layout()
        plt.show()
        
        print("K线图绘制完成")
        print(f"信号显示设置: ADX={bool(signal_display[0])}, DI差值={bool(signal_display[1])}, DI比值={bool(signal_display[2])}")
        
    except Exception as e:
        print(f"绘制K线图时出错: {e}")
        plt.close('all')

def _draw_candlestick(ax, df):
    """绘制K线图
    
    参数:
    ax: matplotlib轴对象
    df: 数据DataFrame
    """
    # 转换时间格式
    if isinstance(df['open_time'].iloc[0], str):
        times = pd.to_datetime(df['open_time'])
    else:
        times = df['open_time']
    
    # 绘制K线
    for i in range(len(df)):
        open_price = df['open'].iloc[i]
        high_price = df['high'].iloc[i]
        low_price = df['low'].iloc[i]
        close_price = df['close'].iloc[i]
        time = times.iloc[i]
        
        # 确定K线颜色
        color = 'red' if close_price >= open_price else 'green'
        
        # 绘制高低线
        ax.plot([time, time], [low_price, high_price], color='black', linewidth=0.8)
        
        # 绘制实体
        body_height = abs(close_price - open_price)
        body_bottom = min(open_price, close_price)
        
        if body_height > 0:
            # 有实体的K线
            ax.bar(time, body_height, bottom=body_bottom, 
                  color=color, alpha=0.7, width=pd.Timedelta(minutes=10))
        else:
            # 十字星
            ax.plot([time, time], [open_price, close_price], color=color, linewidth=2)

def _draw_signal_markers(ax, df, signal_display):
    """绘制信号标记点
    
    参数:
    ax: matplotlib轴对象
    df: 数据DataFrame
    signal_display: 控制显示的信号类型列表
    """
    # 转换时间格式
    if isinstance(df['open_time'].iloc[0], str):
        times = pd.to_datetime(df['open_time'])
    else:
        times = df['open_time']
    
    # 定义信号类型和对应的标记样式
    signal_configs = {
        'adx': {
            'columns': ['signal_adx_low', 'signal_adx_high'],
            'marker': '^',  # 三角形
            'colors': ['green', 'red'],  # 低=绿色（震荡），高=红色（强趋势）
            'sizes': [80, 100],
            'labels': ['ADX异常低（震荡）', 'ADX异常高（强趋势）'],
            'enabled': signal_display[0]  # 对应signal_display第0个元素
        },
        'di_diff': {
            'columns': ['signal_di_diff_bearish', 'signal_di_diff_bullish'],
            'marker': 's',  # 正方形
            'colors': ['red', 'green'],  # 空头=红色，多头=绿色
            'sizes': [60, 80],
            'labels': ['DI差值异常（空头强势）', 'DI差值异常（多头强势）'],
            'enabled': signal_display[1]  # 对应signal_display第1个元素
        },
        'di_ratio': {
            'columns': ['signal_di_ratio_strong'],
            'marker': 'o',  # 圆形
            'colors': ['red'],  # 单边强势=红色
            'sizes': [70],
            'labels': ['DI比值异常（单边强势）'],
            'enabled': signal_display[2]  # 对应signal_display第2个元素
        }
    }
    
    # 绘制各类信号标记（仅绘制启用的信号类型）
    for signal_type, config in signal_configs.items():
        # 检查是否启用该信号类型
        if not config['enabled']:
            continue
            
        for i, col in enumerate(config['columns']):
            if col in df.columns:
                # 获取信号点
                signal_mask = df[col] == True
                if signal_mask.any():
                    signal_times = times[signal_mask]
                    signal_highs = df.loc[signal_mask, 'high']
                    
                    # 标记点位置稍微高于K线最高点
                    marker_y = signal_highs * 1.002
                    
                    # 绘制标记
                    ax.scatter(signal_times, marker_y,
                             marker=config['marker'],
                             color=config['colors'][i],
                             s=config['sizes'][i],
                             alpha=0.8,
                             label=config['labels'][i],
                             zorder=5)

def _format_chart(ax, df):
    """设置图表格式
    
    参数:
    ax: matplotlib轴对象
    df: 数据DataFrame
    """
    # 设置标题和标签
    ax.set_title('K线图与信号标记', fontsize=16, fontweight='bold', pad=20)
    ax.set_xlabel('时间', fontsize=12)
    ax.set_ylabel('价格', fontsize=12)
    
    # 设置时间轴格式
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
    ax.xaxis.set_major_locator(mdates.HourLocator(interval=24))
    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
    
    # 设置Y轴格式 - 精确到3位小数
    def price_formatter(x, pos):
        """Y轴价格格式化函数，保留3位小数"""
        return f'{x:.3f}'
    
    ax.yaxis.set_major_formatter(FuncFormatter(price_formatter))
    
    # 设置网格
    ax.grid(True, alpha=0.3, linestyle='--')
    
    # 设置图例
    legend = ax.legend(loc='upper left', fontsize=10, framealpha=0.9)
    if legend:
        legend.set_zorder(10)
    
    # 自动调整Y轴范围
    y_min = df['low'].min() * 0.998
    y_max = df['high'].max() * 1.005
    ax.set_ylim(y_min, y_max)

def _save_chart(fig, save_path):
    """保存图表
    
    参数:
    fig: matplotlib图形对象
    save_path: 保存路径
    """
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        
        # 保存图片
        fig.savefig(save_path, dpi=300, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        print(f"图表已保存到: {save_path}")
        
    except Exception as e:
        print(f"保存图表时出错: {e}")

def analyze_signal_distribution(df):
    """分析信号分布情况
    
    参数:
    df (pd.DataFrame): 包含信号标记的DataFrame
    """
    try:
        print("\n=== 信号分布分析 ===")
        
        # 统计各类信号数量
        signal_columns = [col for col in df.columns if col.startswith('signal_')]
        
        if not signal_columns:
            print("未发现信号列")
            return
        
        total_bars = len(df)
        print(f"总K线数量: {total_bars}")
        
        for col in signal_columns:
            if col in df.columns:
                count = df[col].sum()
                rate = count / total_bars * 100
                print(f"{col}: {count}个信号 ({rate:.2f}%)")
        
        # 分析信号密集区域
        if 'signal_combined' in df.columns:
            combined_signals = df[df['signal_combined'] == True]
            if not combined_signals.empty:
                print(f"\n综合信号总数: {len(combined_signals)}")
                print("最近5个信号时间:")
                for idx in combined_signals.tail(5).index:
                    time_str = df.iloc[idx]['open_time']
                    price = df.iloc[idx]['close']
                    print(f"  {time_str}: 价格 {price:.5f}")
        
    except Exception as e:
        print(f"分析信号分布时出错: {e}")


        
def main():
    print("=== OKX交易所SDK测试 ===\n")
    try:
        dotenv_path = r'C:\Users\<USER>\Desktop\my_python\claudeV1\量化\123.env'
        client = OKXClient(dotenv_path=dotenv_path)
    except Exception as e:
        print(f"初始化失败: {e}")
        return

    symbol = 'DOGE-USDT-SWAP'
    timeframe = '15m'
    counts = 500
    ####实时数据回测
    print("\n1. 测试获取最新K线数据")
    print("-" * 30)
    df = get_kline_data(client, symbol, timeframe=timeframe, count=counts)
    df = df.iloc[:-1]
    
    # ####历史数据回测
   
    # Uname='doge'
    # Ktime='15m'
    # strTime='2024-9-1'
    # endTime='2024-9-3'
    # # print(strTime)
    # try:
    #     # df = get_local_kline_data('doge', '15m', '2024-12-30 10:00:00','2025-2-1')
    #     df = get_local_kline_data(Uname, Ktime, strTime, endTime)
    #     print(df.head())
    # except Exception as e:
    #     print(e)

    
    if not df.empty:
        print("原始数据:")
        print(df.iloc[:,:6].tail())
        
        print("\n2. 真实模拟回测标记")
        print("-" * 30)
        try:
            # 直接传入原始df，函数内部会逐步计算指标
            backtest_result = realtime_backtest_marker(
                df,  # 原始OHLC数据
                ema_periods=[10, 20, 50],
                ema_names=['ema_kuai', 'ema_zhong', 'ema_man'],
                bb_period=20,
                adx_period=14,
                lookback_window=100,
                start_delay=60  # 给足够时间让指标稳定
            )
            # print(backtest_result)
            # 显示回测结果摘要
            print_backtest_summary(backtest_result)
            
            signal_display=[0,1,1]
            plot_kline_with_signals(
            backtest_result,  # 你的回测结果DataFrame
            signal_display=signal_display,
            save_path=r"C:\Users\<USER>\Desktop\my_python\claudeV1\量化\可视化\png\signals_chartV2.png"
        )
            
            print(backtest_result)
    
            # 分析信号分布
            analyze_signal_distribution(backtest_result)
            # 可选：保存结果
            # backtest_result.to_csv('realtime_backtest_result.csv', index=False)
            
        except Exception as e:
            print(f"回测标记时出错: {e}")
            return

    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()