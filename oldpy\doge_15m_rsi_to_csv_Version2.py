import sqlite3
import pandas as pd

def compute_rsi(series: pd.Series, period: int = 14) -> pd.Series:
    delta = series.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period, min_periods=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period, min_periods=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

if __name__ == "__main__":
    db_path = "量化/binance_kline_db/kline.db"   # 修改为你的实际路径
    table_name = "doge15m"                    # 按实际表名，也可能是 'dogeusdt15m'
    csv_path = "doge_15m_rsi14.csv"

    # 连接数据库并读入数据
    with sqlite3.connect(db_path) as conn:
        # 先查表名是否存在
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?;", (table_name,))
        if not cursor.fetchone():
            raise Exception(f"表 {table_name} 不存在！")
        df = pd.read_sql_query(f'SELECT open_time, close FROM "{table_name}" ORDER BY open_time ASC', conn)

    # 数据预处理
    df['close'] = pd.to_numeric(df['close'], errors='coerce')
    df = df.dropna(subset=['close'])  # 去除无法转为数字的close

    # 计算RSI
    df['rsi14'] = compute_rsi(df['close'], period=14)

    # 保存到CSV
    df.to_csv(csv_path, index=False)
    print(f"已保存到 {csv_path}")