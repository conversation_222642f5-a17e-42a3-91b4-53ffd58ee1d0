import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

# 从您的结果数据创建DataFrame（这里只显示前25行作为示例）
data = {
    'macd_Fast': [4, 6, 6, 6, 4, 6, 4, 4, 4, 4, 4, 6, 6, 8, 4, 6, 6, 4, 6, 6, 6, 4, 4, 4, 4],
    'macd_Slow': [20, 20, 20, 26, 24, 24, 20, 20, 24, 26, 24, 20, 20, 20, 24, 24, 20, 20, 26, 24, 24, 20, 24, 20, 24],
    'macd_Signal': [9, 7, 7, 7, 9, 7, 11, 9, 11, 9, 11, 7, 7, 7, 9, 7, 9, 11, 7, 7, 7, 11, 9, 9, 11],
    'zhiyinPre': [3.0, 2.0, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 1.5, 3.0, 2.0, 2.0, 2.0, 2.0, 2.0, 3.0, 3.0, 3.0, 3.0, 3.0, 2.0, 2.0],
    'zhisunPre': [0.2] * 25,  # 所有都是0.2
    'total_profit': [15.674183, 15.114583, 14.967183, 14.613883, 14.613883, 14.613883, 14.613883, 14.213883, 14.213883, 13.813883, 
                    13.509983, 13.314583, 13.227483, 12.914583, 12.914583, 12.914583, 12.914583, 12.914583, 12.914583, 12.874183, 
                    12.874183, 12.874183, 12.874183, 12.514583, 12.514583],
    'num_long': [15, 14, 14, 14, 14, 14, 14, 15, 15, 16, 15, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 15, 15],
    'num_short': [16, 15, 15, 15, 15, 15, 15, 16, 16, 17, 16, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 16, 16]
}

df = pd.DataFrame(data)

def analyze_macd_optimization_results(df):
    """
    分析MACD参数优化结果
    """
    print("=" * 60)
    print("MACD参数优化结果分析报告")
    print("=" * 60)
    
    # 1. 基本统计信息
    print("\n1. 基本统计信息:")
    print(f"   测试的参数组合数量: {len(df)}")
    print(f"   最高总收益: {df['total_profit'].max():.6f}")
    print(f"   最低总收益: {df['total_profit'].min():.6f}")
    print(f"   平均总收益: {df['total_profit'].mean():.6f}")
    print(f"   收益标准差: {df['total_profit'].std():.6f}")
    
    # 2. 最佳参数组合
    print("\n2. 最佳参数组合 (前5名):")
    top_5 = df.nlargest(5, 'total_profit')
    for i, (idx, row) in enumerate(top_5.iterrows()):
        print(f"   排名 {i+1}: Fast={int(row['macd_Fast'])}, Slow={int(row['macd_Slow'])}, Signal={int(row['macd_Signal'])}")
        print(f"           止盈={row['zhiyinPre']}, 止损={row['zhisunPre']}")
        print(f"           总收益={row['total_profit']:.6f}, 做多={int(row['num_long'])}次, 做空={int(row['num_short'])}次")
        print()
    
    # 3. 参数影响分析
    print("3. 各参数对收益的影响:")
    
    # macd_Fast 参数分析
    fast_analysis = df.groupby('macd_Fast')['total_profit'].agg(['mean', 'std', 'count']).round(6)
    print("\n   macd_Fast 参数影响:")
    for fast_val, stats in fast_analysis.iterrows():
        print(f"     Fast={int(fast_val)}: 平均收益={stats['mean']:.6f} (±{stats['std']:.6f}), 测试次数={int(stats['count'])}")
    
    # macd_Slow 参数分析
    slow_analysis = df.groupby('macd_Slow')['total_profit'].agg(['mean', 'std', 'count']).round(6)
    print("\n   macd_Slow 参数影响:")
    for slow_val, stats in slow_analysis.iterrows():
        print(f"     Slow={int(slow_val)}: 平均收益={stats['mean']:.6f} (±{stats['std']:.6f}), 测试次数={int(stats['count'])}")
    
    # macd_Signal 参数分析
    signal_analysis = df.groupby('macd_Signal')['total_profit'].agg(['mean', 'std', 'count']).round(6)
    print("\n   macd_Signal 参数影响:")
    for signal_val, stats in signal_analysis.iterrows():
        print(f"     Signal={int(signal_val)}: 平均收益={stats['mean']:.6f} (±{stats['std']:.6f}), 测试次数={int(stats['count'])}")
    
    # 止盈参数分析
    zhiying_analysis = df.groupby('zhiyinPre')['total_profit'].agg(['mean', 'std', 'count']).round(6)
    print("\n   止盈倍数(zhiyinPre) 参数影响:")
    for zy_val, stats in zhiying_analysis.iterrows():
        print(f"     止盈={zy_val}: 平均收益={stats['mean']:.6f} (±{stats['std']:.6f}), 测试次数={int(stats['count'])}")
    
    # 4. 交易次数分析
    df['total_trades'] = df['num_long'] + df['num_short']
    print(f"\n4. 交易次数分析:")
    print(f"   平均总交易次数: {df['total_trades'].mean():.1f}")
    print(f"   平均做多次数: {df['num_long'].mean():.1f}")
    print(f"   平均做空次数: {df['num_short'].mean():.1f}")
    print(f"   做多做空比例: {df['num_long'].sum()}:{df['num_short'].sum()}")
    
    # 5. 参数组合建议
    print("\n5. 参数优化建议:")
    
    # 找出最佳的Fast参数
    best_fast = fast_analysis['mean'].idxmax()
    print(f"   🎯 最佳 macd_Fast: {int(best_fast)} (平均收益: {fast_analysis.loc[best_fast, 'mean']:.6f})")
    
    # 找出最佳的Slow参数
    best_slow = slow_analysis['mean'].idxmax()
    print(f"   🎯 最佳 macd_Slow: {int(best_slow)} (平均收益: {slow_analysis.loc[best_slow, 'mean']:.6f})")
    
    # 找出最佳的Signal参数
    best_signal = signal_analysis['mean'].idxmax()
    print(f"   🎯 最佳 macd_Signal: {int(best_signal)} (平均收益: {signal_analysis.loc[best_signal, 'mean']:.6f})")
    
    # 找出最佳的止盈参数
    best_zhiying = zhiying_analysis['mean'].idxmax()
    print(f"   🎯 最佳 止盈倍数: {best_zhiying} (平均收益: {zhiying_analysis.loc[best_zhiying, 'mean']:.6f})")
    
    # 6. 风险收益分析
    print(f"\n6. 风险收益分析:")
    # 收益率 vs 交易次数
    correlation_profit_trades = df['total_profit'].corr(df['total_trades'])
    print(f"   总收益与交易次数相关性: {correlation_profit_trades:.4f}")
    
    # 计算夏普比率的简化版本（收益/波动率）
    df['return_per_trade'] = df['total_profit'] / df['total_trades']
    print(f"   平均单笔交易收益: {df['return_per_trade'].mean():.6f}")
    print(f"   单笔交易收益标准差: {df['return_per_trade'].std():.6f}")
    
    sharpe_like = df['return_per_trade'].mean() / df['return_per_trade'].std()
    print(f"   类夏普比率(收益/波动): {sharpe_like:.4f}")
    
    # 7. 推荐的最优参数组合
    print(f"\n7. 🏆 推荐的最优参数组合:")
    best_row = df.loc[df['total_profit'].idxmax()]
    print(f"   macd_Fast = {int(best_row['macd_Fast'])}")
    print(f"   macd_Slow = {int(best_row['macd_Slow'])}")
    print(f"   macd_Signal = {int(best_row['macd_Signal'])}")
    print(f"   zhiyinPre = {best_row['zhiyinPre']}")
    print(f"   zhisunPre = {best_row['zhisunPre']}")
    print(f"   预期总收益: {best_row['total_profit']:.6f}")
    print(f"   预期交易次数: 做多{int(best_row['num_long'])}次, 做空{int(best_row['num_short'])}次")
    
    print("\n" + "=" * 60)
    return df

def plot_optimization_results(df):
    """
    绘制优化结果图表
    """
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 总收益分布
    axes[0, 0].hist(df['total_profit'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0, 0].set_title('总收益分布', fontsize=14, fontweight='bold')
    axes[0, 0].set_xlabel('总收益')
    axes[0, 0].set_ylabel('频次')
    axes[0, 0].axvline(df['total_profit'].mean(), color='red', linestyle='--', label=f'平均值: {df["total_profit"].mean():.3f}')
    axes[0, 0].legend()
    
    # 2. 各macd_Fast参数的收益对比
    fast_means = df.groupby('macd_Fast')['total_profit'].mean()
    axes[0, 1].bar(fast_means.index.astype(str), fast_means.values, color='lightgreen', alpha=0.7)
    axes[0, 1].set_title('不同 macd_Fast 参数的平均收益', fontsize=14, fontweight='bold')
    axes[0, 1].set_xlabel('macd_Fast')
    axes[0, 1].set_ylabel('平均总收益')
    
    # 3. 交易次数 vs 收益散点图
    df['total_trades'] = df['num_long'] + df['num_short']
    scatter = axes[1, 0].scatter(df['total_trades'], df['total_profit'], 
                               c=df['macd_Fast'], cmap='viridis', alpha=0.6, s=50)
    axes[1, 0].set_title('交易次数 vs 总收益', fontsize=14, fontweight='bold')
    axes[1, 0].set_xlabel('总交易次数')
    axes[1, 0].set_ylabel('总收益')
    plt.colorbar(scatter, ax=axes[1, 0], label='macd_Fast')
    
    # 4. 止盈参数影响
    zhiying_means = df.groupby('zhiyinPre')['total_profit'].mean()
    axes[1, 1].bar(zhiying_means.index.astype(str), zhiying_means.values, color='orange', alpha=0.7)
    axes[1, 1].set_title('不同止盈倍数的平均收益', fontsize=14, fontweight='bold')
    axes[1, 1].set_xlabel('止盈倍数 (zhiyinPre)')
    axes[1, 1].set_ylabel('平均总收益')
    
    plt.tight_layout()
    plt.show()

# 执行分析
if __name__ == "__main__":
    # 分析结果
    analyzed_df = analyze_macd_optimization_results(df)
    
    # 绘制图表
    print("\n正在生成可视化图表...")
    plot_optimization_results(analyzed_df)