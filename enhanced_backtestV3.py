import requests
import pandas as pd
import sqlite3
import datetime # 确保只有这一行或类似的导入
from typing import List, Union
import pytz
import numpy as np
if not hasattr(np, 'NaN'):
    np.NaN = np.nan

import numpy as np  # 同时导入标准的numpy
import pandas_ta as ta
from ku import *
from config import calculate_kline_needed, get_kline_data, add_rsi_to_kline_data
from analyze_trendV1 import TrendPullbackDetector
from tradeing import check_trading_single, judge_signal, calc_price, batch_judge_trade
from bayes_opt import BayesianOptimization
from bayes_opt.logger import JSONLogger
from bayes_opt.event import Events
# from bayes_opt.util import UtilityFunction
import json
import os
# from datetime import datetime as dt # 确保这一行是注释掉的或被删除的
import warnings
warnings.filterwarnings('ignore')

def sub_kline_time(old_date, count, kline_type):
    """日期转换函数（向前推算）"""
    if isinstance(old_date, str):
        dt_obj = None
        tried_formats = [
            "%Y-%m-%d %H:%M:%S",
            "%Y-%m-%d",
            "%Y-%m-%d %H:%M",
            "%Y-%m-%d %H",
            "%Y/%m/%d %H:%M:%S",
            "%Y/%m/%d",
            "%Y/%m/%d %H:%M",
            "%Y/%m/%d %H",
        ]
        for fmt in tried_formats:
            try:
                dt_obj = datetime.strptime(old_date, fmt)
                break
            except ValueError:
                continue
        if dt_obj is None:
            raise ValueError(f"无法识别日期格式: {old_date}")
    else:
        dt_obj = old_date

    unit_map = {'m': 1, 'h': 60, 'd': 1440}
    import re
    match = re.match(r"(\d+)([mhd])", kline_type)
    if not match:
        raise ValueError(f"不支持的k线类型: {kline_type}")
    num, unit = match.groups()
    minutes = int(num) * unit_map[unit]

    new_dt = dt_obj - timedelta(minutes=minutes * count)
    return new_dt.strftime("%Y-%m-%d %H:%M:%S")

def safe_get(d, keys, default=''):
    """多级安全取值"""
    for k in keys:
        if isinstance(d, dict):
            d = d.get(k, None)
        else:
            return default
        if d is None:
            return default
    return d if d is not None else default

def list_to_str(lst, prefix=''):
    """将列表转换为带前缀的字符串"""
    if not lst:
        return ''
    return '; '.join([f"{prefix}{str(item)}" for item in lst])

def write_analysis_to_kline(df, next_index, analysis_result):
    """将分析结果写入kline_df_with_rsi的指定行"""
    if not isinstance(analysis_result, dict):
        print(f"警告：analysis_result 为空或不是字典，实际值为：{analysis_result}")
        return

    advice = analysis_result.get('advice', {})
    fields_to_update = {
        '投资建议': advice.get('投资建议', analysis_result.get('投资建议', '')),
    }

    for col in fields_to_update.keys():
        if col not in df.columns:
            df[col] = ""
    if next_index < len(df):
        for field, value in fields_to_update.items():
            df.at[next_index, field] = value
    else:
        print(f"警告：next_index = {next_index} 已超出DataFrame范围，无法写入数据！")
        print(fields_to_update['投资建议'])

def run_backtest(params, Uname, Ktime, strTime_start, strTime_end, return_full_metrics=False):
    """运行回测并返回目标指标"""
    # 整数参数处理
    int_params = ['rsi_length', 'windowsTime', 'lookback_period', 'rsi_threshold_bull', 'rsi_threshold_bear']
    for param in int_params:
        if param in params:
            params[param] = int(params[param])
    
    # 构建参数字典
    my_pram = {
        "rsi_length": params.get('rsi_length', 12),
        "macd_length": 26,
        "max_lookback_kline_count": 100,
        "my_sma_length": 5,
        "my_ema_length": 5,
        "my_gaussian_sigma": params.get('my_gaussian_sigma', 2.0),
        "zhiyinPre": params.get('zhiyinPre', 2.4),
        "zhisunPre": params.get('zhisunPre', 1.8),
        "windowsTime": params.get('windowsTime', 16)
    }
    
    # 获取K线数据
    needed_klines = calculate_kline_needed(my_pram)
    strTime = sub_kline_time(strTime_start, needed_klines, Ktime)
    
    try:
        kline_df = get_local_kline_data(Uname, Ktime, strTime, strTime_end)
    except Exception as e:
        print(f"获取数据失败: {e}")
        return -1000 if not return_full_metrics else {}
    
    # 初始化DataFrame
    kline_df_with_rsi = kline_df.copy()
    kline_df_with_rsi['RSI'] = None
    kline_df_with_rsi['RSI_Gaussian_2.0'] = None
    kline_df_with_rsi['投资建议'] = None
    
    # 初始化趋势检测器
    lookback_period = int(params.get('lookback_period', 48))
    detector = TrendPullbackDetector(
        lookback_period,
        rsi_threshold_bull=int(params.get('rsi_threshold_bull', 42)),
        rsi_threshold_bear=int(params.get('rsi_threshold_bear', 40)),
        price_retracement_pct=params.get('price_retracement_pct', 0.382),
        min_trend_strength=params.get('min_trend_strength', 0.6),
        smoothing_factor=params.get('smoothing_factor', 0.8)
    )
    
    tradingSingle = 0
    close_values = kline_df_with_rsi['close'].values
    total_loops = len(kline_df_with_rsi) - needed_klines + 1
    
    # 执行回测
    for count in range(total_loops):
        nowPrice = close_values[count + needed_klines - 1]
        window_close = close_values[count : count + needed_klines]
        
        window_rsi = fast_rsi_calculation(window_close, my_pram["rsi_length"])
        valid_rsi = window_rsi[~np.isnan(window_rsi)]
        if len(valid_rsi) > 0:
            smooth_rsi_window = fast_gaussian_smooth(valid_rsi, my_pram["my_gaussian_sigma"])
        else:
            continue
        
        # 调整数据长度
        window_close = window_close[-lookback_period:]
        window_rsi = window_rsi[-lookback_period:]
        smooth_rsi_window = smooth_rsi_window[-lookback_period:]
        
        result = detector.detect_pattern(window_close.tolist(), window_rsi.tolist(), smooth_rsi_window.tolist(), "15m")
        
        next_index = count + needed_klines
        
        # 模拟交易
        nowSingle = judge_signal(round(result['confidence'], 3), result['pattern'])
        tradingSingle, nowSingle = check_trading_single(tradingSingle, nowSingle)
        
        if next_index < len(kline_df_with_rsi):
            kline_df_with_rsi.at[next_index, 'tradingSingle'] = tradingSingle
            kline_df_with_rsi.at[next_index, 'nowSingle'] = nowSingle
            if nowSingle != 0:
                kline_df_with_rsi.at[next_index, 'buyPrice'] = calc_price(nowSingle, nowPrice, 0)
            
            kline_df_with_rsi.at[next_index, '识别模式'] = result['pattern']
            kline_df_with_rsi.at[next_index, '置信度'] = f"{result['confidence']:.2%}"
            kline_df_with_rsi.at[next_index, '建议'] = result['recommendation']
            kline_df_with_rsi.at[next_index, '价格趋势'] = result['price_features']['trend_type']
            kline_df_with_rsi.at[next_index, '当前RSI'] = f"{result['rsi_features']['current_rsi']:.2f}"
    
    # 计算交易指标
    kline_df_with_rsi = batch_judge_trade(kline_df_with_rsi, my_pram['zhiyinPre'], my_pram['zhisunPre'], my_pram['windowsTime'], '15m')
    metrics = calculate_trading_metrics_with_signals(kline_df_with_rsi)
    
    if return_full_metrics:
        return metrics
    
    # 从嵌套字典中提取指标（使用percentage_of_capital_metrics）
    pct_metrics = metrics.get('percentage_of_capital_metrics', {})
    sharpe_ratio = float(pct_metrics.get('夏普率', 0))
    
    # 处理numpy类型
    if hasattr(sharpe_ratio, 'item'):
        sharpe_ratio = sharpe_ratio.item()
    
    return sharpe_ratio if sharpe_ratio > -100 else -100

def objective_function(**params):
    """使用总收益作为优化目标，与V2保持一致"""
    global Uname, Ktime, strTime_start, strTime_end
    metrics = run_backtest(params, Uname, Ktime, strTime_start, strTime_end, return_full_metrics=True)
    
    if not metrics:
        return -1000
    
    # 使用总收益而不是夏普比率
    pct_metrics = metrics.get('percentage_of_capital_metrics', {})
    total_profit = float(pct_metrics.get('总收益', 0))
    
    print(f"当前参数组合总收益: {total_profit:.2f}%")
    return total_profit if total_profit > -100 else -100

def save_optimization_results(best_params, best_value, worst_params, worst_value, all_results, save_path, Uname, Ktime, strTime_start, strTime_end):
    """保存优化结果"""
    # 创建保存目录
    os.makedirs(save_path, exist_ok=True)
    
    # 获取当前时间
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # 重新运行获取最优和最差参数的完整指标
    best_metrics = run_backtest(best_params, Uname, Ktime, strTime_start, strTime_end, return_full_metrics=True)
    worst_metrics = run_backtest(worst_params, Uname, Ktime, strTime_start, strTime_end, return_full_metrics=True)
    
    # 提取两种交易模式的指标
    best_fixed = best_metrics.get('fixed_amount_metrics', {})
    best_pct = best_metrics.get('percentage_of_capital_metrics', {})
    worst_fixed = worst_metrics.get('fixed_amount_metrics', {})
    worst_pct = worst_metrics.get('percentage_of_capital_metrics', {})
    
    # 构建结果DataFrame
    results_data = []
    
    # 最优结果行（使用percentage_of_capital_metrics）
    best_row = {
        '记录类型': '最优',
        '虚拟币': Uname,
        'K线周期': Ktime,
        '起始时间': strTime_start,
        '结束时间': strTime_end,
        '记录时间': current_time,
        'zhiyin': round(best_params.get('zhiyinPre', 0), 2),
        'zhisun': round(best_params.get('zhisunPre', 0), 2),
        'windowsTime': int(best_params.get('windowsTime', 0)),
        'rsi_length': int(best_params.get('rsi_length', 0)),
        'my_gaussian_sigma': round(best_params.get('my_gaussian_sigma', 0), 2),
        'lookback_period': int(best_params.get('lookback_period', 0)),
        'rsi_threshold_bull': int(best_params.get('rsi_threshold_bull', 0)),
        'rsi_threshold_bear': int(best_params.get('rsi_threshold_bear', 0)),
        'price_retracement_pct': round(best_params.get('price_retracement_pct', 0), 3),
        'min_trend_strength': round(best_params.get('min_trend_strength', 0), 2),
        'smoothing_factor': round(best_params.get('smoothing_factor', 0), 2),
        'profit': round(float(best_pct.get('总收益', 0)), 2),
        'max_drawdown': round(float(best_pct.get('最大回撤', 0)), 2),
        'sharpe_ratio': round(float(best_pct.get('夏普率', 0)), 2),
        'num_trades': int(best_pct.get('交易次数', 0)),
        'win_rate': round(float(best_pct.get('胜率', 0)), 2),
        'final_capital': round(float(best_pct.get('最终资金', 10000)), 2)
    }
    results_data.append(best_row)
    
    # 最差结果行
    worst_row = {
        '记录类型': '最差',
        '虚拟币': Uname,
        'K线周期': Ktime,
        '起始时间': strTime_start,
        '结束时间': strTime_end,
        '记录时间': current_time,
        'zhiyin': round(worst_params.get('zhiyinPre', 0), 2),
        'zhisun': round(worst_params.get('zhisunPre', 0), 2),
        'windowsTime': int(worst_params.get('windowsTime', 0)),
        'rsi_length': int(worst_params.get('rsi_length', 0)),
        'my_gaussian_sigma': round(worst_params.get('my_gaussian_sigma', 0), 2),
        'lookback_period': int(worst_params.get('lookback_period', 0)),
        'rsi_threshold_bull': int(worst_params.get('rsi_threshold_bull', 0)),
        'rsi_threshold_bear': int(worst_params.get('rsi_threshold_bear', 0)),
        'price_retracement_pct': round(worst_params.get('price_retracement_pct', 0), 3),
        'min_trend_strength': round(worst_params.get('min_trend_strength', 0), 2),
        'smoothing_factor': round(worst_params.get('smoothing_factor', 0), 2),
        'profit': round(float(worst_pct.get('总收益', 0)), 2),
        'max_drawdown': round(float(worst_pct.get('最大回撤', 0)), 2),
        'sharpe_ratio': round(float(worst_pct.get('夏普率', 0)), 2),
        'num_trades': int(worst_pct.get('交易次数', 0)),
        'win_rate': round(float(worst_pct.get('胜率', 0)), 2),
        'final_capital': round(float(worst_pct.get('最终资金', 10000)), 2)
    }
    results_data.append(worst_row)
    
    # 保存为CSV
    df_results = pd.DataFrame(results_data)
    filename = f"{Uname}_{Ktime}_optimization_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    filepath = os.path.join(save_path, filename)
    df_results.to_csv(filepath, index=False, encoding='utf-8-sig')
    
    # 打印结果摘要
    print(f"\n=== 最优参数结果 ===")
    print(f"总收益: {best_row['profit']:.2f}%")
    print(f"最大回撤: {best_row['max_drawdown']:.2f}%")
    print(f"夏普率: {best_row['sharpe_ratio']:.2f}")
    print(f"胜率: {best_row['win_rate']:.2f}%")
    print(f"交易次数: {best_row['num_trades']}")
    print(f"最终资金: {best_row['final_capital']:.2f}")
    
    print(f"\n=== 最差参数结果 ===")
    print(f"总收益: {worst_row['profit']:.2f}%")
    print(f"最大回撤: {worst_row['max_drawdown']:.2f}%")
    print(f"夏普率: {worst_row['sharpe_ratio']:.2f}")
    print(f"胜率: {worst_row['win_rate']:.2f}%")
    print(f"交易次数: {worst_row['num_trades']}")
    print(f"最终资金: {worst_row['final_capital']:.2f}")
    
    # 保存详细的JSON结果
    json_data = {
        'optimization_info': {
            'symbol': Uname,
            'interval': Ktime,
            'start_time': strTime_start,
            'end_time': strTime_end,
            'optimization_time': current_time
        },
        'best_result': {
            'parameters': best_params,
            'value': best_value,
            'metrics': {
                'fixed_amount': best_fixed,
                'percentage_of_capital': best_pct
            }
        },
        'worst_result': {
            'parameters': worst_params,
            'value': worst_value,
            'metrics': {
                'fixed_amount': worst_fixed,
                'percentage_of_capital': worst_pct
            }
        },
        'all_results': all_results
    }
    
    # 处理numpy类型转换
    def convert_numpy(obj):
        if isinstance(obj, np.generic):
            return obj.item()
        elif isinstance(obj, dict):
            return {k: convert_numpy(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy(v) for v in obj]
        return obj
    
    json_data = convert_numpy(json_data)
    
    json_filename = f"{Uname}_{Ktime}_optimization_details_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    json_filepath = os.path.join(save_path, json_filename)
    with open(json_filepath, 'w', encoding='utf-8') as f:
        json.dump(json_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n优化结果已保存到:")
    print(f"CSV文件: {filepath}")
    print(f"JSON文件: {json_filepath}")

if __name__ == "__main__":
    # 全局参数设置
    Uname = 'doge'
    Ktime = '15m'
    strTime_start = '2024-2-1'
    strTime_end = '2024-2-25'
    
    # 定义优化参数的搜索范围
    pbounds = {
        # 策略参数
        'rsi_length': (8, 16),
        'my_gaussian_sigma': (0.5, 2.5),
        # 'zhiyinPre': (0.5, 5.0),
        # 'zhisunPre': (0.5, 3.0),
        # 'windowsTime': (10, 50),
        # 趋势检测器参数
        'lookback_period': (10, 60),
        # 'rsi_threshold_bull': (40, 60),
        # 'rsi_threshold_bear': (40, 60),
        # 'price_retracement_pct': (0.2, 0.5),
        # 'min_trend_strength': (0.5, 0.9),
        # 'smoothing_factor': (0.6, 0.95),
    }
    
    # 贝叶斯优化参数
    total_iterations = 1000
    initial_points = 200
    
    # 创建贝叶斯优化器
    optimizer = BayesianOptimization(
        f=objective_function,
        pbounds=pbounds,
        random_state=42,
        verbose=2
    )
    
    # 设置日志记录
    logger = JSONLogger(path=f"./optimization_logs_{Uname}_{Ktime}.json")
    optimizer.subscribe(Events.OPTIMIZATION_STEP, logger)
    
    # 执行优化
    print(f"开始贝叶斯优化...")
    print(f"币种: {Uname}, K线周期: {Ktime}")
    print(f"回测时间: {strTime_start} 至 {strTime_end}")
    print(f"初始随机探索: {initial_points}次")
    print(f"贝叶斯优化迭代: {total_iterations - initial_points}次\n")
    
    optimizer.maximize(
        init_points=initial_points,
        n_iter=total_iterations - initial_points
        # acq='ucb',  # 使用UCB采集函数
        # kappa=2.5,  # 探索与利用的平衡参数
        # xi=0.0
    )
    
    # 获取最优结果
    best_params = optimizer.max['params']
    best_value = optimizer.max['target']
    
    print(f"\n最优参数组合:")
    for param, value in best_params.items():
        if param in ['rsi_length', 'windowsTime', 'lookback_period', 'rsi_threshold_bull', 'rsi_threshold_bear']:
            print(f"{param}: {int(value)}")
        else:
            print(f"{param}: {value:.4f}")
    print(f"最优夏普比率: {best_value:.4f}")
    
    # 获取所有结果用于找最差参数
    all_results = []
    for i, res in enumerate(optimizer.res):
        all_results.append({
            'iteration': i,
            'parameters': res['params'],
            'value': res['target']
        })
    
    # 找出最差参数
    worst_result = min(optimizer.res, key=lambda x: x['target'])
    worst_params = worst_result['params']
    worst_value = worst_result['target']
    
    print(f"\n最差参数组合:")
    for param, value in worst_params.items():
        if param in ['rsi_length', 'windowsTime', 'lookback_period', 'rsi_threshold_bull', 'rsi_threshold_bear']:
            print(f"{param}: {int(value)}")
        else:
            print(f"{param}: {value:.4f}")
    print(f"最差夏普比率: {worst_value:.4f}")
    
    # 保存结果
    save_path = r"C:\Users\<USER>\Desktop\my_python\claudeV1\量化\optimization"
    save_optimization_results(best_params, best_value, worst_params, worst_value, all_results, save_path, Uname, Ktime, strTime_start, strTime_end)
    
    # 使用最优参数运行一次完整回测并保存详细结果
    print("\n使用最优参数运行完整回测...")
    final_metrics = run_backtest(best_params, Uname, Ktime, strTime_start, strTime_end, return_full_metrics=True)
    
    print("\n--- 交易指标报告 ---")
    for strategy_name, strategy_metrics in final_metrics.items():
        print(f"\n{strategy_name}:")
        for key, value in strategy_metrics.items():
            if isinstance(value, float) or (hasattr(value, 'item') and callable(value.item)):
                print(f"  {key}: {float(value):.2f}")
            else:
                print(f"  {key}: {value}")