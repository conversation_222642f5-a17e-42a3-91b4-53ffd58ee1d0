import sqlite3
import pandas as pd

db_path = "量化/binance_kline_db/kline.db"  # 数据库路径，可按需修改
table_name = "doge15m"                     # 数据表名，按实际情况修改
csv_path = "doge_15m.csv"                  # 输出的CSV文件名

if __name__ == "__main__":
    with sqlite3.connect(db_path) as conn:
        # 检查表是否存在
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?;", (table_name,))
        if not cursor.fetchone():
            raise Exception(f"表 {table_name} 不存在！")
        # 读取全部数据
        df = pd.read_sql_query(f'SELECT * FROM "{table_name}" ORDER BY open_time ASC', conn)

    df.to_csv(csv_path, index=False)
    print(f"已保存 {len(df)} 条数据到 {csv_path}")