import okx.MarketData as MarketData
import pandas as pd

flag = "0"
marketDataAPI = MarketData.MarketAPI(flag=flag)

def get_mark_price_kline_df(inst_id: str, bar: str = "1m", total: int = 1440, tz: str = "Asia/Shanghai") -> pd.DataFrame:
    """
    获取OKX标记价格K线数据（仅6字段），返回 DataFrame
    """
    MAX_LIMIT = 1440
    if total > MAX_LIMIT:
        print(f"最大可获取{MAX_LIMIT}条数据，已自动调整为{MAX_LIMIT}")
        total = MAX_LIMIT

    all_data = []
    after = None
    while len(all_data) < total:
        fetch_num = min(100, total - len(all_data))
        params = {
            "instId": inst_id,
            "bar": str(bar),
            "limit": str(fetch_num),
        }
        if after is not None:
            params["after"] = str(after)
        resp = marketDataAPI.get_mark_price_candlesticks(**params)
        data = resp.get("data", [])
        if not data:
            break
        all_data.extend(data)
        if len(data) < fetch_num:
            break
        after = data[-1][0]

    # 字段只取6个
    columns = ["open_time", "open", "high", "low", "close", "confirm"]
    df = pd.DataFrame(all_data[:total], columns=columns)
    df["open_time"] = pd.to_datetime(df["open_time"].astype(float), unit="ms", utc=True).dt.tz_convert(tz)
    for col in ["open", "high", "low", "close"]:
        df[col] = pd.to_numeric(df[col], errors="coerce")
    df["confirm"] = df["confirm"].astype(int)
    return df

if __name__ == "__main__":
    params = {
    "inst_id": "DOGE-USDT-SWAP",
    "bar": "1m",
    "total": 1640}
    df = get_mark_price_kline_df(**params)
    print(df)