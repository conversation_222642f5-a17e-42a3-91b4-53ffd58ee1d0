#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能打印函数的效果
演示如何识别不同类型的 OKX API 返回结果并智能打印关键信息
"""

from datetime import datetime

def smart_print_result(result, context="", show_full=False):
    """
    智能打印 OKX API 返回的 result，自动识别类型并打印关键信息
    
    Args:
        result: OKX API 返回的结果
        context: 上下文描述，用于标识这是什么操作的结果
        show_full: 是否显示完整的 result（调试用）
    """
    if show_full:
        print(f"\n=== 完整 Result ({context}) ===")
        print(result)
        print("=" * 50)
        return
    
    if not result:
        print(f"❌ {context}: 返回结果为空")
        return
    
    # 检查基本的 OKX API 响应格式
    if not isinstance(result, dict):
        print(f"⚠️ {context}: 返回结果不是字典格式")
        print(f"Result: {result}")
        return
    
    code = result.get("code", "")
    msg = result.get("msg", "")
    data = result.get("data", [])
    
    print(f"\n📊 {context} 结果:")
    print(f"状态码: {code}")
    
    if code != "0":
        print(f"❌ 操作失败: {msg}")
        return
    
    print(f"✅ 操作成功: {msg if msg else '成功'}")
    
    if not data:
        print("📝 无数据返回")
        return
    
    # 根据数据内容智能识别类型并打印关键信息
    if isinstance(data, list) and len(data) > 0:
        first_item = data[0]
        
        # 识别订单信息
        if 'ordId' in first_item or 'clOrdId' in first_item:
            _print_order_info(data, context)
        
        # 识别算法订单信息
        elif 'algoId' in first_item:
            _print_algo_order_info(data, context)
        
        # 识别持仓信息
        elif 'instId' in first_item and 'pos' in first_item:
            _print_position_info(data, context)
        
        # 识别账户余额信息
        elif 'details' in first_item:
            _print_balance_info(data, context)
        
        # 其他类型的数据
        else:
            _print_generic_info(data, context)
    
    else:
        print(f"📝 数据: {data}")

def _print_order_info(data, context):
    """打印订单信息"""
    print(f"📋 找到 {len(data)} 个订单:")
    for i, order in enumerate(data):
        print(f"  订单 {i+1}:")
        print(f"    🏷️  产品: {order.get('instId', 'N/A')}")
        print(f"    🆔 订单ID: {order.get('ordId', 'N/A')}")
        print(f"    🏪 客户订单ID: {order.get('clOrdId', 'N/A')}")
        print(f"    📊 状态: {order.get('state', 'N/A')}")
        print(f"    💰 方向: {order.get('side', 'N/A')}")
        print(f"    📏 数量: {order.get('sz', 'N/A')}")
        print(f"    💵 价格: {order.get('px', 'N/A')}")
        print(f"    ✅ 成交数量: {order.get('accFillSz', 'N/A')}")
        
        # 时间转换
        c_time = order.get('cTime')
        u_time = order.get('uTime')
        if c_time:
            try:
                c_time_str = datetime.fromtimestamp(int(c_time)/1000).strftime('%Y-%m-%d %H:%M:%S')
                print(f"    🕐 创建时间: {c_time_str}")
            except:
                print(f"    🕐 创建时间: {c_time}")
        if u_time:
            try:
                u_time_str = datetime.fromtimestamp(int(u_time)/1000).strftime('%Y-%m-%d %H:%M:%S')
                print(f"    🔄 更新时间: {u_time_str}")
            except:
                print(f"    🔄 更新时间: {u_time}")
        print()

def _print_algo_order_info(data, context):
    """打印算法订单信息"""
    print(f"🤖 找到 {len(data)} 个算法订单:")
    for i, order in enumerate(data):
        print(f"  算法订单 {i+1}:")
        print(f"    🏷️  产品: {order.get('instId', 'N/A')}")
        print(f"    🆔 算法ID: {order.get('algoId', 'N/A')}")
        print(f"    📊 状态: {order.get('state', 'N/A')}")
        print(f"    🔧 类型: {order.get('ordType', 'N/A')}")
        print(f"    💰 方向: {order.get('side', 'N/A')}")
        print(f"    📏 数量: {order.get('sz', 'N/A')}")
        
        # 止损止盈信息
        if order.get('slTriggerPx'):
            print(f"    🛑 止损触发价: {order.get('slTriggerPx', 'N/A')}")
        if order.get('tpTriggerPx'):
            print(f"    🎯 止盈触发价: {order.get('tpTriggerPx', 'N/A')}")
        
        # 时间转换
        c_time = order.get('cTime')
        if c_time:
            try:
                c_time_str = datetime.fromtimestamp(int(c_time)/1000).strftime('%Y-%m-%d %H:%M:%S')
                print(f"    🕐 创建时间: {c_time_str}")
            except:
                print(f"    🕐 创建时间: {c_time}")
        print()

def _print_position_info(data, context):
    """打印持仓信息"""
    print(f"💼 找到 {len(data)} 个持仓:")
    for i, pos in enumerate(data):
        print(f"  持仓 {i+1}:")
        print(f"    🏷️  产品: {pos.get('instId', 'N/A')}")
        print(f"    🏭 产品类型: {pos.get('instType', 'N/A')}")
        print(f"    📊 持仓数量: {pos.get('pos', 'N/A')}")
        print(f"    💰 持仓方向: {pos.get('posSide', 'N/A')}")
        print(f"    💵 开仓均价: {pos.get('avgPx', 'N/A')}")
        print(f"    📈 标记价格: {pos.get('markPx', 'N/A')}")
        print(f"    🔧 杠杆倍数: {pos.get('lever', 'N/A')}")

        # 订单编号相关信息
        pos_id = pos.get('posId', 'N/A')
        trade_id = pos.get('tradeId', 'N/A')
        last_trade_id = pos.get('last', 'N/A')

        print(f"    🆔 持仓ID: {pos_id}")
        if trade_id != 'N/A':
            print(f"    🔄 最新成交ID: {trade_id}")
        if last_trade_id != 'N/A':
            print(f"    📝 最新成交编号: {last_trade_id}")

        # 财务信息
        margin = pos.get('margin', 'N/A')
        upl = pos.get('upl', 'N/A')
        upl_ratio = pos.get('uplRatio', 'N/A')
        liq_px = pos.get('liqPx', 'N/A')

        print(f"    💳 保证金: {margin}")
        print(f"    💸 未实现盈亏: {upl}")
        print(f"    📊 盈亏比例: {upl_ratio}")
        print(f"    ⚠️  强平价格: {liq_px}")

        # 时间信息（如果有的话）
        c_time = pos.get('cTime')
        u_time = pos.get('uTime')
        if c_time:
            try:
                c_time_str = datetime.fromtimestamp(int(c_time)/1000).strftime('%Y-%m-%d %H:%M:%S')
                print(f"    🕐 创建时间: {c_time_str}")
            except:
                print(f"    🕐 创建时间: {c_time}")
        if u_time:
            try:
                u_time_str = datetime.fromtimestamp(int(u_time)/1000).strftime('%Y-%m-%d %H:%M:%S')
                print(f"    🔄 更新时间: {u_time_str}")
            except:
                print(f"    🔄 更新时间: {u_time}")

        print()

def _print_balance_info(data, context):
    """打印账户余额信息"""
    print(f"💰 账户余额信息:")
    for account in data:
        details = account.get('details', [])
        for detail in details:
            ccy = detail.get('ccy', 'N/A')
            avail_bal = detail.get('availBal', 'N/A')
            frozen_bal = detail.get('frozenBal', 'N/A')
            try:
                if float(avail_bal) > 0 or float(frozen_bal) > 0:  # 只显示非零余额
                    print(f"    💎 {ccy}: 可用 {avail_bal}, 冻结 {frozen_bal}")
            except:
                print(f"    💎 {ccy}: 可用 {avail_bal}, 冻结 {frozen_bal}")

def _print_generic_info(data, context):
    """打印通用信息"""
    print(f"📝 数据项数: {len(data)}")
    if len(data) > 0:
        first_item = data[0]
        print(f"    主要字段: {list(first_item.keys())[:10]}")  # 显示前10个字段
        
        # 尝试找到一些常见的关键字段
        key_fields = ['instId', 'ordId', 'algoId', 'state', 'side', 'sz', 'px']
        for field in key_fields:
            if field in first_item:
                print(f"    {field}: {first_item[field]}")
    print()

def test_smart_print():
    """测试智能打印函数"""
    
    # 测试订单信息
    order_result = {
        "code": "0",
        "msg": "",
        "data": [
            {
                "instId": "BTC-USDT-SWAP",
                "ordId": "123456789",
                "clOrdId": "client123",
                "state": "filled",
                "side": "buy",
                "sz": "0.1",
                "px": "45000",
                "accFillSz": "0.1",
                "cTime": "1703123456789",
                "uTime": "1703123456890"
            }
        ]
    }
    
    # 测试算法订单信息
    algo_result = {
        "code": "0",
        "msg": "",
        "data": [
            {
                "instId": "ETH-USDT-SWAP",
                "algoId": "987654321",
                "state": "live",
                "ordType": "conditional",
                "side": "sell",
                "sz": "1.0",
                "slTriggerPx": "2800",
                "cTime": "1703123456789"
            }
        ]
    }
    
    # 测试持仓信息
    position_result = {
        "code": "0",
        "msg": "",
        "data": [
            {
                "instId": "DOGE-USDT-SWAP",
                "instType": "SWAP",
                "pos": "1000",
                "posSide": "long",
                "avgPx": "0.45",
                "markPx": "0.48",
                "upl": "30.0",
                "uplRatio": "0.0667",
                "lever": "10",
                "posId": "123456789",
                "tradeId": "987654321",
                "last": "0.48",
                "margin": "45.0",
                "liqPx": "0.35",
                "cTime": "1703123456789",
                "uTime": "1703123456890"
            }
        ]
    }
    
    # 测试失败结果
    error_result = {
        "code": "50001",
        "msg": "Insufficient balance",
        "data": []
    }
    
    # 测试空结果
    empty_result = None
    
    print("=" * 60)
    print("智能打印函数测试演示")
    print("=" * 60)
    
    # 执行测试
    smart_print_result(order_result, "查询订单")
    smart_print_result(algo_result, "查询算法订单")
    smart_print_result(position_result, "查询持仓")
    smart_print_result(error_result, "下单操作")
    smart_print_result(empty_result, "空结果测试")
    
    print("\n" + "=" * 60)
    print("对比：显示完整结果")
    print("=" * 60)
    smart_print_result(order_result, "查询订单", show_full=True)

if __name__ == "__main__":
    test_smart_print()
