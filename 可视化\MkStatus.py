from okx_sdkV1 import get_kline_data, OKXClient
from MkKu import analyze_numerical_series
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import os
from datetime import datetime

import pandas as pd
import numpy as np
from typing import Optional, Tu<PERSON>


def calculate_ema(df: pd.DataFrame, periods: list = [10, 20, 50], names: Optional[list] = None,
                  price_column: str = 'close', inplace: bool = True) -> Optional[pd.DataFrame]:
    """
    计算指数移动平均线 (EMA)
    
    Parameters:
    -----------
    df : pd.DataFrame
        包含价格数据的DataFrame
    periods : list, default [10, 20, 50]
        EMA周期列表
    names : list, optional
        与周期对应的列名列表
    price_column : str, default 'close'
        用于计算EMA的价格列名
    inplace : bool, default True
        是否直接修改原DataFrame
    
    Returns:
    --------
    pd.DataFrame or None
        如果inplace=False，返回修改后的DataFrame；否则返回None
    
    Raises:
    -------
    ValueError: 当数据不足或参数错误时抛出异常
    """
    # 数据验证
    if df.empty:
        raise ValueError("DataFrame不能为空")
    
    if price_column not in df.columns:
        raise ValueError(f"列 '{price_column}' 不存在于DataFrame中")
    
    # 检查数据量是否足够
    max_period = max(periods)
    if len(df) < max_period:
        raise ValueError(f"数据量不足：需要至少 {max_period} 条数据，当前只有 {len(df)} 条")
    
    # 检查价格数据是否有效
    if df[price_column].isnull().any():
        raise ValueError(f"价格列 '{price_column}' 包含空值")
    
    # 确保价格为数值类型
    try:
        df[price_column] = pd.to_numeric(df[price_column], errors='raise')
    except ValueError:
        raise ValueError(f"价格列 '{price_column}' 包含非数值数据")
    
    # 选择目标DataFrame
    target_df = df if inplace else df.copy()
    
    # 计算各周期的EMA
    if names and len(names) != len(periods):
        raise ValueError("The length of 'names' must match the length of 'periods'")

    for i, period in enumerate(periods):
        if period <= 0:
            raise ValueError(f"EMA周期必须为正数，当前为: {period}")
        
        column_name = names[i] if names else f'ema_{period}'
        target_df[column_name] = target_df[price_column].ewm(span=period, adjust=False).mean()
    
    if not inplace:
        return target_df
    
    return None


def calculate_bollinger_bands(df: pd.DataFrame, period: int = 20, std_dev: float = 2, 
                             price_column: str = 'close', inplace: bool = True) -> Optional[pd.DataFrame]:

    # 数据验证
    if df.empty:
        raise ValueError("DataFrame不能为空")
    
    if price_column not in df.columns:
        raise ValueError(f"列 '{price_column}' 不存在于DataFrame中")
    
    # 检查数据量是否足够
    if len(df) < period:
        raise ValueError(f"数据量不足：需要至少 {period} 条数据，当前只有 {len(df)} 条")
    
    # 参数验证
    if period <= 0:
        raise ValueError(f"周期必须为正数，当前为: {period}")
    
    if std_dev <= 0:
        raise ValueError(f"标准差倍数必须为正数，当前为: {std_dev}")
    
    # 检查价格数据是否有效
    if df[price_column].isnull().any():
        raise ValueError(f"价格列 '{price_column}' 包含空值")
    
    # 确保价格为数值类型
    try:
        df[price_column] = pd.to_numeric(df[price_column], errors='raise')
    except ValueError:
        raise ValueError(f"价格列 '{price_column}' 包含非数值数据")
    
    # 选择目标DataFrame
    target_df = df if inplace else df.copy()
    
    # 计算布林带
    # 中轨：简单移动平均线
    target_df['BB_middle'] = target_df[price_column].rolling(window=period).mean()
    
    # 标准差
    rolling_std = target_df[price_column].rolling(window=period).std()
    
    # 上轨和下轨
    target_df['BB_upper'] = target_df['BB_middle'] + (rolling_std * std_dev)
    target_df['BB_down'] = target_df['BB_middle'] - (rolling_std * std_dev)
    
    # 计算布林带宽度和位置
    target_df['BB_width'] = target_df['BB_upper'] - target_df['BB_down']
    target_df['BB_position'] = ((target_df[price_column] - target_df['BB_down']) /
                                         target_df['BB_width']) * 100
    
    if not inplace:
        return target_df
    
    return None


def calculate_adx(df: pd.DataFrame, period: int = 14, price_columns: dict = None,
                  inplace: bool = True) -> Optional[pd.DataFrame]:
    """
    计算平均方向指数 (ADX)、正方向指标 (+DI) 和负方向指标 (-DI)
    
    Parameters:
    -----------
    df : pd.DataFrame
        包含价格数据的DataFrame
    period : int, default 14
        ADX计算周期
    price_columns : dict, default None
        价格列名字典，格式：{'high': 'high', 'low': 'low', 'close': 'close'}
        如果为None，使用默认列名
    inplace : bool, default True
        是否直接修改原DataFrame
    
    Returns:
    --------
    pd.DataFrame or None
        如果inplace=False，返回修改后的DataFrame；否则返回None
        添加的列：'+DI', '-DI', 'ADX'
    
    Raises:
    -------
    ValueError: 当数据不足或参数错误时抛出异常
    """
    # 默认列名
    if price_columns is None:
        price_columns = {'high': 'high', 'low': 'low', 'close': 'close'}
    
    # 数据验证
    if df.empty:
        raise ValueError("DataFrame不能为空")
    
    required_columns = ['high', 'low', 'close']
    for col_type in required_columns:
        col_name = price_columns.get(col_type, col_type)
        if col_name not in df.columns:
            raise ValueError(f"列 '{col_name}' 不存在于DataFrame中")
    
    # 检查数据量是否足够（ADX需要更多数据来稳定）
    min_required = period * 2
    if len(df) < min_required:
        raise ValueError(f"数据量不足：需要至少 {min_required} 条数据，当前只有 {len(df)} 条")
    
    # 参数验证
    if period <= 0:
        raise ValueError(f"周期必须为正数，当前为: {period}")
    
    # 检查价格数据是否有效
    for col_type in required_columns:
        col_name = price_columns.get(col_type, col_type)
        if df[col_name].isnull().any():
            raise ValueError(f"价格列 '{col_name}' 包含空值")
        
        try:
            df[col_name] = pd.to_numeric(df[col_name], errors='raise')
        except ValueError:
            raise ValueError(f"价格列 '{col_name}' 包含非数值数据")
    
    # 选择目标DataFrame
    target_df = df if inplace else df.copy()
    
    # 获取列名
    high_col = price_columns['high']
    low_col = price_columns['low']
    close_col = price_columns['close']
    
    # 计算True Range (TR)
    target_df['H-L'] = target_df[high_col] - target_df[low_col]
    target_df['H-PC'] = abs(target_df[high_col] - target_df[close_col].shift(1))
    target_df['L-PC'] = abs(target_df[low_col] - target_df[close_col].shift(1))
    target_df['TR'] = target_df[['H-L', 'H-PC', 'L-PC']].max(axis=1)
    
    # 计算方向移动 (Directional Movement)
    target_df['H-PH'] = target_df[high_col] - target_df[high_col].shift(1)
    target_df['PL-L'] = target_df[low_col].shift(1) - target_df[low_col]
    
    # 计算+DM和-DM
    target_df['+DM'] = np.where(
        (target_df['H-PH'] > target_df['PL-L']) & (target_df['H-PH'] > 0),
        target_df['H-PH'], 0
    )
    target_df['-DM'] = np.where(
        (target_df['PL-L'] > target_df['H-PH']) & (target_df['PL-L'] > 0),
        target_df['PL-L'], 0
    )
    
    # 计算平滑的TR、+DM、-DM
    target_df['TR_smooth'] = target_df['TR'].ewm(span=period, adjust=False).mean()
    target_df['+DM_smooth'] = target_df['+DM'].ewm(span=period, adjust=False).mean()
    target_df['-DM_smooth'] = target_df['-DM'].ewm(span=period, adjust=False).mean()
    
    # 计算方向指标 (+DI, -DI) - 这些列将被保留
    target_df['+DI'] = 100 * (target_df['+DM_smooth'] / target_df['TR_smooth'])
    target_df['-DI'] = 100 * (target_df['-DM_smooth'] / target_df['TR_smooth'])
    
    # 计算DX
    target_df['DX'] = 100 * (abs(target_df['+DI'] - target_df['-DI']) /
                             (target_df['+DI'] + target_df['-DI']))
    
    # 计算ADX
    target_df['ADX'] = target_df['DX'].ewm(span=period, adjust=False).mean()
    
    # 清理临时列（注意：+DI和-DI不在清理列表中）
    temp_columns = ['H-L', 'H-PC', 'L-PC', 'TR', 'H-PH', 'PL-L', '+DM', '-DM',
                    'TR_smooth', '+DM_smooth', '-DM_smooth', 'DX']
    target_df.drop(columns=temp_columns, inplace=True)
    
    if not inplace:
        return target_df
    
    return None


def apply_all_indicators(df: pd.DataFrame, 
                        ema_periods: list = [10, 20, 50],
                        bb_period: int = 20,
                        bb_std_dev: float = 2,
                        adx_period: int = 14,
                        price_columns: dict = None,
                        inplace: bool = True) -> Optional[pd.DataFrame]:
    """
    一次性应用所有技术指标
    
    Parameters:
    -----------
    df : pd.DataFrame
        包含价格数据的DataFrame
    ema_periods : list, default [10, 20, 50]
        EMA周期列表
    bb_period : int, default 20
        布林带周期
    bb_std_dev : float, default 2
        布林带标准差倍数
    adx_period : int, default 14
        ADX周期
    price_columns : dict, default None
        价格列名字典
    inplace : bool, default True
        是否直接修改原DataFrame
    
    Returns:
    --------
    pd.DataFrame or None
        如果inplace=False，返回修改后的DataFrame；否则返回None
    """
    # 选择目标DataFrame
    target_df = df if inplace else df.copy()
    
    # 应用EMA
    calculate_ema(target_df, periods=ema_periods, inplace=True)
    
    # 应用布林带
    calculate_bollinger_bands(target_df, period=bb_period, std_dev=bb_std_dev, inplace=True)
    
    # 应用ADX
    calculate_adx(target_df, period=adx_period, price_columns=price_columns, inplace=True)
    
    if not inplace:
        return target_df
    
    return None




def mark_invalid_indicators(df: pd.DataFrame, ema_periods: list = [10, 20, 50], 
                           bb_period: int = 20, adx_period: int = 14, inplace: bool = True) -> Optional[pd.DataFrame]:
    """标记技术指标的无效区域
    
    参数:
    df (pd.DataFrame): 包含技术指标的DataFrame
    ema_periods (list): EMA计算周期列表
    bb_period (int): 布林带计算周期  
    adx_period (int): ADX计算周期
    inplace (bool): 是否直接修改原DataFrame
    
    返回:
    pd.DataFrame or None: 如果inplace=False，返回修改后的DataFrame
    """
    target_df = df if inplace else df.copy()
    
    # 计算各指标的有效起始位置
    max_ema_period = max(ema_periods)
    ema_valid_start = max_ema_period - 1
    bb_valid_start = bb_period - 1
    adx_valid_start = adx_period * 2 - 1
    
    # 标记EMA指标无效区域
    ema_columns = ['ema_kuai', 'ema_zhong', 'ema_man']
    for col in ema_columns:
        if col in target_df.columns:
            target_df.loc[:ema_valid_start-1, col] = np.nan
    
    # 标记布林带指标无效区域
    bb_columns = ['BB_upper', 'BB_down', 'BB_middle']
    for col in bb_columns:
        if col in target_df.columns:
            target_df.loc[:bb_valid_start-1, col] = np.nan
    
    # 标记ADX指标无效区域
    adx_columns = ['ADX', '+DI', '-DI']
    for col in adx_columns:
        if col in target_df.columns:
            target_df.loc[:adx_valid_start-1, col] = np.nan
    
    if not inplace:
        return target_df
    
    return None


def print_validity_report(df: pd.DataFrame, ema_periods: list = [10, 20, 50], 
                         bb_period: int = 20, adx_period: int = 14):
    """打印指标有效性报告
    
    参数:
    df (pd.DataFrame): 包含技术指标的DataFrame
    ema_periods (list): EMA计算周期列表
    bb_period (int): 布林带计算周期
    adx_period (int): ADX计算周期
    """
    total_bars = len(df)
    max_ema_period = max(ema_periods)
    
    ema_valid_start = max_ema_period - 1
    bb_valid_start = bb_period - 1
    adx_valid_start = adx_period * 2 - 1
    
    ema_valid_count = max(0, total_bars - ema_valid_start)
    bb_valid_count = max(0, total_bars - bb_valid_start)
    adx_valid_count = max(0, total_bars - adx_valid_start)
    
    print("=" * 50)
    print("技术指标有效性报告")
    print("=" * 50)
    print(f"总K线数量: {total_bars}")
    print()
    
    print(f"EMA指标 (周期: {ema_periods}):")
    print(f"  无效K线: 前{ema_valid_start}根")
    print(f"  有效K线: {ema_valid_count}根")
    print(f"  有效起始位置: 第{ema_valid_start + 1}根K线")
    print()
    
    print(f"布林带指标 (周期: {bb_period}):")
    print(f"  无效K线: 前{bb_valid_start}根")
    print(f"  有效K线: {bb_valid_count}根")
    print(f"  有效起始位置: 第{bb_valid_start + 1}根K线")
    print()
    
    print(f"ADX指标 (周期: {adx_period}):")
    print(f"  无效K线: 前{adx_valid_start}根")
    print(f"  有效K线: {adx_valid_count}根")
    print(f"  有效起始位置: 第{adx_valid_start + 1}根K线")
    print("=" * 50)


def filter_valid_data(df: pd.DataFrame, indicator_type: str = 'all') -> pd.DataFrame:
    """过滤出有效的指标数据
    
    参数:
    df (pd.DataFrame): 包含技术指标的DataFrame
    indicator_type (str): 指标类型 ('ema', 'bb', 'adx', 'all')
    
    返回:
    pd.DataFrame: 过滤后的DataFrame
    """
    if indicator_type == 'ema':
        ema_cols = ['ema_kuai', 'ema_zhong', 'ema_man']
        existing_cols = [col for col in ema_cols if col in df.columns]
        mask = df[existing_cols].notna().all(axis=1)
        
    elif indicator_type == 'bb':
        bb_cols = ['BB_upper', 'BB_down']
        existing_cols = [col for col in bb_cols if col in df.columns]
        mask = df[existing_cols].notna().all(axis=1)
        
    elif indicator_type == 'adx':
        adx_cols = ['ADX', '+DI', '-DI']
        existing_cols = [col for col in adx_cols if col in df.columns]
        mask = df[existing_cols].notna().all(axis=1)
    
    elif indicator_type == 'di_enhanced':
        # DI增强指标（差值和比值）
        di_enhanced_cols = ['DI_Difference', 'DI_Ratio', 'DI_Direction', 'DI_Strength']
        existing_cols = [col for col in di_enhanced_cols if col in df.columns]
        mask = df[existing_cols].notna().all(axis=1)
        
    else:  # 'all'
        all_indicator_cols = ['ema_kuai', 'ema_zhong', 'ema_man', 
                     'BB_upper', 'BB_down', 'ADX', '+DI', '-DI',
                     'DI_Difference', 'DI_Ratio', 'DI_Direction', 'DI_Strength']
        existing_cols = [col for col in all_indicator_cols if col in df.columns]
        mask = df[existing_cols].notna().all(axis=1)
    
    return df[mask].copy()



def create_technical_charts(df, path_png=r"C:\Users\<USER>\Desktop\my_python\claudeV1\量化\可视化\png", 
                           filter_invalid=True):
    """创建技术指标可视化图表
    
    参数:
    df (pd.DataFrame): 包含价格和指标数据的DataFrame
    path_png (str): 图片保存路径
    filter_invalid (bool): 是否过滤无效数据
    """
    # 确保目录存在
    try:
        os.makedirs(path_png, exist_ok=True)
        print(f"图片将保存到: {path_png}")
    except OSError:
        print(f"错误：无法创建目录 {path_png}")
        return
    
    # 设置matplotlib为非交互模式
    plt.ioff()  # 关闭交互模式
    plt.rcParams['axes.unicode_minus'] = False
    
    # 如果需要过滤无效数据
    if filter_invalid:
        plot_df = filter_valid_data(df, indicator_type='all')
        print(f"过滤后有效数据: {len(plot_df)}行 (原始数据: {len(df)}行)")
    else:
        plot_df = df
    
    # 创建四个图表
    try:
        _create_ema_chart(plot_df, path_png)
        print("✓ EMA图表已保存")
        
        _create_bb_chart(plot_df, path_png)
        print("✓ 布林带图表已保存")
        
        _create_adx_chart(plot_df, path_png)
        print("✓ ADX图表已保存")
        
        _create_di_ratio_chart(plot_df, path_png)
        print("✓ DI比值图表已保存")
        
        _create_di_difference_chart(plot_df, path_png)
        print("✓ DI差值图表已保存")
        
        print("所有图表（5张）已成功保存到本地")
        
    except Exception as e:
        print(f"生成图表时出错: {e}")


def _create_ema_chart(df, path_png):
    """创建EMA指标图表"""
    fig, ax1 = plt.subplots(figsize=(12, 8))
    
    # 左轴：价格
    ax1.set_ylabel('Price', color='black')
    ax1.plot(df.index, df['close'], label='close', color='black', linewidth=2)
    
    # 检查EMA列是否存在
    if 'ema_man' in df.columns:
        ax1.plot(df.index, df['ema_man'], label='ema_man', color='blue', alpha=0.8)
    if 'ema_zhong' in df.columns:
        ax1.plot(df.index, df['ema_zhong'], label='ema_zhong', color='orange', alpha=0.8)
    if 'ema_kuai' in df.columns:
        ax1.plot(df.index, df['ema_kuai'], label='ema_kuai', color='red', alpha=0.8)
    
    ax1.tick_params(axis='y', labelcolor='black')
    ax1.grid(True, alpha=0.3)
    ax1.legend(loc='upper left')
    
    plt.title('EMA Indicator and Price Chart', fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    # 保存图片
    save_path = os.path.join(path_png, 'EMA_Chart.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()  # 关闭图形以释放内存


def _create_bb_chart(df, path_png):
    """创建布林带指标图表"""
    fig, ax1 = plt.subplots(figsize=(12, 8))
    
    # 左轴：价格
    ax1.set_ylabel('Price', color='black')
    ax1.plot(df.index, df['close'], label='close', color='black', linewidth=2)
    
    # 检查布林带列是否存在
    if 'BB_upper' in df.columns and 'BB_down' in df.columns:
        ax1.plot(df.index, df['BB_upper'], label='BB_upper', color='red', alpha=0.7)
        ax1.plot(df.index, df['BB_down'], label='BB_down', color='green', alpha=0.7)
        
        # 填充布林带区域
        ax1.fill_between(df.index, df['BB_upper'], df['BB_down'], 
                         alpha=0.2, color='gray', label='BB_zone')
    
    ax1.tick_params(axis='y', labelcolor='black')
    ax1.grid(True, alpha=0.3)
    ax1.legend(loc='upper left')
    
    plt.title('Bollinger Bands and Price Chart', fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    # 保存图片
    save_path = os.path.join(path_png, 'BB_Chart.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()  # 关闭图形以释放内存


def _create_adx_chart(df, path_png):
    """创建ADX指标图表（仅显示ADX，不显示+DI和-DI）"""
    fig, ax1 = plt.subplots(figsize=(12, 8))
    
    # 左轴：价格
    ax1.set_ylabel('Price', color='black')
    ax1.plot(df.index, df['close'], label='close', color='black', linewidth=2)
    ax1.tick_params(axis='y', labelcolor='black')
    ax1.grid(True, alpha=0.3)
    
    # 右轴：仅显示ADX指标
    ax2 = ax1.twinx()
    ax2.set_ylabel('ADX Values', color='blue')
    
    # 只检查和绘制ADX
    if 'ADX' in df.columns:
        ax2.plot(df.index, df['ADX'], label='ADX', color='blue', linewidth=2)
    
    ax2.tick_params(axis='y', labelcolor='blue')
    
    # 添加ADX参考线
    ax2.axhline(y=25, color='gray', linestyle='--', alpha=0.5, label='Strong Trend(25)')
    ax2.axhline(y=50, color='gray', linestyle='--', alpha=0.5, label='Very Strong(50)')
    
    # 合并图例
    lines1, labels1 = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
    
    plt.title('ADX Indicator and Price Chart', fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    # 保存图片
    save_path = os.path.join(path_png, 'ADX_Chart.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()  # 关闭图形以释放内存

def _create_di_ratio_chart(df, path_png):
    """创建DI比值指标图表（改进版：使用颜色区分方向）
    
    参数:
    df (pd.DataFrame): 包含价格和指标数据的DataFrame
    path_png (str): 图片保存路径
    """
    fig, ax1 = plt.subplots(figsize=(12, 8))
    
    # 左轴：价格
    ax1.set_ylabel('Price', color='black')
    ax1.plot(df.index, df['close'], label='close', color='black', linewidth=2)
    ax1.tick_params(axis='y', labelcolor='black')
    ax1.grid(True, alpha=0.3)
    
    # 右轴：DI比值
    ax2 = ax1.twinx()
    ax2.set_ylabel('DI Ratio', color='purple')
    
    # 检查DI列是否存在并计算比值
    if '+DI' in df.columns and '-DI' in df.columns:
        plus_di = df['+DI']
        minus_di = df['-DI']
        
        # 计算比值和方向
        bullish_ratios = []  # 看涨比值（+DI > -DI时）
        bearish_ratios = []  # 看跌比值（-DI > +DI时）
        bullish_indices = []
        bearish_indices = []
        
        for i in range(len(plus_di)):
            if plus_di.iloc[i] >= minus_di.iloc[i]:
                # 看涨：+DI >= -DI，计算+DI/-DI
                if minus_di.iloc[i] < 0.1:
                    ratio = min(5.0, plus_di.iloc[i] / 0.1)
                else:
                    ratio = min(5.0, plus_di.iloc[i] / minus_di.iloc[i])
                bullish_ratios.append(ratio)
                bullish_indices.append(df.index[i])
                bearish_ratios.append(None)
                bearish_indices.append(df.index[i])
            else:
                # 看跌：-DI > +DI，计算-DI/+DI
                if plus_di.iloc[i] < 0.1:
                    ratio = min(5.0, minus_di.iloc[i] / 0.1)
                else:
                    ratio = min(5.0, minus_di.iloc[i] / plus_di.iloc[i])
                bearish_ratios.append(ratio)
                bearish_indices.append(df.index[i])
                bullish_ratios.append(None)
                bullish_indices.append(df.index[i])
        
        # 绘制不同颜色的比值线
        ax2.plot(bullish_indices, bullish_ratios, label='+DI/-DI (Bullish)', 
                color='green', linewidth=2, alpha=0.8)
        ax2.plot(bearish_indices, bearish_ratios, label='-DI/+DI (Bearish)', 
                color='red', linewidth=2, alpha=0.8)
        
        # 添加参考线
        ax2.axhline(y=1.0, color='gray', linestyle='-', alpha=0.7, label='Neutral(1.0)')
        ax2.axhline(y=1.5, color='orange', linestyle='--', alpha=0.5, label='Moderate(1.5)')
        ax2.axhline(y=2.0, color='blue', linestyle='--', alpha=0.5, label='Strong(2.0)')
        ax2.axhline(y=3.0, color='purple', linestyle='--', alpha=0.5, label='Very Strong(3.0)')
        
        # 设置Y轴范围
        ax2.set_ylim(0, 5)
    
    ax2.tick_params(axis='y', labelcolor='purple')
    
    # 合并图例
    lines1, labels1 = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
    
    plt.title('DI Ratio Chart with Direction Colors', fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    # 保存图片
    save_path = os.path.join(path_png, 'DI_Ratio_Chart.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()


def _create_di_difference_chart(df, path_png):
    """创建DI差值指标图表
    
    参数:
    df (pd.DataFrame): 包含价格和指标数据的DataFrame
    path_png (str): 图片保存路径
    """
    fig, ax1 = plt.subplots(figsize=(12, 8))
    
    # 左轴：价格
    ax1.set_ylabel('Price', color='black')
    ax1.plot(df.index, df['close'], label='close', color='black', linewidth=2)
    ax1.tick_params(axis='y', labelcolor='black')
    ax1.grid(True, alpha=0.3)
    
    # 右轴：DI差值
    ax2 = ax1.twinx()
    ax2.set_ylabel('DI Difference', color='blue')
    
    # 检查DI列是否存在并计算差值
    if '+DI' in df.columns and '-DI' in df.columns:
        # 计算+DI - (-DI)的差值
        di_difference = df['+DI'] - df['-DI']
        
        # 根据差值正负使用不同颜色
        positive_diff = di_difference.where(di_difference >= 0)
        negative_diff = di_difference.where(di_difference < 0)
        
        # 绘制差值线
        ax2.plot(df.index, positive_diff, label='+DI > -DI (Bullish)', 
                color='green', linewidth=2, alpha=0.8)
        ax2.plot(df.index, negative_diff, label='+DI < -DI (Bearish)', 
                color='red', linewidth=2, alpha=0.8)
        
        # 填充区域
        ax2.fill_between(df.index, 0, positive_diff, where=(positive_diff >= 0), 
                        color='green', alpha=0.2, interpolate=True)
        ax2.fill_between(df.index, 0, negative_diff, where=(negative_diff < 0), 
                        color='red', alpha=0.2, interpolate=True)
        
        # 添加参考线
        ax2.axhline(y=0, color='gray', linestyle='-', alpha=0.7, label='Neutral(0)')
        ax2.axhline(y=5, color='green', linestyle='--', alpha=0.5, label='Strong Bull(+5)')
        ax2.axhline(y=-5, color='red', linestyle='--', alpha=0.5, label='Strong Bear(-5)')
        ax2.axhline(y=10, color='darkgreen', linestyle='--', alpha=0.5, label='Very Strong Bull(+10)')
        ax2.axhline(y=-10, color='darkred', linestyle='--', alpha=0.5, label='Very Strong Bear(-10)')
    
    ax2.tick_params(axis='y', labelcolor='blue')
    
    # 合并图例
    lines1, labels1 = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
    
    plt.title('DI Difference Chart (+DI minus -DI)', fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    # 保存图片
    save_path = os.path.join(path_png, 'DI_Difference_Chart.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

def calculate_di_enhanced_indicators(df: pd.DataFrame, inplace: bool = True) -> Optional[pd.DataFrame]:
    # 数据验证
    if df.empty:
        raise ValueError("DataFrame不能为空")
    
    required_columns = ['+DI', '-DI']
    for col in required_columns:
        if col not in df.columns:
            raise ValueError(f"列 '{col}' 不存在于DataFrame中")
    
    # 选择目标DataFrame
    target_df = df if inplace else df.copy()
    
    # 获取DI数据
    plus_di = target_df['+DI']
    minus_di = target_df['-DI']
    
    # 计算DI差值
    target_df['DI_Difference'] = plus_di - minus_di
    
    # 计算智能DI比值和方向
    di_ratio = []
    di_direction = []
    
    for i in range(len(plus_di)):
        if plus_di.iloc[i] >= minus_di.iloc[i]:
            # 看涨：+DI >= -DI
            direction = 1
            if minus_di.iloc[i] < 0.1:
                ratio = min(5.0, plus_di.iloc[i] / 0.1)
            else:
                ratio = min(5.0, plus_di.iloc[i] / minus_di.iloc[i])
        else:
            # 看跌：-DI > +DI
            direction = -1
            if plus_di.iloc[i] < 0.1:
                ratio = min(5.0, minus_di.iloc[i] / 0.1)
            else:
                ratio = min(5.0, minus_di.iloc[i] / plus_di.iloc[i])
        
        di_ratio.append(ratio)
        di_direction.append(direction)
    
    target_df['DI_Ratio'] = di_ratio
    target_df['DI_Direction'] = di_direction
    
    # 计算趋势强度分类
    def classify_strength(ratio, difference):
        """分类趋势强度"""
        abs_diff = abs(difference)
        
        if ratio >= 3.0 or abs_diff >= 15:
            return "极强"
        elif ratio >= 2.0 or abs_diff >= 10:
            return "强"
        elif ratio >= 1.5 or abs_diff >= 5:
            return "中等"
        elif ratio >= 1.2 or abs_diff >= 2:
            return "弱"
        else:
            return "震荡"
    
    target_df['DI_Strength'] = [
        classify_strength(ratio, diff) 
        for ratio, diff in zip(target_df['DI_Ratio'], target_df['DI_Difference'])
    ]
    
    if not inplace:
        return target_df
    
    return None

def main():
    print("=== OKX交易所SDK测试 ===\n")
    try:
        dotenv_path = r'C:\Users\<USER>\Desktop\my_python\claudeV1\量化\123.env'
        client = OKXClient(dotenv_path=dotenv_path)
    except Exception as e:
        print(f"初始化失败: {e}")
        return

    symbol = 'DOGE-USDT-SWAP'
    timeframe = '15m'
    counts = 250
    
    print("\n1. 测试获取最新K线数据")
    print("-" * 30)
    df = get_kline_data(client, symbol, timeframe=timeframe, count=counts)
    df = df.iloc[:-1]
    
    if not df.empty:
        print("原始数据:")
        print(df.iloc[:,:6].tail())
        
        print("\n2. 计算技术指标")
        print("-" * 30)
        try:
            # 定义指标参数
            ema_periods = [10, 20, 50]
            ema_names = ['ema_kuai', 'ema_zhong', 'ema_man']
            bb_period = 20
            adx_period = 14

            # 计算指标
            calculate_ema(df, periods=ema_periods, names=ema_names)
            calculate_bollinger_bands(df, period=bb_period, std_dev=2)
            calculate_adx(df, period=adx_period)

            print("技术指标计算完成")
            
            # 显示结果
            print("添加指标后的数据:")
            print(df[['close', 'ema_man', 'ema_zhong', 'ema_kuai', 'BB_upper', 'BB_down', 'ADX','+DI', '-DI']].tail())
            
        except Exception as e:
            print(f"计算技术指标时出错: {e}")
            return
        
        print("\n2.5. 计算DI增强指标")
        print("-" * 30)

        # 计算并添加DI比值和差值到DataFrame
        if '+DI' in df.columns and '-DI' in df.columns:
            calculate_di_enhanced_indicators(df, inplace=True)
            print("DI增强指标计算完成")
            
            # 显示增强后的数据
            print("\n增强指标数据样本:")
            enhanced_cols = ['close', '+DI', '-DI', 'DI_Difference', 'DI_Ratio', 'DI_Direction', 'DI_Strength']
            print(df[enhanced_cols].tail())
        else:
            print("警告：缺少+DI或-DI数据，跳过DI增强指标计算")
                

        
        print("\n3. 指标有效性分析")
        print("-" * 30)
        print_validity_report(df, ema_periods=[10, 20, 50], bb_period=20, adx_period=14)
        
        print("\n4. 标记无效数据")
        print("-" * 30)
        mark_invalid_indicators(df, ema_periods=[10, 20, 50], bb_period=20, adx_period=14)
        
        print("\n5. 生成可视化图表")
        print("-" * 30)
        custom_path = r"C:\Users\<USER>\Desktop\my_python\claudeV1\量化\可视化\png"
        create_technical_charts(df, path_png=custom_path, filter_invalid=True)
        
        print("\n6. 最终数据统计")
        print("-" * 30)
        # 获取有效数据统计
        valid_df = filter_valid_data(df, indicator_type='all')
        print(f"原始数据行数: {len(df)}")
        print(f"有效数据行数: {len(valid_df)}")
        print(f"数据有效率: {len(valid_df)/len(df)*100:.1f}%")
        
        
        # 显示包含DI增强指标的完整数据
        # enhanced_cols = ['close', 'ema_man', 'ema_zhong', 'ema_kuai', 'BB_upper', 'BB_down', 
        #                 'ADX', '+DI', '-DI', 'DI_Difference', 'DI_Ratio', 'DI_Direction', 'DI_Strength']
        enhanced_cols = ['close',
                        'ADX', '+DI', '-DI', 'DI_Difference', 'DI_Ratio', 'DI_Direction', 'DI_Strength']
        existing_enhanced_cols = [col for col in enhanced_cols if col in valid_df.columns]
        print(f"\n7. 最终有效数据样本（包含DI增强指标）:")
        print(valid_df[existing_enhanced_cols].tail())
        # print(valid_df[existing_enhanced_cols])
        
        stats=analyze_numerical_series(valid_df['ADX'])    
        print("正态分布置信区间1σ区:", stats['conf_interval_1sigma'], "数量:", stats['n_1sigma'])
        print("正态分布置信区间2σ区:", stats['conf_interval_2sigma'], "数量:", stats['n_2sigma'])
        print("正态分布置信区间3σ区:", stats['conf_interval_3sigma'], "数量:", stats['n_3sigma'])   
                
        stats=analyze_numerical_series(valid_df['DI_Difference'])    
        print("正态分布置信区间1σ区:", stats['conf_interval_1sigma'], "数量:", stats['n_1sigma'])
        print("正态分布置信区间2σ区:", stats['conf_interval_2sigma'], "数量:", stats['n_2sigma'])
        print("正态分布置信区间3σ区:", stats['conf_interval_3sigma'], "数量:", stats['n_3sigma'])   
        stats=analyze_numerical_series(valid_df['DI_Ratio'])    
        print("正态分布置信区间1σ区:", stats['conf_interval_1sigma'], "数量:", stats['n_1sigma'])
        print("正态分布置信区间2σ区:", stats['conf_interval_2sigma'], "数量:", stats['n_2sigma'])
        print("正态分布置信区间3σ区:", stats['conf_interval_3sigma'], "数量:", stats['n_3sigma'])   

    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()