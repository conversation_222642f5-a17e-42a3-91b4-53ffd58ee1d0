#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
参数优化回测脚本 - 贝叶斯优化版本

使用 scikit-optimize 库进行高效的参数优化。
相比穷举法，能够用更少的回测次数找到更好的参数组合。
"""

import numpy as np
import pandas as pd
import time
from typing import Dict, List, Tuple, Any
import warnings
import os
import pickle
from multiprocessing import Pool, cpu_count
from functools import partial
from tqdm import tqdm
from itertools import product
from dataclasses import dataclass
import gc
import sys

# --- 猴子补丁开始 ---
if not hasattr(np, 'NaN'):
    np.NaN = np.nan
# --- 猴子补丁结束 ---

# --- 导入贝叶斯优化库 ---
from skopt import gp_minimize
from skopt.space import Real, Integer
from skopt.utils import use_named_args
from skopt.space import Categorical

warnings.filterwarnings('ignore')

# 导入必要的模块和函数 (请确保这些文件在当前目录下)
try:
    import pandas_ta as ta
    from huice import sub_kline_time, write_analysis_to_kline, batch_judge_trade
    from ku import (smooth_rsi_with_gaussian, get_local_kline_data,
                    fast_rsi_calculation, fast_gaussian_smooth, fast_rsi_numpy)
    from config import calculate_kline_needed
    import trend_analysis_processorV2
    from tradeing import check_trading_single, judge_signal, calc_price
    from datetime import datetime, timedelta
    import re
    # 尝试导入可视化库和openpyxl
    try:
        import matplotlib.pyplot as plt
        import seaborn as sns
        import openpyxl
        VISUALIZATION_AVAILABLE = True
        EXCEL_AVAILABLE = True
    except ImportError:
        VISUALIZATION_AVAILABLE = False
        EXCEL_AVAILABLE = False
        print("警告: 无法导入 matplotlib, seaborn 或 openpyxl。部分功能将不可用。")

except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保以下模块文件 (huice.py, ku.py, config.py, trend_analysis_processorV2.py, tradeing.py) 在当前目录或Python路径中。")
    # 为了让 skopt 能够正常运行，需要将导入失败的模块路径添加到 sys.path
    # 假设您的模块都在 biquan/geminiCode/极值量化 目录下
    module_dir = os.path.join(os.path.dirname(__file__), '..', '..', '..')
    if os.path.exists(module_dir) and module_dir not in sys.path:
        sys.path.append(module_dir)
        print(f"已将 '{module_dir}' 添加到 Python 路径。请再次尝试运行。")
    exit(1)


@dataclass
class TrendConfig:
    """
    趋势分析配置，使用 dataclass 结构化参数。
    """
    extrema_sensitivity: float = 0.30
    extrema_credibility_threshold: float = 42.0
    high_credibility_threshold: float = 75.0
    drawdown_threshold: float = -1.5
    severe_drawdown_threshold: float = -8.0
    trend_consistency_threshold: float = 70.0
    trend_strength_strong_threshold: float = 4.0
    trend_strength_medium_threshold: float = 2.0
    
def calculate_trade_metrics(kline_df: pd.DataFrame) -> Dict[str, float]:
    """
    计算并返回回测的各项关键指标。
    """
    pct_series = pd.to_numeric(kline_df['达成百分比'], errors='coerce').fillna(0)
    valid_pct = pct_series[pct_series != 0]
    
    if valid_pct.empty:
        return {
            'profit': 0.0,
            'max_drawdown': 0.0,
            'sharpe_ratio': 0.0,
            'calmar_ratio': 0.0,
            'num_trades': 0
        }
        
    total_profit = valid_pct.sum()
    
    cumulative_returns = (1 + valid_pct / 100).cumprod()
    peak = cumulative_returns.cummax()
    drawdown = (cumulative_returns - peak) / peak
    max_drawdown = abs(drawdown.min()) * 100 if not drawdown.empty else 0
    
    daily_returns = (valid_pct / 100).to_numpy()
    if daily_returns.std() == 0:
        sharpe_ratio = 0.0
    else:
        sharpe_ratio = daily_returns.mean() / daily_returns.std()
        
    calmar_ratio = total_profit / max_drawdown if max_drawdown > 0 else 0.0
    
    return {
        'profit': total_profit,
        'max_drawdown': max_drawdown,
        'sharpe_ratio': sharpe_ratio,
        'calmar_ratio': calmar_ratio,
        'num_trades': len(valid_pct)
    }

def run_single_backtest_optimized(kline_df: pd.DataFrame, params: Dict[str, Any], base_my_pram: Dict[str, Any], base_config_15K: TrendConfig, needed_klines: int) -> pd.DataFrame:
    """
    运行单次回测，返回带有交易结果的 DataFrame。
    """
    my_pram = base_my_pram.copy()
    for key, value in params.items():
        if key in my_pram:
            my_pram[key] = value

    config_dict = base_config_15K.__dict__.copy()
    for key, value in params.items():
        if key in config_dict:
            config_dict[key] = value
    config_15K = TrendConfig(**config_dict)
    
    analyzer = trend_analysis_processorV2.TrendAnalyzer(config_15K)
    verifier = trend_analysis_processorV2.UniqueTrendVerifier(config_15K)
    
    df_length = len(kline_df)
    kline_df_with_analysis = kline_df.copy()
    
    invest_advice = np.full(df_length, '', dtype=object)
    trading_signals = np.zeros(df_length, dtype=int)
    now_signals = np.zeros(df_length, dtype=int)
    buy_prices = np.full(df_length, np.nan, dtype=np.float64)
    
    tradingSingle = 0
    close_values = kline_df['close'].values
    
    # 提前计算 RSI 和 Gaussian 平滑值，减少循环内部的重复计算
    all_rsi = fast_rsi_numpy(close_values, my_pram["rsi_length"])
    all_gaussian_rsi = np.full(len(all_rsi), np.nan)
    valid_rsi_indices = ~np.isnan(all_rsi)
    if valid_rsi_indices.any():
        all_gaussian_rsi[valid_rsi_indices] = fast_gaussian_smooth(all_rsi[valid_rsi_indices], my_pram["my_gaussian_sigma"])
    
    for i in range(needed_klines, df_length):
        nowPrice = close_values[i]
        smooth_rsi_window = all_gaussian_rsi[i - needed_klines + 1 : i + 1]
        
        if len(smooth_rsi_window) < 8:
            continue
            
        result = analyzer.analyze_trend(smooth_rsi_window.tolist(), params.get('malen', 10))
        analysis_result = verifier.verify_and_advise([result])
        
        nowSingle = judge_signal(analysis_result['建议操作'])
        tradingSingle, nowSingle = check_trading_single(tradingSingle, nowSingle)
        
        invest_advice[i] = analysis_result.get('建议操作', '')
        trading_signals[i] = tradingSingle
        now_signals[i] = nowSingle
        
        if nowSingle != 0:
            buy_prices[i] = calc_price(nowSingle, nowPrice, 0)
    
    kline_df_with_analysis['RSI'] = all_rsi
    kline_df_with_analysis['RSI_Gaussian_2.0'] = all_gaussian_rsi
    kline_df_with_analysis['投资建议'] = invest_advice
    kline_df_with_analysis['tradingSingle'] = trading_signals
    kline_df_with_analysis['nowSingle'] = now_signals
    kline_df_with_analysis['buyPrice'] = buy_prices
    
    kline_df_with_analysis = batch_judge_trade(kline_df_with_analysis, params['zhiyin'], params['zhisun'], my_pram['windowsTime'], my_pram["Ktime"])
    
    del invest_advice, trading_signals, now_signals, buy_prices, all_rsi, all_gaussian_rsi
    gc.collect()
    
    return kline_df_with_analysis

# ==================== 贝叶斯优化核心逻辑 ====================

def objective_function_wrapper_final(params_list, kline_df_shared, my_pram_shared, base_config_shared, needed_klines_shared, dimension_names_shared):
    """
    贝叶斯优化的目标函数。
    接收一组参数（列表形式），并返回我们想要最小化的指标。
    
    使用 functools.partial 来绑定共享数据，避免全局变量初始化问题。
    """
    # 将参数列表转换为字典
    # 这里的 `dimension_names_shared` 已经通过 partial 绑定，不会是 None
    params = dict(zip(dimension_names_shared, params_list))
    
    try:
        # 运行回测，获取完整的 DataFrame
        result_df = run_single_backtest_optimized(
            kline_df_shared, params, my_pram_shared, base_config_shared, needed_klines_shared
        )
        # 计算关键指标
        metrics = calculate_trade_metrics(result_df)
        
        # 贝叶斯优化器默认寻找最小值，所以我们返回夏普比率的负值
        objective_value = -metrics['sharpe_ratio']
        
        # 打印当前回测结果
        print(f"-> Params: {params} | Sharpe: {metrics['sharpe_ratio']:.4f} | Profit: {metrics['profit']:.2f}% | Drawdown: {metrics['max_drawdown']:.2f}%")
        
        return objective_value
    except Exception as e:
        import traceback
        print(f"参数组合 {params} 出错: {e}")
        print(traceback.format_exc())
        # 返回一个很大的正值，让优化器知道这个参数组合很差
        return 999999

# --- 新增：结果记录功能 ---
def save_results_to_excel(result_type: str, my_pram: Dict[str, Any], params: Dict[str, Any], metrics: Dict[str, Any], filename: str = 'optimization_results.xlsx'):
    """
    将最优/最差参数和指标保存到 Excel 文件。
    如果文件不存在则创建，如果存在则追加写入。
    如果xls字段不一就重新创建
    """
    if not EXCEL_AVAILABLE:
        print("警告: 无法保存到 Excel。请安装 openpyxl。")
        return
        
    # 定义表头
    base_headers = ['记录类型', '虚拟币', 'K线周期', '起始时间', '结束时间', '记录时间']
    param_headers = list(params.keys())
    metric_headers = list(metrics.keys())
    full_headers = base_headers + param_headers + metric_headers
    
    # 提取数据行
    record_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    data_row = [
        result_type,
        my_pram['Uname'],
        my_pram['Ktime'],
        my_pram['strTime'],
        my_pram['endTime'],
        record_time
    ]
    # 添加参数值
    for p_key in param_headers:
        data_row.append(params.get(p_key, '')) # 使用 .get() 防止参数缺失
    # 添加指标值
    for m_key in metric_headers:
        data_row.append(metrics.get(m_key, ''))
        
    # 检查文件是否存在
    if not os.path.exists(filename):
        # 文件不存在，创建新的 Workbook
        print(f"文件 '{filename}' 不存在，正在创建新文件...")
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = f"{my_pram['Uname']}_{my_pram['Ktime']}_Optimization"
        ws.append(full_headers)
        ws.append(data_row)
        wb.save(filename)
        print(f"已创建并写入新的 Excel 文件: {filename}")
        return

    # 文件存在，加载 Workbook
    try:
        wb = openpyxl.load_workbook(filename)
        # 尝试找到匹配的 Sheet
        target_sheet_name = f"{my_pram['Uname']}_{my_pram['Ktime']}_Optimization"
        if target_sheet_name in wb.sheetnames:
            ws = wb[target_sheet_name]
            # 检查表头是否匹配
            existing_headers = [cell.value for cell in ws[1]]
            if existing_headers == full_headers:
                # 表头匹配，直接追加
                ws.append(data_row)
                print(f"已将结果追加到现有 Sheet: '{target_sheet_name}'")
            else:
                # 表头不匹配，创建新的 Sheet
                print(f"现有 Sheet '{target_sheet_name}' 的字段不匹配，正在创建新的 Sheet...")
                new_sheet_name = f"{target_sheet_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                ws_new = wb.create_sheet(title=new_sheet_name)
                ws_new.append(full_headers)
                ws_new.append(data_row)
                print(f"已创建并写入新的 Sheet: '{new_sheet_name}'")
        else:
            # 没有找到匹配的 Sheet，创建新的
            print(f"未找到匹配的 Sheet，正在创建新 Sheet: '{target_sheet_name}'")
            ws = wb.create_sheet(title=target_sheet_name)
            ws.append(full_headers)
            ws.append(data_row)
            print(f"已创建并写入新的 Sheet: '{target_sheet_name}'")
            
        wb.save(filename)
        
    except Exception as e:
        print(f"保存 Excel 文件时发生错误: {e}")

def analyze_and_print_results_bo(result, kline_df_main, my_pram_main, base_config_main, needed_klines_main):
    """
    分析和打印贝叶斯优化的最终结果，并新增记录功能。
    """
    if result.x is None:
        print("优化失败，未找到有效结果。")
        return
        
    print("\n" + "="*100)
    print("📈 贝叶斯优化完成！")
    print("="*100)
    
    # 获取所有回测历史结果
    all_results_df = pd.DataFrame([
        {'params': dict(zip(result.space.dimension_names, p)), 'objective_value': f, 'sharpe_ratio': -f}
        for p, f in zip(result.x_iters, result.func_vals)
    ])
    
    # 过滤掉失败的组合 (objective_value > 999990)
    valid_results_df = all_results_df[all_results_df['objective_value'] < 999990].copy()
    valid_results_df['sharpe_ratio'] = -valid_results_df['objective_value']
    
    # 根据夏普比率排序
    sorted_df = valid_results_df.sort_values(by='sharpe_ratio', ascending=False)
    
    # 提取最优和最差组合的参数和指标
    if not sorted_df.empty:
        best_params_series = sorted_df.iloc[0]['params']
        worst_params_series = sorted_df.iloc[-1]['params']
    else:
        print("警告: 无效回测结果，无法提取最优和最差组合。")
        return

    # 获取最优参数组合的详细指标
    best_params_dict = dict(best_params_series)
    final_df_best = run_single_backtest_optimized(kline_df_main, best_params_dict, my_pram_main, base_config_main, needed_klines_main)
    final_metrics_best = calculate_trade_metrics(final_df_best)

    print("🥇 最优参数组合:")
    for param, value in best_params_dict.items():
        if isinstance(value, float):
            print(f"  - {param:<25}: {value:.4f}")
        else:
            print(f"  - {param:<25}: {value}")
            
    print(f"\n✨ 最佳夏普比率 (最优指标): {final_metrics_best['sharpe_ratio']:.4f}")
    
    print("\n📊 最优参数组合的回测详细指标:")
    print(f"  - 总收益率 (Profit)         : {final_metrics_best['profit']:.2f}%")
    print(f"  - 最大回撤 (Max Drawdown)   : {final_metrics_best['max_drawdown']:.2f}%")
    print(f"  - 交易次数 (Num Trades)     : {final_metrics_best['num_trades']}")
    print(f"  - 夏普比率 (Sharpe Ratio)   : {final_metrics_best['sharpe_ratio']:.4f}")
    print(f"  - 卡玛比率 (Calmar Ratio)   : {final_metrics_best['calmar_ratio']:.2f}")

    # 获取最差参数组合的详细指标
    worst_params_dict = dict(worst_params_series)
    final_df_worst = run_single_backtest_optimized(kline_df_main, worst_params_dict, my_pram_main, base_config_main, needed_klines_main)
    final_metrics_worst = calculate_trade_metrics(final_df_worst)
    
    print("\n❌ 最差参数组合:")
    for param, value in worst_params_dict.items():
        if isinstance(value, float):
            print(f"  - {param:<25}: {value:.4f}")
        else:
            print(f"  - {param:<25}: {value}")
            
    print("\n📊 最差参数组合的回测详细指标:")
    print(f"  - 总收益率 (Profit)         : {final_metrics_worst['profit']:.2f}%")
    print(f"  - 最大回撤 (Max Drawdown)   : {final_metrics_worst['max_drawdown']:.2f}%")
    print(f"  - 交易次数 (Num Trades)     : {final_metrics_worst['num_trades']}")
    print(f"  - 夏普比率 (Sharpe Ratio)   : {final_metrics_worst['sharpe_ratio']:.4f}")
    print(f"  - 卡玛比率 (Calmar Ratio)   : {final_metrics_worst['calmar_ratio']:.2f}")
    
    # --- 关键新增功能：保存结果到 Excel ---
    if EXCEL_AVAILABLE:
        print("\n📝 正在将结果保存到 Excel 文件...")
        # 保存最优结果
        save_results_to_excel(
            result_type='最优',
            my_pram=my_pram_main,
            params=best_params_dict,
            metrics=final_metrics_best
        )
        # 保存最差结果
        save_results_to_excel(
            result_type='最差',
            my_pram=my_pram_main,
            params=worst_params_dict,
            metrics=final_metrics_worst
        )

    # 可视化优化过程
    if VISUALIZATION_AVAILABLE:
        from skopt.plots import plot_convergence, plot_evaluations
        
        plot_convergence(result, yscale='linear')
        plt.title('贝叶斯优化收敛图', fontsize=16)
        plt.xlabel('回测次数', fontsize=12)
        plt.ylabel('最佳夏普比率负值', fontsize=12)
        plt.savefig('bayesian_optimization_convergence.png', dpi=300)
        plt.show()

        plot_evaluations(result, bins=20)
        plt.tight_layout()
        plt.savefig('bayesian_optimization_evaluations.png', dpi=300)
        plt.show()
        print("\n优化收敛图和参数评估图已保存。")
        
    # 保存所有历史结果
    all_results_df.to_csv("bayesian_optimization_history.csv", index=False, encoding='utf-8-sig', float_format='%.4f')
    print("\n所有回测历史记录已保存到: bayesian_optimization_history.csv")


if __name__ == "__main__":
    # --- 1. 基础参数配置 ---
    my_pram = {
        "rsi_length": 14,
        "macd_length": 26,
        "max_lookback_kline_count": 100,
        "my_sma_length": 5,
        "my_ema_length": 5,
        "my_gaussian_sigma": 2.0,
        "windowsTime": 16,
        "Uname": "doge",
        "Ktime": "15m",
        "strTime": "2024-5-1",
        "endTime": "2024-5-2"
    }
    
    # --- 2. 参数搜索空间定义 ---
    space_dimensions = [
        # 交易参数
        Categorical(np.linspace(1.0, 3.0, 21).tolist(), name="zhiyin"),  # 步长为0.1
        Categorical(np.round(np.linspace(0.1, 2.0, 20), 2).tolist(), name="zhisun"),  # 用Categorical替代Real
        Integer(8, 60, name="malen"),

        # 策略指标参数
        Integer(10, 20, name="rsi_length"),  # 整数优化
        Categorical(np.round(np.linspace(1.0, 3.0, 21), 2).tolist(), name="my_gaussian_sigma"),  # 用Categorical替代Real

        # TrendConfig 参数
        Categorical(np.round(np.linspace(0.2, 0.4, 21), 3).tolist(), name="extrema_sensitivity"),
        Integer(30, 50, name="extrema_credibility_threshold"),
        Integer(60, 90, name="high_credibility_threshold"),
        Categorical(np.round(np.linspace(-3.0, -1.0, 21), 2).tolist(), name="drawdown_threshold"),
        Categorical(np.round(np.linspace(-12.0, -5.0, 29), 2).tolist(), name="severe_drawdown_threshold"),
        Integer(60, 80, name="trend_consistency_threshold"),
        Categorical(np.round(np.linspace(3.0, 5.0, 21), 2).tolist(), name="trend_strength_strong_threshold"),
        Categorical(np.round(np.linspace(1.0, 3.0, 21), 2).tolist(), name="trend_strength_medium_threshold"),
    ]
    
    # --- 关键修复：修改打印逻辑以兼容 Categorical 类型 ---
    print("📈 参数搜索空间:")
    for dim in space_dimensions:
        if isinstance(dim, (Real, Integer)):
            # 对于 Real 和 Integer 类型，打印 low 和 high 属性
            print(f"  - {dim.name:<30}: [{dim.low}, {dim.high}] (Type: {type(dim)})")
        elif hasattr(dim, 'categories'):
            # 对于 Categorical 类型，打印 categories 属性
            print(f"  - {dim.name:<30}: {dim.categories[:3]}... ({len(dim.categories)} values) (Type: {type(dim)})")
        else:
            # 兼容其他未知类型
            print(f"  - {dim.name:<30}: Range Not Available (Type: {type(dim)})")
    
    # --- 3. 数据准备 ---
    base_config_15K = TrendConfig(
        extrema_sensitivity=0.30,
        extrema_credibility_threshold=42.0,
        high_credibility_threshold=75.0,
        drawdown_threshold=-1.5,
        severe_drawdown_threshold=-8.0,
        trend_consistency_threshold=70.0,
        trend_strength_strong_threshold=4.0,
        trend_strength_medium_threshold=2.0
    )
    
    needed_klines = calculate_kline_needed(my_pram)
    print(f"\n📈 策略分析所需的最小K线数量: {needed_klines}")
    strTime = sub_kline_time(my_pram["strTime"], needed_klines, my_pram["Ktime"])
    
    try:
        kline_df = get_local_kline_data(my_pram["Uname"], my_pram["Ktime"], strTime, my_pram["endTime"])
        if kline_df is None or kline_df.empty:
            raise ValueError("获取到的K线数据为空。")
        print(f"✅ 成功获取到 {len(kline_df)} 条K线数据，时间范围从 {kline_df['open_time'].iloc[0]} 到 {kline_df['open_time'].iloc[-1]}。")
        print("数据预览:")
        print(kline_df.tail())
    except Exception as e:
        print(f"❌ 获取数据失败: {e}")
        exit(1)

    # --- 4. 启动贝叶斯优化 ---
    
    # ==================== 核心修改：并行设置参数化 ====================
    # 设置并行计算使用的 CPU 核心比例
    # 0.5 代表使用 50% 的核心，1.0 代表 100%
    parallel_cpu_ratio = 0.5 
    
    # 计算要使用的进程数
    total_cpu_count = cpu_count()
    if total_cpu_count > 1:
        # 确保至少保留一个核心给系统，避免卡死
        n_jobs = max(1, int(total_cpu_count * parallel_cpu_ratio))
    else:
        n_jobs = 1 # 单核CPU，无法并行
        
    print(f"\n🚀 启动贝叶斯优化，总回测次数: 100，使用 {n_jobs} 个进程 (占总核心数的 {parallel_cpu_ratio*100:.0f}%)。")
    # ====================================================================

    start_time_opt = time.time()
    
    # 关键修改：使用 partial 绑定所有共享数据
    dimension_names = [dim.name for dim in space_dimensions]
    
    func_with_data = partial(
        objective_function_wrapper_final,
        kline_df_shared=kline_df,
        my_pram_shared=my_pram,
        base_config_shared=base_config_15K,
        needed_klines_shared=needed_klines,
        dimension_names_shared=dimension_names
    )
    
    # 调用 gp_minimize，它会负责多进程的创建和任务分发
    result = gp_minimize(
        func=func_with_data,
        dimensions=space_dimensions,
        n_calls=100,
        n_random_starts=20,
        acq_func="gp_hedge",
        n_jobs=n_jobs,
        random_state=42,
    )
    
    elapsed_time_opt = time.time() - start_time_opt
    print(f"\n贝叶斯优化总耗时: {elapsed_time_opt:.2f} 秒")
    
    # --- 5. 分析和展示结果 ---
    analyze_and_print_results_bo(result, kline_df, my_pram, base_config_15K, needed_klines)
    
    print("\n优化脚本运行完毕。")