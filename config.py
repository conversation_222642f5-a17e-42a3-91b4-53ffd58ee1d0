import requests
import pandas as pd
import sqlite3
import datetime
import pytz # 用于时区转换
# import pandas as pd
# --- 猴子补丁开始 ---
# 解决 ImportError: cannot import name 'NaN' from 'numpy'
# 强制将 numpy.nan 导入为 numpy.NaN
import numpy
if not hasattr(numpy, 'NaN'):
    numpy.NaN = numpy.nan
# --- 猴子补丁结束 ---
import pandas_ta as ta # 导入 pandas_ta 库 (现在可以正常导入了)
# from ku import generate_plot,  generate_dual_axis_plot
from ku import *



def get_kline_data(symbol: str, interval: str, count: int) -> pd.DataFrame:
    """
    从币安永续合约 API 获取 K 线数据。

    Args:
        symbol (str): 交易对，例如 "DOGEUSDT"。
        interval (str): K 线时间间隔，例如 "15m", "1h", "1d"。
        count (int): 需要获取的K线数量。

    Returns:
        pd.DataFrame: 包含 K 线数据的 DataFrame，列包括：
                      'open_time', 'open', 'high', 'low', 'close',
                      'volume', 'close_time', 'quote_asset_volume',
                      'number_of_trades', 'taker_buy_base_asset_volume',
                      'taker_buy_quote_asset_volume', 'ignore'。
                      'open_time' 和 'close_time' 已转换为北京时间。
    """
    base_url = "https://fapi.binance.com"  # 币安U本位永续合约API基础URL
    endpoint = "/fapi/v1/klines"

    params = {
        "symbol": symbol.upper(),  # 交易对转换为大写
        "interval": interval,
        "limit": count
    }

    try:
        response = requests.get(base_url + endpoint, params=params)
        response.raise_for_status()  # 检查请求是否成功
        data = response.json()

        if not data:
            print(f"警告: 未能获取到 {symbol} 的 K 线数据，请检查交易对和参数。")
            return pd.DataFrame()

        # 将K线数据转换为DataFrame
        df = pd.DataFrame(data, columns=[
            'open_time', 'open', 'high', 'low', 'close',
            'volume', 'close_time', 'quote_asset_volume',
            'number_of_trades', 'taker_buy_base_asset_volume',
            'taker_buy_quote_asset_volume', 'ignore'
        ])
        # df = pd.DataFrame(data, columns=[
        #     'open_time', 'open', 'high', 'low', 'close',
        #     'volume', 'close_time', 'quote_asset_volume',
        #     'number_of_trades', 'taker_buy_base_asset_volume',
        #     'taker_buy_quote_asset_volume', 'ignore'
        # ])

        # 将时间戳转换为 datetime 对象并处理时区
        # 币安API返回的时间戳是毫秒，需要除以1000
        beijing_tz = pytz.timezone('Asia/Shanghai')
        utc_tz = pytz.utc

        df['open_time'] = pd.to_datetime(df['open_time'], unit='ms').dt.tz_localize(utc_tz).dt.tz_convert(beijing_tz)
        df['close_time'] = pd.to_datetime(df['close_time'], unit='ms').dt.tz_localize(utc_tz).dt.tz_convert(beijing_tz)

        # 转换价格和成交量数据类型为数值型
        numeric_cols = ['open', 'high', 'low', 'close', 'volume']
        df[numeric_cols] = df[numeric_cols].astype(float)

        print(f"成功获取 {symbol} ({interval}, {count}根) 的K线数据。")
        return df

    except requests.exceptions.RequestException as e:
        print(f"请求币安API失败: {e}")
        return pd.DataFrame()
    except Exception as e:
        print(f"处理K线数据时发生错误: {e}")
        return pd.DataFrame()
def calculate_kline_needed(pram: dict) -> int:
    
    # RSI至少需要 rsi_length 根K线才能计算第一个值
    rsi_warmup_needed = pram.get("rsi_length", 14) # 默认14

    # MACD通常需要慢速EMA周期的2倍或更多K线来确保初始值的稳定
    # 这里使用 get 方法并提供默认值，以防 pram 中缺少某个键
    macd_warmup_needed = pram.get("macd_Slow",26) * 2 # 默认26*2=52

    # 取所有指标预热所需的最大值
    indicator_warmup_needed = max(rsi_warmup_needed, macd_warmup_needed)

    # 步骤2：计算总计所需历史K线数
    # 总计 = 指标预热所需 + 您实际要分析的K线数
    max_lookback_kline_count = pram.get("max_lookback_kline_count", 100) # 默认100

    total_required_klines = indicator_warmup_needed + max_lookback_kline_count

    # print(f"\n根据参数 {pram} 计算所需K线数量:")
    # print(f"  - 指标预热所需K线数 (取RSI和MACD预热最大值): {indicator_warmup_needed} 根")
    # print(f"  - 实际分析回顾K线数: {max_lookback_kline_count} 根")
    # print(f"  - 因此，总计需要获取的历史K线数: {total_required_klines} 根")

    return total_required_klines


def add_rsi_to_kline_data(kline_df: pd.DataFrame, rsi_length: int = 14) -> pd.DataFrame:
    """
    在 K 线 DataFrame 中增加 RSI (相对强弱指数) 值。
    使用 pandas_ta 库进行计算，更轻量且易于安装。

    Args:
        kline_df (pd.DataFrame): 包含 K 线数据的 DataFrame，必须包含 'close' 列。
        rsi_length (int): RSI 的计算周期，默认为 14。

    Returns:
        pd.DataFrame: 增加了 'RSI' 列的 DataFrame。如果数据不足以计算RSI，则该列可能包含NaN。
    """
    if 'close' not in kline_df.columns:
        print("错误: K 线 DataFrame 中缺少 'close' 列，无法计算 RSI。")
        return kline_df.copy()

    # pandas_ta 会自动处理数据不足的情况，生成NaN
    df_with_rsi = kline_df.copy() # 创建副本，避免修改原始DataFrame
    
    # 使用 pandas_ta 计算RSI
    # ta.rsi() 返回的是一个 pandas Series，可以直接赋值
    df_with_rsi['RSI'] = ta.rsi(df_with_rsi['close'], length=rsi_length)

    print(f"已成功在 K 线数据中增加 RSI (周期: {rsi_length})，使用 pandas_ta。")
    return df_with_rsi

if __name__ == "__main__":
    # 假设的参数集合
    my_pram = {
        "rsi_length": 14,
        "macd_length": 26,
        "max_lookback_kline_count": 100,
        "my_sma_length": 5,
        "my_ema_length": 5,
        "my_gaussian_sigma": 2.0
    }

    # 1. 计算所需获取的K线数量
    needed_klines = calculate_kline_needed(my_pram)
    kline_df = None
    kline_df_with_rsi = None
    # print(needed_klines)
    # 2. 获取K线数据
    # 交易对为 DOGEUSDT，时间间隔15分钟
    kline_df = get_kline_data(symbol="DOGEUSDT", interval="15m", count=needed_klines)
    # print(kline_df).head()
    kline_df=kline_df.iloc[:, 0:5]
    print(kline_df)
    print(type(kline_df['open'].iloc[0]))
    

    conn = sqlite3.connect('Kdatas/kline.db')
    cursor = conn.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    print(cursor.fetchall())
    conn.close()

    ###查看数据库字段
    # conn = sqlite3.connect('Kdatas/kline.db')
    # cursor = conn.cursor()
    # cursor.execute("PRAGMA table_info(doge15m);")
    # for row in cursor.fetchall():
    #     print(row)
    # conn.close()

    # test_print_kline_head('doge', '15m')

    # try:
    #     kline_df = get_local_kline_data('doge', '15m', '2025-1-1', '2025-2-2')
    #     print(kline_df.head())
    # except Exception as e:
    #     print(e)
    # print(kline_df)
    # print(type(kline_df['open'].iloc[0]))
    



# ###计算指标的函数

#     if not kline_df.empty:
#         # 增加RSI
#         kline_df_with_rsi = add_rsi_to_kline_data(kline_df, rsi_length=my_pram["rsi_length"])
#         original_rsi_series = kline_df_with_rsi['RSI']

#         # 增加平滑RSI
#         kline_df_with_rsi['RSI_SMA_5'] = smooth_rsi_with_sma(original_rsi_series, length=my_pram["my_sma_length"])
#         kline_df_with_rsi['RSI_EMA_5'] = smooth_rsi_with_ema(original_rsi_series, length=my_pram["my_ema_length"])
#         kline_df_with_rsi['RSI_Gaussian_2.0'] = smooth_rsi_with_gaussian(original_rsi_series, sigma=my_pram["my_gaussian_sigma"])

#         # 保存完整K线数据及所有计算列到CSV
#         kline_df_with_rsi.to_csv("dogeusdt_kline_rsi_smoothing.csv", index=False, encoding="utf-8-sig")
#         print("完整K线数据及RSI平滑结果已保存到 dogeusdt_kline_rsi_smoothing.csv")
#     else:
#         print("K线数据为空，无法进行分析。")



#     if not kline_df_with_rsi.empty and 'RSI' in kline_df_with_rsi.columns:
#         stats = analyze_numerical_series(kline_df_with_rsi['RSI'])
#         print("前5个最大RSI值（四舍五入取整）:", stats['top_5_max'], "数量:", stats['top_5_max_counts'])
#         print("后5个最小RSI值（四舍五入取整）:", stats['bottom_5_min'], "数量:", stats['bottom_5_min_counts'])
#         print("均值:", stats['mean'])
#         print("众数:", stats['mode'])
#         print("方差:", stats['variance'])
#         print("平方差:", stats['sum_squared_diff'])
#         print("均方误差:", stats['mse'])
#         print("标准差:", stats['std_dev'])
#         print("正态分布置信区间1σ区:", stats['conf_interval_1sigma'], "数量:", stats['n_1sigma'])
#         print("正态分布置信区间2σ区:", stats['conf_interval_2sigma'], "数量:", stats['n_2sigma'])
#         print("正态分布置信区间3σ区:", stats['conf_interval_3sigma'], "数量:", stats['n_3sigma'])

#     if not kline_df_with_rsi.empty and 'RSI_Gaussian_2.0' in kline_df_with_rsi.columns:
#         stats = analyze_numerical_series(kline_df_with_rsi['RSI_Gaussian_2.0'])
#         print("前5个最大RSI值（四舍五入取整）:", stats['top_5_max'], "数量:", stats['top_5_max_counts'])
#         print("后5个最小RSI值（四舍五入取整）:", stats['bottom_5_min'], "数量:", stats['bottom_5_min_counts'])
#         print("均值:", stats['mean'])
#         print("众数:", stats['mode'])
#         print("方差:", stats['variance'])
#         print("平方差:", stats['sum_squared_diff'])
#         print("均方误差:", stats['mse'])
#         print("标准差:", stats['std_dev'])
#         print("正态分布置信区间1σ区:", stats['conf_interval_1sigma'], "数量:", stats['n_1sigma'])
#         print("正态分布置信区间2σ区:", stats['conf_interval_2sigma'], "数量:", stats['n_2sigma'])
#         print("正态分布置信区间3σ区:", stats['conf_interval_3sigma'], "数量:", stats['n_3sigma'])




    # 测试保存
    # if not kline_df.empty:
    #     # 增加RSI
    #     kline_df_with_rsi = add_rsi_to_kline_data(kline_df, rsi_length=my_pram["rsi_length"])
    #     original_rsi_series = kline_df_with_rsi['RSI']

    #     # 增加平滑RSI
    #     kline_df_with_rsi['RSI_SMA_5'] = smooth_rsi_with_sma(original_rsi_series, length=my_pram["my_sma_length"])
    #     kline_df_with_rsi['RSI_EMA_5'] = smooth_rsi_with_ema(original_rsi_series, length=my_pram["my_ema_length"])
    #     kline_df_with_rsi['RSI_Gaussian_2.0'] = smooth_rsi_with_gaussian(original_rsi_series, sigma=my_pram["my_gaussian_sigma"])

    #     # # 保存完整K线数据及所有计算列到CSV
    #     # kline_df_with_rsi.to_csv("dogeusdt_kline_rsi_smoothing.csv", index=False, encoding="utf-8-sig")
    #     # print("完整K线数据及RSI平滑结果已保存到 dogeusdt_kline_rsi_smoothing.csv")
    # else:
    #     print("K线数据为空，无法进行分析。")





    # # ###画图
    # if not kline_df.empty:
    #     # 增加RSI
    #     kline_df_with_rsi = add_rsi_to_kline_data(kline_df, rsi_length=my_pram["rsi_length"])
    #     original_rsi_series = kline_df_with_rsi['RSI']

    #     # 增加平滑RSI
    #     kline_df_with_rsi['RSI_SMA_5'] = smooth_rsi_with_sma(original_rsi_series, length=my_pram["my_sma_length"])
    #     kline_df_with_rsi['RSI_EMA_5'] = smooth_rsi_with_ema(original_rsi_series, length=my_pram["my_ema_length"])
    #     kline_df_with_rsi['RSI_Gaussian_2.0'] = smooth_rsi_with_gaussian(original_rsi_series, sigma=my_pram["my_gaussian_sigma"])

    #     # 显示部分结果
    #     print("\n----------------------------------------------------------------")
    #     print("带有RSI值的K线数据 (前5行和后5行):")
    #     print(kline_df_with_rsi[['open_time', 'close', 'RSI']].head())
    #     print("...")
    #     print(kline_df_with_rsi[['open_time', 'close', 'RSI']].tail())
    #     print(f"\nRSI列中NaN值的数量: {kline_df_with_rsi['RSI'].isna().sum()}")
    #     print(f"第一个非NaN的RSI值索引: {kline_df_with_rsi['RSI'].first_valid_index()}")
    #     print(f"所需预热K线数中RSI部分: {my_pram['rsi_length']}")

    #     # --------- 单曲线绘图 ----------
    #     # 你可以选择只画某一条曲线（RSI/RSI_SMA/RSI_EMA/RSI_Gaussian）
    #     generate_plot(
    #         y_values=kline_df_with_rsi['RSI'],
    #         title=f"DOGEUSDT 15m RSI (周期: {my_pram['rsi_length']}) 趋势图",
    #         y_label="RSI 值",
    #         x_label="K线索引",
    #         filename="dogeusdt_rsi_chart.png"
    #     )

    #     # --------- 双轴图组合（RSI+价格） ----------
    #     generate_dual_axis_plot(
    #         y1_values=kline_df_with_rsi['RSI'],
    #         y2_values=kline_df_with_rsi['close'],
    #         title=f"DOGEUSDT 15m (RSI & 价格)",
    #         y1_label=f"RSI (周期: {my_pram['rsi_length']})",
    #         y2_label="DOGEUSDT 价格",
    #         x_label="K线索引",
    #         filename="dogeusdt_rsi_price_dual_axis.png",
    #         y1_color='tab:blue',
    #         y2_color='tab:red'
    #     )

    #     # --------- 多曲线叠加组图（RSI+所有平滑） ----------
    #     generate_multi_rsi_smoothing_plot(
    #         kline_df=kline_df_with_rsi,
    #         rsi_length=my_pram["rsi_length"],
    #         sma_length=my_pram["my_sma_length"],
    #         ema_length=my_pram["my_ema_length"],
    #         gaussian_sigma=my_pram["my_gaussian_sigma"],
    #         title_prefix="DOGEUSDT 15m K线分析",
    #         x_label="K线索引",
    #         filename="dogeusdt_rsi_smoothing_comparison.png"
    #     )

    #     # 打印部分平滑结果用于验证
    #     print("\n--- 带有平滑RSI的K线数据 (部分，用于验证) ---")
    #     print(kline_df_with_rsi[['open_time', 'close', 'RSI', 'RSI_SMA_5', 'RSI_EMA_5', 'RSI_Gaussian_2.0']].tail(10))

    # else:
    #     print("K线数据为空，无法进行分析和绘图。")