import numpy as np
import pandas as pd
from typing import List, Union, Tuple, Dict, Optional
from scipy import stats
import warnings
from dataclasses import dataclass
warnings.filterwarnings('ignore')

@dataclass
class TrendConfig:
    """
    趋势分析配置类
    参数详解与必要性：
    1. 极值检测参数 (🔍 拐点识别相关)
    -----------------------------------------------------
    extrema_sensitivity: 极值检测敏感度（0.1-1.0，必要性: ⭐⭐⭐⭐⭐，影响识别拐点数量）
        0.2 - 只识别最明显的拐点（保守）
        0.5 - 平衡识别（默认）
        0.8 - 识别更多细微拐点（激进）
    extrema_credibility_threshold: 拐点最低可信度门槛（20-50，必要性: ⭐⭐⭐，过滤低质量拐点）
    high_credibility_threshold: 高可信度拐点定义（50-90，必要性: ⭐⭐⭐⭐，投资决策关键，决定强反转信号标准）
    2. 回撤分析参数 (📉 风险控制相关)
    -----------------------------------------------------
    drawdown_threshold: 回撤识别阈值（-0.1~-2.0，必要性: ⭐⭐⭐⭐，影响误报/漏报）
        -0.5   - 适中敏感度
        -0.8   - 更敏感（短线）
        -1.2   - 较宽松（波段）
    severe_drawdown_threshold: 严重回撤定义（-5~-20，必要性: ⭐⭐⭐，重要风险分级，风险等级划分和警告触发）

    3. 趋势确认参数 (📈 趋势质量相关)
    -----------------------------------------------------
    trend_consistency_threshold: 多时间框架趋势一致性要求（60-90，必要性: ⭐⭐⭐⭐，趋势确认核心，越高越严苛）

    trend_strength_strong_threshold: 强劲趋势力度门槛（2-8，必要性: ⭐⭐⭐，趋势分级，影响强趋势定义）

    trend_strength_medium_threshold: 中等趋势力度门槛（1-4，必要性: ⭐⭐，细分级别，影响趋势强度细分）
    """

    # 极值检测配置
    extrema_sensitivity: float = 0.5  # 极值检测敏感度，越大越敏感，检测出的拐点越多
    extrema_credibility_threshold: float = 40.0  # 拐点可信度最低门槛，过滤低质量拐点
    high_credibility_threshold: float = 60.0  # 定义为高可信度拐点的阈值，用于强信号判别

    # 回撤分析配置
    drawdown_threshold: float = -0.5  # 回撤识别阈值（百分比），低于此值计为回撤
    severe_drawdown_threshold: float = -10.0  # 严重回撤的界定阈值（百分比），用于风险分级

    # 趋势确认配置
    trend_consistency_threshold: float = 80.0  # 趋势一致性判断阈值（%），多时间框架一致性要求
    trend_strength_strong_threshold: float = 5.0  # 强劲趋势力度门槛
    trend_strength_medium_threshold: float = 2.0  # 中等趋势力度门槛

class TrendAnalyzer:
    """核心趋势分析器"""
    
    def __init__(self, config: TrendConfig = None):
        self.config = config or TrendConfig()
    
    def analyze_trend(self, data: List[float], malen: int) -> Dict:
        """
        分析列表数据的趋势，包含极值拐点判断和回撤分析
        
        参数:
        data: 数值列表
        malen: 检查长度，从后往前的长度
        
        返回:
        包含趋势分析结果的字典
        """
        # 数据验证
        validation_result = self._validate_data(data, malen)
        if validation_result:
            return validation_result
        
        # 取最后 malen 个数据点
        recent_data = data[-malen:]
        
        try:
            # 基础趋势分析
            basic_trend = self._analyze_basic_trend(recent_data)
            
            # 极值拐点分析
            extrema_analysis = self._analyze_extrema_turning_points(recent_data, data)
            
            # 回撤分析
            drawdown_analysis = self._analyze_comprehensive_drawdown(recent_data)
            
            # 趋势确认分析
            trend_confirmation = self._analyze_trend_confirmation(recent_data)
            
            # 风险评估
            risk_assessment = self._assess_overall_risk(
                basic_trend, extrema_analysis, drawdown_analysis, trend_confirmation
            )
            
            return {
                **basic_trend,
                'extrema_analysis': extrema_analysis,
                'drawdown_analysis': drawdown_analysis,
                'trend_confirmation': trend_confirmation,
                'risk_assessment': risk_assessment,
                'data_points': malen
            }
            
        except Exception as e:
            return {
                'error': f'分析过程中出现错误: {str(e)}',
                'data_points': malen
            }
    
    def _validate_data(self, data: List[float], malen: int) -> Optional[Dict]:
        """数据验证"""
        if not data:
            return {'error': '数据为空'}
        
        if len(data) < malen:
            return {'error': f'数据长度 {len(data)} 小于检查长度 {malen}'}
        
        if malen < 3:
            return {'error': f'检查长度 {malen} 太小，至少需要3个数据点'}
        
        # 检查数据质量
        recent_data = data[-malen:]
        if all(x == recent_data[0] for x in recent_data):
            return {'error': '数据无变化，无法进行趋势分析'}
        
        return None
    
    def _analyze_basic_trend(self, recent_data: List[float]) -> Dict:
        """基础趋势分析"""
        x = np.arange(len(recent_data))
        y = np.array(recent_data)
        
        # 线性回归计算斜率
        slope = np.polyfit(x, y, 1)[0]
        
        # 计算相对变化率
        relative_change = (recent_data[-1] - recent_data[0]) / recent_data[0] * 100
        
        # 计算移动平均趋势
        if len(recent_data) >= 6:
            ma_short = np.mean(recent_data[-3:])
            ma_long = np.mean(recent_data[:3])
            ma_trend = "上升" if ma_short > ma_long else "下降" if ma_short < ma_long else "平稳"
        else:
            ma_trend = "数据点太少"
        
        # 判断总体趋势
        slope_threshold = 0.01
        strong_threshold = 0.1
        
        if abs(slope) < slope_threshold:
            trend = "平稳"
        elif slope > 0:
            trend = "强烈上升" if slope > strong_threshold else "缓慢上升"
        else:
            trend = "强烈下降" if slope < -strong_threshold else "缓慢下降"
        
        # 计算波动性
        volatility = np.std(recent_data)
        
        return {
            'trend': trend,
            'slope': slope,
            'relative_change_percent': round(relative_change, 2),
            'moving_average_trend': ma_trend,
            'volatility': round(volatility, 4),
            'start_value': recent_data[0],
            'end_value': recent_data[-1],
            'min_value': min(recent_data),
            'max_value': max(recent_data)
        }
    
    def _analyze_extrema_turning_points(self, recent_data: List[float], full_data: List[float]) -> Dict:
        """极值拐点分析（优化版）"""
        recent_array = np.array(recent_data)
        full_array = np.array(full_data)
        current_value = recent_data[-1]
        
        # 寻找局部极值点
        peaks, troughs = self._find_extrema_points(recent_data)
        
        # 计算所有极值点的可信度
        credible_peaks = []
        credible_troughs = []
        
        for peak in peaks:
            credibility = self._calculate_turning_point_credibility(recent_data, peak)
            if credibility >= self.config.extrema_credibility_threshold:
                credible_peaks.append({
                    **peak,
                    'credibility': round(credibility, 1),
                    'distance_from_end': len(recent_data) - 1 - peak['index'],
                    'type': '峰值'
                })
        
        for trough in troughs:
            credibility = self._calculate_turning_point_credibility(recent_data, trough)
            if credibility >= self.config.extrema_credibility_threshold:
                credible_troughs.append({
                    **trough,
                    'credibility': round(credibility, 1),
                    'distance_from_end': len(recent_data) - 1 - trough['index'],
                    'type': '谷值'
                })
        
        # 合并并排序所有可信拐点
        all_turning_points = credible_peaks + credible_troughs
        all_turning_points.sort(key=lambda x: x['distance_from_end'])
        
        # 历史百分位计算
        percentile = stats.percentileofscore(full_array, current_value)
        
        # 识别极值区域
        is_extreme_high = percentile >= 95
        is_extreme_low = percentile <= 5
        is_high_zone = percentile >= 85
        is_low_zone = percentile <= 15
        
        # 当前位置拐点预测
        turning_probability, prediction_type = self._predict_current_turning_point(
            recent_data, is_extreme_high, is_extreme_low, is_high_zone, is_low_zone
        )
        
        # 最强拐点识别
        strongest_turning_point = None
        if all_turning_points:
            strongest_turning_point = max(all_turning_points, key=lambda x: x['credibility'])
        
        return {
            'current_percentile': round(percentile, 1),
            'is_extreme_high': is_extreme_high,
            'is_extreme_low': is_extreme_low,
            'is_high_zone': is_high_zone,
            'is_low_zone': is_low_zone,
            'turning_points_found': len(all_turning_points),
            'credible_turning_points': all_turning_points[:5],
            'strongest_turning_point': strongest_turning_point,
            'current_turning_probability': turning_probability,
            'prediction_type': prediction_type,
            'extrema_summary': {
                'total_peaks': len(credible_peaks),
                'total_troughs': len(credible_troughs),
                'avg_peak_credibility': round(np.mean([p['credibility'] for p in credible_peaks]), 1) if credible_peaks else 0,
                'avg_trough_credibility': round(np.mean([t['credibility'] for t in credible_troughs]), 1) if credible_troughs else 0
            }
        }
    
    def _find_extrema_points(self, data: List[float]) -> Tuple[List[Dict], List[Dict]]:
        """寻找局部极值点（优化算法）"""
        data_array = np.array(data)
        peaks = []
        troughs = []
        
        # 动态窗口大小，但限制在合理范围内
        window = max(2, min(5, int(len(data) * 0.1)))
        
        for i in range(window, len(data) - window):
            left_window = data_array[i-window:i]
            right_window = data_array[i+1:i+window+1]
            current = data_array[i]
            
            # 计算动态阈值
            left_mean = np.mean(left_window)
            left_std = np.std(left_window)
            right_mean = np.mean(right_window)
            right_std = np.std(right_window)
            
            # 峰值判断
            if (current > left_mean + self.config.extrema_sensitivity * left_std and
                current > right_mean + self.config.extrema_sensitivity * right_std):
                strength = min(current - left_mean, current - right_mean)
                peaks.append({
                    'index': i,
                    'value': current,
                    'strength': strength
                })
            
            # 谷值判断
            if (current < left_mean - self.config.extrema_sensitivity * left_std and
                current < right_mean - self.config.extrema_sensitivity * right_std):
                strength = min(left_mean - current, right_mean - current)
                troughs.append({
                    'index': i,
                    'value': current,
                    'strength': strength
                })
        
        return peaks, troughs
    
    def _calculate_turning_point_credibility(self, data: List[float], extrema_point: Dict) -> float:
        """计算拐点可信度（优化评分系统）"""
        data_array = np.array(data)
        idx = extrema_point['index']
        strength = extrema_point['strength']
        
        if idx < 3 or idx >= len(data) - 3:
            return 0
        
        # 强度评分（30分）
        max_strength = np.std(data_array) * 2
        strength_score = min(30, (strength / max_strength) * 30) if max_strength > 0 else 0
        
        # 趋势反转评分（40分）
        window_size = min(5, idx, len(data) - idx - 1)
        if window_size >= 2:
            before_trend = np.mean(np.diff(data_array[max(0, idx-window_size):idx+1]))
            after_trend = np.mean(np.diff(data_array[idx:min(len(data), idx+window_size+1)]))
            trend_change = abs(before_trend - after_trend)
            max_trend_change = np.std(np.diff(data_array)) * 3
            trend_score = min(40, (trend_change / max_trend_change) * 40) if max_trend_change > 0 else 0
        else:
            trend_score = 0
        
        # 体积评分（20分）
        volatility_window = min(3, idx, len(data) - idx - 1)
        if volatility_window >= 1:
            volatility_around = np.std(data_array[max(0, idx-volatility_window):min(len(data), idx+volatility_window+1)])
            avg_volatility = np.std(data_array)
            volume_score = min(20, (volatility_around / avg_volatility) * 20) if avg_volatility > 0 else 0
        else:
            volume_score = 0
        
        # 位置评分（10分）
        position_score = 10 if 5 <= idx <= len(data) - 5 else 5
        
        total_credibility = strength_score + trend_score + volume_score + position_score
        return min(100, total_credibility)
    
    def _predict_current_turning_point(self, recent_data: List[float], 
                                     is_extreme_high: bool, is_extreme_low: bool,
                                     is_high_zone: bool, is_low_zone: bool) -> Tuple[float, str]:
        """预测当前位置形成拐点的概率"""
        if len(recent_data) < 10:
            return 0, "数据不足"
        
        # 动量变化分析
        momentum_window = min(5, len(recent_data) // 2)
        recent_momentum = np.mean(np.diff(recent_data[-momentum_window:]))
        previous_momentum = np.mean(np.diff(recent_data[-2*momentum_window:-momentum_window]))
        momentum_change = abs(recent_momentum - previous_momentum)
        
        # 极值区域因子
        extrema_factor = 0
        if is_extreme_high or is_extreme_low:
            extrema_factor = 0.4
        elif is_high_zone or is_low_zone:
            extrema_factor = 0.2
        
        # 波动率变化
        recent_volatility = np.std(recent_data[-momentum_window:])
        historical_volatility = np.std(recent_data)
        volatility_factor = min(0.3, abs(recent_volatility - historical_volatility) / historical_volatility) if historical_volatility > 0 else 0
        
        # 综合评分
        turning_probability = min(100, (momentum_change * 100 + extrema_factor * 100 + volatility_factor * 100))
        
        # 判断拐点类型倾向
        if is_extreme_high and recent_momentum < 0:
            prediction_type = "可能形成顶部拐点"
        elif is_extreme_low and recent_momentum > 0:
            prediction_type = "可能形成底部拐点"
        elif momentum_change > np.std(np.diff(recent_data)):
            prediction_type = "趋势可能发生变化"
        else:
            prediction_type = "延续当前趋势"
        
        return round(turning_probability, 1), prediction_type
    
    def _analyze_comprehensive_drawdown(self, data: List[float]) -> Dict:
        """综合回撤分析（优化版）"""
        data_array = np.array(data)
        
        # 计算回撤序列
        peak = np.maximum.accumulate(data_array)
        drawdown = (data_array - peak) / peak * 100
        
        # 识别回撤期间
        drawdown_periods = self._identify_drawdown_periods(drawdown)
        
        # 回撤强度分类
        max_drawdown = np.min(drawdown)
        intensity_label, intensity_level = self._classify_drawdown_intensity(max_drawdown)
        
        # 回撤类型分析
        pattern_type, risk_profile = self._analyze_drawdown_pattern(drawdown_periods)
        
        # 恢复能力分析
        recovery_analysis = self._analyze_recovery_capability(drawdown_periods)
        
        # 当前状态评估
        current_drawdown = drawdown[-1]
        is_in_drawdown = current_drawdown < self.config.drawdown_threshold
        
        # 计算从最近峰值的持续时间
        last_peak_idx = np.where(peak == peak[-1])[0][-1] if len(peak) > 0 else 0
        time_from_peak = len(data) - 1 - last_peak_idx
        
        return {
            'max_drawdown_percent': round(max_drawdown, 2),
            'intensity_label': intensity_label,
            'intensity_level': intensity_level,
            'pattern_type': pattern_type,
            'risk_profile': risk_profile,
            'drawdown_periods_count': len(drawdown_periods),
            'drawdown_periods': drawdown_periods,
            'recovery_analysis': recovery_analysis,
            'current_status': {
                'current_drawdown': round(current_drawdown, 2),
                'is_in_drawdown': is_in_drawdown,
                'time_from_peak': time_from_peak,
                'peak_value': round(peak[-1], 4)
            },
            'risk_metrics': {
                'avg_drawdown_duration': round(np.mean([p['duration'] for p in drawdown_periods]), 1) if drawdown_periods else 0,
                'max_drawdown_duration': max([p['duration'] for p in drawdown_periods]) if drawdown_periods else 0,
                'drawdown_frequency': len(drawdown_periods) / len(data) * 100
            }
        }
    
    def _identify_drawdown_periods(self, dd_series: np.ndarray) -> List[Dict]:
        """识别回撤期间（优化算法）"""
        periods = []
        in_drawdown = False
        start_idx = 0
        
        for i, dd in enumerate(dd_series):
            if dd <= self.config.drawdown_threshold and not in_drawdown:
                in_drawdown = True
                start_idx = i
            elif dd > self.config.drawdown_threshold and in_drawdown:
                in_drawdown = False
                recovery_speed = self._calculate_recovery_speed(dd_series, start_idx, i)
                periods.append({
                    'start': start_idx,
                    'end': i - 1,
                    'duration': i - start_idx,
                    'max_drawdown': np.min(dd_series[start_idx:i]),
                    'recovery_speed': recovery_speed
                })
        
        # 处理未结束的回撤
        if in_drawdown:
            periods.append({
                'start': start_idx,
                'end': len(dd_series) - 1,
                'duration': len(dd_series) - start_idx,
                'max_drawdown': np.min(dd_series[start_idx:]),
                'recovery_speed': None,
                'ongoing': True
            })
        
        return periods
    
    def _calculate_recovery_speed(self, dd_series: np.ndarray, start_idx: int, end_idx: int) -> float:
        """计算恢复速度"""
        if end_idx <= start_idx:
            return 0
        
        min_dd = np.min(dd_series[start_idx:end_idx])
        end_dd = dd_series[end_idx - 1]
        duration = end_idx - start_idx
        
        return (end_dd - min_dd) / duration if duration > 0 else 0
    
    def _classify_drawdown_intensity(self, max_dd: float) -> Tuple[str, int]:
        """回撤强度分类（优化阈值）"""
        thresholds = [
            (-1, "微弱", 1),
            (-3, "轻微", 2),
            (-5, "中等", 3),
            (-10, "严重", 4),
            (-20, "重度", 5),
            (float('-inf'), "极端", 6)
        ]
        
        for threshold, label, level in thresholds:
            if max_dd > threshold:
                return label, level
        
        return "极端", 6
    
    def _analyze_drawdown_pattern(self, periods: List[Dict]) -> Tuple[str, str]:
        """分析回撤模式"""
        if not periods:
            return "无明显回撤", "稳定型"
        
        total_periods = len(periods)
        avg_duration = np.mean([p['duration'] for p in periods])
        ongoing_periods = sum(1 for p in periods if p.get('ongoing', False))
        
        # 模式识别
        if total_periods == 1:
            if ongoing_periods == 1:
                pattern_type = "单次持续回撤"
                risk_profile = "高风险"
            else:
                pattern_type = "单次完整回撤"
                risk_profile = "中等风险"
        elif avg_duration <= 3:
            pattern_type = "频繁快速回撤"
            risk_profile = "高波动风险"
        elif avg_duration <= 7:
            pattern_type = "周期性回撤"
            risk_profile = "中等风险"
        else:
            pattern_type = "长期深度回撤"
            risk_profile = "高风险"
        
        return pattern_type, risk_profile
    
    def _analyze_recovery_capability(self, periods: List[Dict]) -> Dict:
        """分析恢复能力"""
        if not periods:
            return {"capability": "无需恢复", "score": 100}
        
        completed_periods = [p for p in periods if not p.get('ongoing', False)]
        
        if not completed_periods:
            return {"capability": "恢复中", "score": 0, "note": "当前仍在回撤期"}
        
        recovery_speeds = [p['recovery_speed'] for p in completed_periods if p['recovery_speed'] is not None]
        if not recovery_speeds:
            return {"capability": "恢复困难", "score": 20}
        
        avg_recovery_speed = np.mean(recovery_speeds)
        
        # 恢复能力评分（优化评分标准）
        if avg_recovery_speed > 1:
            capability, score = "强恢复能力", 90
        elif avg_recovery_speed > 0.5:
            capability, score = "良好恢复能力", 75
        elif avg_recovery_speed > 0.2:
            capability, score = "中等恢复能力", 60
        elif avg_recovery_speed > 0:
            capability, score = "较弱恢复能力", 40
        else:
            capability, score = "恢复困难", 20
        
        return {
            "capability": capability,
            "score": score,
            "avg_recovery_speed": round(avg_recovery_speed, 3),
            "recovery_consistency": round(np.std(recovery_speeds), 3) if len(recovery_speeds) > 1 else 0
        }
    
    def _analyze_trend_confirmation(self, data: List[float]) -> Dict:
        """趋势确认信号分析（优化版）"""
        data_array = np.array(data)
        
        # 多时间框架趋势（动态调整）
        short_window = min(5, len(data) // 4)
        medium_window = min(10, len(data) // 2)
        
        short_trend = np.polyfit(range(short_window), data_array[-short_window:], 1)[0] if len(data) >= short_window else 0
        medium_trend = np.polyfit(range(medium_window), data_array[-medium_window:], 1)[0] if len(data) >= medium_window else 0
        long_trend = np.polyfit(range(len(data)), data_array, 1)[0]
        
        # 趋势一致性评分
        trends = [t for t in [short_trend, medium_trend, long_trend] if t != 0]
        if trends:
            trend_consistency = sum(1 for t in trends if t * trends[0] > 0) / len(trends) * 100
        else:
            trend_consistency = 0
        
        # 强度评估（优化计算）
        trend_strength = abs(long_trend) / np.std(data_array) * 100 if np.std(data_array) > 0 else 0
        
        if trend_strength > self.config.trend_strength_strong_threshold:
            strength_label = "强劲"
        elif trend_strength > self.config.trend_strength_medium_threshold:
            strength_label = "中等"
        elif trend_strength > 0.5:
            strength_label = "微弱"
        else:
            strength_label = "无明显趋势"
        
        return {
            'trend_consistency_percent': round(trend_consistency, 1),
            'trend_strength': round(trend_strength, 2),
            'strength_label': strength_label,
            'short_term_slope': round(short_trend, 6),
            'medium_term_slope': round(medium_trend, 6),
            'long_term_slope': round(long_trend, 6)
        }
    
    def _assess_overall_risk(self, basic_trend: Dict, extrema_analysis: Dict, 
                           drawdown_analysis: Dict, trend_confirmation: Dict) -> Dict:
        """整体风险评估"""
        risk_score = 0
        risk_factors = []
        
        # 趋势风险
        if basic_trend['trend'] in ['强烈下降', '缓慢下降']:
            risk_score += 30
            risk_factors.append("下降趋势风险")
        
        # 拐点风险
        if extrema_analysis['current_turning_probability'] > 70:
            risk_score += 20
            risk_factors.append("高概率拐点风险")
        
        # 回撤风险
        if drawdown_analysis['intensity_level'] >= 4:
            risk_score += 25
            risk_factors.append("严重回撤风险")
        
        # 波动性风险
        if basic_trend['volatility'] > np.mean([basic_trend['max_value'], basic_trend['min_value']]) * 0.1:
            risk_score += 15
            risk_factors.append("高波动性风险")
        
        # 趋势不确定性风险
        if trend_confirmation['trend_consistency_percent'] < 60:
            risk_score += 10
            risk_factors.append("趋势不确定性风险")
        
        # 风险等级
        if risk_score >= 70:
            risk_level = "极高风险"
        elif risk_score >= 50:
            risk_level = "高风险"
        elif risk_score >= 30:
            risk_level = "中等风险"
        elif risk_score >= 10:
            risk_level = "低风险"
        else:
            risk_level = "极低风险"
        
        return {
            'risk_score': risk_score,
            'risk_level': risk_level,
            'risk_factors': risk_factors
        }


class TrendReportParser:
    """趋势报告解析器（优化版）"""
    
    def __init__(self):
        self.required_fields = [
            'trend', 'slope', 'relative_change_percent', 'data_points'
        ]
    
    def parse(self, report_dicts: List[Dict]) -> List[Dict]:
        """解析报告字典列表"""
        parsed_reports = []
        
        for i, report_dict in enumerate(report_dicts):
            try:
                standardized_data = self._standardize_report_data(report_dict, i)
                if standardized_data:
                    parsed_reports.append(standardized_data)
            except Exception as e:
                print(f"警告: 解析第{i+1}个报告时出错: {str(e)}")
                continue
        
        return parsed_reports
    
    def _standardize_report_data(self, data: Dict, index: int) -> Optional[Dict]:
        """标准化报告数据"""
        if not isinstance(data, dict):
            print(f"警告: 第{index+1}个报告不是字典类型")
            return None
        
        # 检查必需字段
        missing_fields = [field for field in self.required_fields if field not in data]
        if missing_fields:
            print(f"警告: 第{index+1}个报告缺少必需字段: {missing_fields}")
            return None
        
        try:
            # 先进行基础数据类型转换
            def safe_str_convert(val):
                """安全的字符串转换"""
                if val is None:
                    return None
                if hasattr(val, 'item'):  # numpy scalar
                    try:
                        val = val.item()
                    except:
                        pass
                return str(val) if val is not None else None
            
            standardized = {
                "数据点周期": self._safe_convert(data.get("data_points"), int),
                "基础趋势": safe_str_convert(data.get("trend")),
                "基础趋势斜率": self._safe_convert(data.get("slope"), float),
                "相对变化": self._safe_convert(data.get("relative_change_percent"), float),
                "移动平均趋势": safe_str_convert(data.get("moving_average_trend")),
                "波动性": self._safe_convert(data.get("volatility"), float),
            }
            
            # 极值拐点分析
            extrema_analysis = data.get("extrema_analysis", {})
            standardized.update(self._parse_extrema_analysis(extrema_analysis))
            
            # 回撤分析
            drawdown_analysis = data.get("drawdown_analysis", {})
            standardized.update(self._parse_drawdown_analysis(drawdown_analysis))
            
            # 趋势确认
            trend_confirmation = data.get("trend_confirmation", {})
            standardized.update(self._parse_trend_confirmation(trend_confirmation))
            
            # 风险评估
            risk_assessment = data.get("risk_assessment", {})
            standardized.update(self._parse_risk_assessment(risk_assessment))
            
            return standardized
            
        except Exception as e:
            print(f"警告: 标准化第{index+1}个报告时出错: {str(e)}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return None
    
    def _safe_convert(self, value, target_type):
        """安全类型转换"""
        if value is None:
            return np.nan
        try:
            if target_type == int:
                return int(float(value)) if not np.isnan(float(value)) else np.nan
            elif target_type == float:
                return float(value) if not np.isnan(float(value)) else np.nan
            else:
                return target_type(value)
        except (ValueError, TypeError):
            return np.nan
    
    def _parse_extrema_analysis(self, extrema_analysis: Dict) -> Dict:
        """解析极值分析数据"""
        def safe_bool_convert(val):
            """安全的布尔值转换"""
            if val is None:
                return False
            if hasattr(val, 'item'):  # numpy scalar
                try:
                    val = val.item()
                except:
                    pass
            if isinstance(val, (bool, np.bool_)):
                return bool(val)
            return bool(val) if val is not None else False
        
        result = {
            "当前百分位": self._safe_convert(extrema_analysis.get("current_percentile"), float),
            "当前拐点概率": self._safe_convert(extrema_analysis.get("current_turning_probability"), float),
            "预测类型": extrema_analysis.get("prediction_type"),
            "发现拐点数": extrema_analysis.get("turning_points_found", 0),
        }
        
        strongest_turning_point = extrema_analysis.get("strongest_turning_point")
        if strongest_turning_point:
            result.update({
                "最强拐点类型": strongest_turning_point.get("type"),
                "最强拐点可信度": self._safe_convert(strongest_turning_point.get("credibility"), float),
                "最强拐点距离": self._safe_convert(strongest_turning_point.get("distance_from_end"), int)
            })
        else:
            result.update({
                "最强拐点类型": None,
                "最强拐点可信度": np.nan,
                "最强拐点距离": np.nan
            })
        
        return result
    
    def _parse_drawdown_analysis(self, drawdown_analysis: Dict) -> Dict:
        """解析回撤分析数据"""
        current_status = drawdown_analysis.get("current_status", {})
        recovery_analysis = drawdown_analysis.get("recovery_analysis", {})
        
        current_drawdown = self._safe_convert(current_status.get("current_drawdown"), float)
        
        return {
            "回撤强度": drawdown_analysis.get("intensity_label"),
            "回撤模式": drawdown_analysis.get("pattern_type"),
            "风险档案": drawdown_analysis.get("risk_profile"),
            "当前回撤": current_drawdown,
            "正在回撤": bool(current_status.get("is_in_drawdown", False)) if current_drawdown != 0.0 else False,
            "恢复能力": recovery_analysis.get("capability"),
            "恢复能力评分": self._safe_convert(recovery_analysis.get("score"), int)
        }
    
    def _parse_trend_confirmation(self, trend_confirmation: Dict) -> Dict:
        """解析趋势确认数据"""
        return {
            "趋势一致性": self._safe_convert(trend_confirmation.get("trend_consistency_percent"), float),
            "趋势强度": self._safe_convert(trend_confirmation.get("trend_strength"), float),
            "趋势强度描述": trend_confirmation.get("strength_label")
        }
    
    def _parse_risk_assessment(self, risk_assessment: Dict) -> Dict:
        """解析风险评估数据"""
        return {
            "风险评分": self._safe_convert(risk_assessment.get("risk_score"), int),
            "风险等级": risk_assessment.get("risk_level"),
            "风险因子": risk_assessment.get("risk_factors", [])
        }


class InvestmentAdvisor:
    """投资建议生成器（优化版）"""
    
    def __init__(self, config: TrendConfig = None):
        self.config = config or TrendConfig()
    
    def generate_advice(self, parsed_reports: List[Dict]) -> Dict:
        """生成综合投资建议"""
        if not parsed_reports:
            return self._no_data_advice()
        
        # 创建分析DataFrame
        df = pd.DataFrame(parsed_reports)
        
        # 多维度分析
        trend_analysis = self._analyze_trend_consistency(df)
        risk_analysis = self._analyze_risk_profile(df)
        opportunity_analysis = self._analyze_opportunities(df)
        timing_analysis = self._analyze_market_timing(df)
        
        # 生成综合建议
        advice = self._synthesize_advice(trend_analysis, risk_analysis, opportunity_analysis, timing_analysis)
        
        return {
            "状态": "成功",
            "综合趋势判断": advice["主导趋势"],
            "建议操作": advice["建议操作"],
            "操作强度": advice["操作强度"],
            "风险提示": advice["风险提示"],
            "潜在机会": advice["潜在机会"],
            "时机分析": advice["时机分析"],
            "详细分析": {
                "趋势分析": trend_analysis,
                "风险分析": risk_analysis,
                "机会分析": opportunity_analysis,
                "时机分析": timing_analysis
            },
            "原始解析数据": parsed_reports
        }
    
    def _no_data_advice(self) -> Dict:
        """无数据时的建议"""
        return {
            "状态": "失败",
            "消息": "未能从报告内容中解析出任何有效的趋势数据。",
            "综合趋势判断": "未知",
            "建议操作": "请检查输入报告格式是否正确。",
            "操作强度": "暂停",
            "风险提示": ["数据不足，无法进行可靠分析"],
            "潜在机会": [],
            "时机分析": "等待有效数据",
            "详细分析": {
                "趋势分析": {"趋势确定性": "未知"},
                "风险分析": {"综合风险等级": "未知"},
                "机会分析": {"机会等级": "未知"},
                "时机分析": {"整体时机评估": "未知"}
            },
            "原始解析数据": []
        }
    
    def _analyze_trend_consistency(self, df: pd.DataFrame) -> Dict:
        """分析趋势一致性"""
        # 趋势方向统计
        trend_counts = df['基础趋势'].value_counts()
        ma_trend_counts = df['移动平均趋势'].value_counts()
        
        # 判断主导趋势
        rising_trends = sum(trend_counts.get(t, 0) for t in ['强烈上升', '缓慢上升'])
        falling_trends = sum(trend_counts.get(t, 0) for t in ['强烈下降', '缓慢下降'])
        stable_trends = trend_counts.get('平稳', 0)
        
        total_reports = len(df)
        rising_ratio = rising_trends / total_reports
        falling_ratio = falling_trends / total_reports
        
        # 趋势强度分析
        avg_slope = df['基础趋势斜率'].mean()
        slope_consistency = 1 - (df['基础趋势斜率'].std() / abs(df['基础趋势斜率'].mean())) if df['基础趋势斜率'].mean() != 0 else 0
        
        # 确定主导趋势
        if rising_ratio >= 0.7:
            dominant_trend = "强势上升"
        elif falling_ratio >= 0.7:
            dominant_trend = "强势下降"
        elif rising_ratio >= 0.5:
            dominant_trend = "偏向上升"
        elif falling_ratio >= 0.5:
            dominant_trend = "偏向下降"
        else:
            dominant_trend = "震荡整理"
        
        return {
            "主导趋势": dominant_trend,
            "上升趋势比例": round(rising_ratio * 100, 1),
            "下降趋势比例": round(falling_ratio * 100, 1),
            "平均斜率": round(avg_slope, 6),
            "斜率一致性": round(slope_consistency, 3),
            "趋势确定性": "高" if max(rising_ratio, falling_ratio) >= 0.8 else "中" if max(rising_ratio, falling_ratio) >= 0.6 else "低"
        }
    
    def _analyze_risk_profile(self, df: pd.DataFrame) -> Dict:
        """分析风险概况"""
        # 回撤风险
        severe_drawdown_count = sum(1 for _, row in df.iterrows() 
                                  if row.get('回撤强度') in ['严重', '重度', '极端'])
        high_risk_count = sum(1 for _, row in df.iterrows() 
                            if row.get('风险档案') == '高风险')
        
        # 拐点风险
        high_turning_probability = sum(1 for _, row in df.iterrows() 
                                     if row.get('当前拐点概率', 0) > 70)
        
        # 波动性风险
        avg_volatility = df['波动性'].mean()
        high_volatility_count = sum(1 for _, row in df.iterrows() 
                                  if row.get('波动性', 0) > avg_volatility * 1.5)
        
        # 综合风险评分
        total_reports = len(df)
        risk_score = (
            (severe_drawdown_count / total_reports) * 30 +
            (high_risk_count / total_reports) * 25 +
            (high_turning_probability / total_reports) * 25 +
            (high_volatility_count / total_reports) * 20
        )
        
        # 风险等级
        if risk_score >= 60:
            risk_level = "极高风险"
        elif risk_score >= 40:
            risk_level = "高风险"
        elif risk_score >= 20:
            risk_level = "中等风险"
        else:
            risk_level = "低风险"
        
        return {
            "综合风险等级": risk_level,
            "风险评分": round(risk_score, 1),
            "严重回撤比例": round(severe_drawdown_count / total_reports * 100, 1),
            "高拐点概率比例": round(high_turning_probability / total_reports * 100, 1),
            "平均波动性": round(avg_volatility, 4),
            "风险因子": self._identify_risk_factors(df)
        }
    
    def _identify_risk_factors(self, df: pd.DataFrame) -> List[str]:
        """识别风险因子"""
        risk_factors = []
        
        # 检查各种风险
        if any(row.get('正在回撤', False) for _, row in df.iterrows()):
            risk_factors.append("当前处于回撤期")
        
        if any(row.get('当前拐点概率', 0) > 80 for _, row in df.iterrows()):
            risk_factors.append("高概率拐点区域")
        
        if any(row.get('最强拐点类型') == '峰值' for _, row in df.iterrows()):
            risk_factors.append("检测到顶部信号")
        
        if df['波动性'].std() > df['波动性'].mean():
            risk_factors.append("波动性不稳定")
        
        return risk_factors
    
    def _analyze_opportunities(self, df: pd.DataFrame) -> Dict:
        """分析投资机会"""
        opportunities = []
        opportunity_score = 0
        
        # 底部拐点机会
        bottom_signals = sum(1 for _, row in df.iterrows() 
                           if row.get('预测类型') == '可能形成底部拐点')
        if bottom_signals > 0:
            opportunities.append(f"发现{bottom_signals}个底部信号，关注反弹机会")
            opportunity_score += 30
        
        # 高可信度谷值
        high_credibility_troughs = sum(1 for _, row in df.iterrows() 
                                     if row.get('最强拐点类型') == '谷值' 
                                     and pd.notna(row.get('最强拐点可信度'))
                                     and row.get('最强拐点可信度', 0) >= self.config.high_credibility_threshold)
        if high_credibility_troughs > 0:
            opportunities.append(f"发现{high_credibility_troughs}个高可信度谷值，反转概率较高")
            opportunity_score += 40
        
        # 强势趋势延续
        strong_trends = sum(1 for _, row in df.iterrows() 
                          if row.get('趋势强度描述') == '强劲' 
                          and pd.notna(row.get('趋势一致性'))
                          and row.get('趋势一致性', 0) >= self.config.trend_consistency_threshold)
        if strong_trends > 0:
            opportunities.append(f"发现{strong_trends}个强势趋势，可考虑顺势操作")
            opportunity_score += 25
        
        # 恢复能力强
        strong_recovery = sum(1 for _, row in df.iterrows() 
                            if pd.notna(row.get('恢复能力评分'))
                            and row.get('恢复能力评分', 0) >= 75)
        if strong_recovery > 0:
            opportunities.append(f"{strong_recovery}个周期显示强恢复能力")
            opportunity_score += 15
        
        return {
            "机会列表": opportunities,
            "机会评分": min(100, opportunity_score),
            "机会等级": "高机会" if opportunity_score >= 60 else "中等机会" if opportunity_score >= 30 else "低机会"
        }
    
    def _analyze_market_timing(self, df: pd.DataFrame) -> Dict:
        """分析市场时机"""
        # 短期时机
        recent_period = df.iloc[-1] if len(df) > 0 else {}
        short_term_timing = self._assess_short_term_timing(recent_period)
        
        # 中期时机
        medium_term_timing = self._assess_medium_term_timing(df)
        
        # 长期时机
        long_term_timing = self._assess_long_term_timing(df)
        
        return {
            "短期时机": short_term_timing,
            "中期时机": medium_term_timing,
            "长期时机": long_term_timing,
            "整体时机评估": self._overall_timing_assessment(short_term_timing, medium_term_timing, long_term_timing)
        }
    
    def _assess_short_term_timing(self, recent_data) -> str:
        """评估短期时机"""
        # 安全检查 recent_data 是否为空
        if recent_data is None:
            return "数据不足"
        
        # 如果是pandas Series，检查是否为空
        if hasattr(recent_data, 'empty'):
            if recent_data.empty:
                return "数据不足"
        # 如果是字典，检查是否为空
        elif isinstance(recent_data, dict):
            if not recent_data:
                return "数据不足"
        # 其他情况
        else:
            try:
                if not recent_data:
                    return "数据不足"
            except ValueError:
                # 如果是无法直接判断的类型，尝试转换为字典
                if hasattr(recent_data, 'to_dict'):
                    recent_data = recent_data.to_dict()
                else:
                    return "数据类型错误"
        
        # 如果 recent_data 是 Series，转换为字典
        if hasattr(recent_data, 'to_dict'):
            recent_data = recent_data.to_dict()
        
        turning_prob = recent_data.get('当前拐点概率', 0)
        if pd.isna(turning_prob):
            turning_prob = 0
        
        is_in_drawdown = recent_data.get('正在回撤', False)
        strongest_point_type = recent_data.get('最强拐点类型')
        
        if turning_prob > 80:
            if strongest_point_type == '谷值':
                return "短期买入时机"
            elif strongest_point_type == '峰值':
                return "短期卖出时机"
            else:
                return "短期观望"
        elif is_in_drawdown:
            return "短期等待"
        else:
            return "短期中性"
    
    def _assess_medium_term_timing(self, df: pd.DataFrame) -> str:
        """评估中期时机"""
        if len(df) < 2:
            return "数据不足"
        
        trend_consistency = df['趋势一致性'].mean()
        avg_strength = df['趋势强度'].mean()
        
        if trend_consistency >= 80 and avg_strength >= 5:
            return "中期趋势明确"
        elif trend_consistency >= 60:
            return "中期偏向性明显"
        else:
            return "中期震荡整理"
    
    def _assess_long_term_timing(self, df: pd.DataFrame) -> str:
        """评估长期时机"""
        if len(df) < 3:
            return "数据不足"
        
        # 安全地获取斜率值
        slopes = df['基础趋势斜率'].dropna()
        if slopes.empty or len(slopes) < 2:
            return "斜率数据不足"
        
        first_slope = float(slopes.iloc[0]) if pd.notna(slopes.iloc[0]) else 0
        last_slope = float(slopes.iloc[-1]) if pd.notna(slopes.iloc[-1]) else 0
        slope_trend = last_slope - first_slope
        
        # 安全地获取恢复能力评分
        valid_recovery = df['恢复能力评分'].dropna()
        recovery_scores = valid_recovery.mean() if not valid_recovery.empty else 0
        recovery_scores = float(recovery_scores) if pd.notna(recovery_scores) else 0
        
        if slope_trend > 0 and recovery_scores >= 70:
            return "长期向上趋势"
        elif slope_trend < 0:
            return "长期向下趋势"
        else:
            return "长期区间震荡"
    
    def _overall_timing_assessment(self, short: str, medium: str, long: str) -> str:
        """整体时机评估"""
        buy_signals = sum(1 for timing in [short, medium, long] 
                         if '买入' in timing or '向上' in timing or '明确' in timing)
        sell_signals = sum(1 for timing in [short, medium, long] 
                          if '卖出' in timing or '向下' in timing)
        
        if buy_signals >= 2:
            return "整体偏多"
        elif sell_signals >= 2:
            return "整体偏空"
        else:
            return "整体中性"
    
    def _synthesize_advice(self, trend_analysis: Dict, risk_analysis: Dict, 
                          opportunity_analysis: Dict, timing_analysis: Dict) -> Dict:
        """综合建议合成"""
        # 基础建议
        base_action = self._determine_base_action(trend_analysis, risk_analysis)
        
        # 操作强度
        operation_intensity = self._determine_operation_intensity(risk_analysis, opportunity_analysis)
        
        # 风险提示
        risk_warnings = self._generate_risk_warnings(risk_analysis, timing_analysis)
        
        # 潜在机会
        opportunities = opportunity_analysis.get("机会列表", [])
        
        # 时机分析
        timing_advice = self._generate_timing_advice(timing_analysis)
        
        return {
            "主导趋势": trend_analysis["主导趋势"],
            "建议操作": base_action,
            "操作强度": operation_intensity,
            "风险提示": risk_warnings,
            "潜在机会": opportunities,
            "时机分析": timing_advice
        }
    
    def _determine_base_action(self, trend_analysis: Dict, risk_analysis: Dict) -> str:
        """确定基础操作建议"""
        dominant_trend = trend_analysis["主导趋势"]
        risk_level = risk_analysis["综合风险等级"]
        trend_certainty = trend_analysis["趋势确定性"]
        
        if dominant_trend in ["强势上升", "偏向上升"]:
            if risk_level in ["极高风险", "高风险"]:
                return "谨慎做多，严控风险"
            elif trend_certainty == "高":
                return "考虑做多"
            else:
                return "轻仓做多"
        elif dominant_trend in ["强势下降", "偏向下降"]:
            if risk_level in ["极高风险", "高风险"]:
                return "考虑做空，注意反弹"
            else:
                return "谨慎做空"
        else:  # 震荡整理
            return "区间操作或观望"
    
    def _determine_operation_intensity(self, risk_analysis: Dict, opportunity_analysis: Dict) -> str:
        """确定操作强度"""
        risk_score = risk_analysis["风险评分"]
        opportunity_score = opportunity_analysis["机会评分"]
        
        net_score = opportunity_score - risk_score
        
        if net_score > 30:
            return "积极"
        elif net_score > 10:
            return "中等"
        elif net_score > -10:
            return "谨慎"
        else:
            return "极度谨慎"
    
    def _generate_risk_warnings(self, risk_analysis: Dict, timing_analysis: Dict) -> List[str]:
        """生成风险提示"""
        warnings = []
        
        # 风险等级警告
        if risk_analysis["综合风险等级"] in ["极高风险", "高风险"]:
            warnings.append(f"当前{risk_analysis['综合风险等级']}，建议严格控制仓位")
        
        # 具体风险因子
        for factor in risk_analysis["风险因子"]:
            warnings.append(factor)
        
        # 时机风险
        if "卖出" in timing_analysis["短期时机"]:
            warnings.append("短期可能面临调整压力")
        
        return list(set(warnings))  # 去重
    
    def _generate_timing_advice(self, timing_analysis: Dict) -> str:
        """生成时机建议"""
        overall = timing_analysis["整体时机评估"]
        short = timing_analysis["短期时机"]
        medium = timing_analysis["中期时机"]
        
        if overall == "整体偏多":
            if "买入" in short:
                return "时机较好，可考虑建仓"
            else:
                return "中长期看好，等待短期时机"
        elif overall == "整体偏空":
            return "谨慎操作，关注风险控制"
        else:
            return "震荡行情，灵活应对"


class UniqueTrendVerifier:
    """综合趋势验证器（优化版）"""
    
    def __init__(self, config: TrendConfig = None):
        self.config = config or TrendConfig()
        self.parser = TrendReportParser()
        self.advisor = InvestmentAdvisor(self.config)
    
    def verify_and_advise(self, report_dicts: List[Dict]) -> Dict:
        """处理原始报告字典列表，生成综合投资验证结果和建议"""
        try:
            # 解析数据
            # print("开始解析数据...")
            parsed_reports = self.parser.parse(report_dicts)
            # print(f"解析完成，得到 {len(parsed_reports)} 个报告")
            
            if not parsed_reports:
                return {
                    "状态": "失败",
                    "消息": "未能从报告内容中解析出任何有效的趋势数据。",
                    "综合趋势判断": "未知",
                    "建议操作": "请检查输入报告格式是否正确。",
                    "操作强度": "暂停",
                    "风险提示": ["数据解析失败"],
                    "潜在机会": [],
                    "时机分析": "等待有效数据"
                }
            
            # 生成投资建议
            # print("开始生成投资建议...")
            advice_result = self.advisor.generate_advice(parsed_reports)
            # print("投资建议生成完成")
            
            return advice_result
            
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            print(f"详细错误信息：\n{error_details}")
            
            return {
                "状态": "错误",
                "消息": f"处理过程中出现错误: {str(e)}",
                "综合趋势判断": "未知",
                "建议操作": "系统错误，请重试",
                "操作强度": "暂停",
                "风险提示": [f"系统错误: {str(e)}"],
                "潜在机会": [],
                "时机分析": "系统异常"
            }


# 优化后的主分析函数
def analyze_trendV2(data: List[float], malen: int, config: TrendConfig = None) -> Dict:
    """
    优化后的趋势分析主函数
    
    参数:
    data: 数值列表
    malen: 检查长度，从后往前的长度
    config: 配置对象，可选
    
    返回:
    包含趋势分析结果的字典
    """
    analyzer = TrendAnalyzer(config)
    return analyzer.analyze_trend(data, malen)


# 示例用法和测试
if __name__ == "__main__":
    # 测试数据
    data = [43.86471923, 44.34390555, 44.30328535, 43.85598118, 43.26073257, 42.75761627,
            42.44077626, 42.22406642, 41.92585099, 41.47573658, 41.07244234, 41.05270332,
            41.57118338, 42.47926685, 43.51646228, 44.51375901, 45.3960352, 46.12907484,
            46.74625297, 47.36825055, 48.1745325, 49.42791484, 51.47851438, 54.51704496,
            58.19815958, 61.65869556, 64.11233404, 65.41259986, 65.96449153, 66.21388489,
            66.36681149, 66.49201622, 66.66104525, 66.91583291, 67.20980307, 67.48537964,
            67.78563602, 68.22558369, 68.83960989, 69.46720191, 69.80725865, 69.66944367,
            69.23227993, 68.95985092, 69.17042442, 69.77639822, 70.50779653, 71.21790081,
            71.85604307, 72.31299523, 72.46429556, 72.30204384, 71.90271444, 71.26028423,
            70.20050512, 68.49526288, 66.08005395, 63.1245077, 59.81476834, 56.11387966,
            51.9094854, 47.44443859, 43.4103203, 40.45465016, 38.68225058, 37.73865815,
            37.24894188, 37.03937777, 37.04328799, 37.1864856, 37.38842763, 37.57853652,
            37.68034585, 37.63679188, 37.50016774, 37.49101935, 37.90268867, 38.85961256,
            40.13403406, 41.24007226, 41.76758368, 41.6585128, 41.19347965, 40.75269397,
            40.61715221, 40.9966071, 42.14341879, 44.26030116, 47.21849836, 50.43427807,
            53.10235478, 54.61245019, 54.85504685, 54.21207047, 53.25980126, 52.45861972,
            52.04150213, 52.02636805, 52.23744253, 52.38009222, 52.19720269, 51.5733376,
            50.49645061, 49.00169543, 47.23438667, 45.55001011, 44.44099427, 44.23435613,
            44.80625051, 45.64723046, 46.27488517, 46.58183127, 46.769859, 46.99808704,
            47.15755033, 46.97768202, 46.28860373, 45.13608906, 43.66702356, 41.96779658,
            40.04661216, 37.93604547, 35.77268651, 33.81750473, 32.45256709, 32.10184409,
            33.00708917, 34.994216, 37.50011016, 39.92390279, 42.01746714, 43.96658596,
            46.10731632, 48.58045186, 51.24916462, 53.84265663, 56.06186071, 57.6053739,
            58.29139903, 58.25380561, 57.9505693, 57.86877101, 58.14600214, 58.47445566,
            58.43200007, 57.94042573, 57.3802525, 57.28452116, 57.96827435, 59.34961893,
            60.93862843, 62.0111259]
    
    # 创建自定义配置
    custom_config = TrendConfig(
        extrema_sensitivity=0.3,  # 降低极值检测敏感度
        high_credibility_threshold=70.0,  # 提高高可信度阈值
        trend_consistency_threshold=75.0  # 调整趋势一致性阈值
    )
    
    print("=== 优化版趋势分析系统测试 ===\n")
    
    # 测试单个周期分析
    print("--- 单周期分析测试 (25个数据点) ---")
    result = analyze_trendV2(data, 25, custom_config)
    if 'error' not in result:
        print(f"基础趋势: {result['trend']}")
        print(f"风险评估: {result['risk_assessment']['risk_level']}")
        print(f"拐点概率: {result['extrema_analysis']['current_turning_probability']}%")
    else:
        print(f"错误: {result['error']}")
    
    print("\n--- 多周期综合分析测试 ---")
    
    # 测试多周期分析
    all_analysis_results = []
    for malen in [15, 25, 40]:
        result = analyze_trendV2(data, malen, custom_config)
        if 'error' not in result:
            all_analysis_results.append(result)
    
    # 综合分析
    if all_analysis_results:
        verifier = UniqueTrendVerifier(custom_config)
        comprehensive_result = verifier.verify_and_advise(all_analysis_results)
        
        print("=== 综合投资建议 ===")
        print(f"状态: {comprehensive_result['状态']}")
        print(f"综合趋势: {comprehensive_result['综合趋势判断']}")
        print(f"建议操作: {comprehensive_result['建议操作']}")
        print(f"操作强度: {comprehensive_result['操作强度']}")
        print(f"时机分析: {comprehensive_result['时机分析']}")
        
        print("\n风险提示:")
        for warning in comprehensive_result['风险提示']:
            print(f"  ⚠️  {warning}")
        
        print("\n潜在机会:")
        for opportunity in comprehensive_result['潜在机会']:
            print(f"  💡 {opportunity}")
        
        # 显示详细分析摘要
        if 'detailed_analysis' in comprehensive_result:
            detailed = comprehensive_result['详细分析']
            print(f"\n=== 详细分析摘要 ===")
            print(f"趋势确定性: {detailed['趋势分析']['趋势确定性']}")
            print(f"风险等级: {detailed['风险分析']['综合风险等级']}")
            print(f"机会等级: {detailed['机会分析']['机会等级']}")
            print(f"整体时机: {detailed['时机分析']['整体时机评估']}")
    
    print("\n=== 配置参数测试 ===")
    
    # 测试不同配置的影响
    configs = [
        ("保守配置", TrendConfig(
            extrema_sensitivity=0.3,
            high_credibility_threshold=80.0,
            trend_consistency_threshold=85.0
        )),
        ("激进配置", TrendConfig(
            extrema_sensitivity=0.7,
            high_credibility_threshold=50.0,
            trend_consistency_threshold=65.0
        )),
        ("平衡配置", TrendConfig())  # 默认配置
    ]
    
    for config_name, config in configs:
        print(f"\n--- {config_name} ---")
        test_result = analyze_trendV2(data, 25, config)
        if 'error' not in test_result:
            risk_level = test_result['risk_assessment']['risk_level']
            turning_prob = test_result['extrema_analysis']['current_turning_probability']
            trend_strength = test_result['trend_confirmation']['strength_label']
            print(f"风险等级: {risk_level}")
            print(f"拐点概率: {turning_prob}%")
            print(f"趋势强度: {trend_strength}")
    
    print("\n=== 性能优化测试 ===")
    
    # 测试大数据集性能
    import time
    large_data = data * 10  # 扩大数据集
    
    start_time = time.time()
    large_result = analyze_trendV2(large_data, 100, custom_config)
    end_time = time.time()
    
    if 'error' not in large_result:
        print(f"大数据集分析完成 (数据点: {len(large_data)}, 分析窗口: 100)")
        print(f"处理时间: {end_time - start_time:.3f} 秒")
        print(f"发现拐点: {large_result['extrema_analysis']['turning_points_found']} 个")
    else:
        print(f"大数据集分析失败: {large_result['error']}")
    
    print("\n=== 边界条件测试 ===")
    
    # 测试边界条件
    test_cases = [
        ("最小数据集", [1.0, 2.0, 3.0], 3),
        ("相同数值", [5.0] * 10, 5),
        ("极端波动", [1, 100, 1, 100, 1, 100, 1, 100], 8),
        ("单调递增", list(range(20)), 10),
        ("单调递减", list(range(20, 0, -1)), 10)
    ]
    
    for test_name, test_data, test_malen in test_cases:
        print(f"\n{test_name}:")
        test_result = analyze_trendV2(test_data, test_malen)
        if 'error' in test_result:
            print(f"  错误: {test_result['error']}")
        else:
            print(f"  趋势: {test_result['trend']}")
            print(f"  拐点数: {test_result['extrema_analysis']['turning_points_found']}")
    
    print("\n=== 测试完成 ===")
    print("优化版趋势分析处理器已就绪，具备以下特性：")
    print("✅ 模块化设计，职责分离")
    print("✅ 强化的数据验证和异常处理") 
    print("✅ 优化的极值检测和可信度算法")
    print("✅ 智能化的投资建议生成")
    print("✅ 灵活的配置系统")
    print("✅ 多时间框架综合分析")
    print("✅ 详细的风险评估和时机分析")