<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DOGE 15M 交互式交易策略应用</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@3.0.1/dist/chartjs-plugin-annotation.min.js"></script>
    <!-- Chosen Palette: Calm Trader -->
    <!-- Application Structure Plan: 本应用设计为一个单页、自上而下的交互式指南，以用户决策流程为核心线索。结构分为四个主要模块：1. 市场诊断（交互式仪表盘），2. 入场协议（动态流程图），3. 仓位管理（金字塔加仓计算器），4. 离场策略（可视化图表案例）。这种结构旨在将报告中的静态知识转化为一个动态的学习和模拟工具。用户可以按顺序学习整个交易流程，也可以直接跳转到特定模块进行查阅或使用工具。之所以选择这种流程化的结构，是因为交易本身就是一个线性的决策过程，这种设计最符合用户的认知习惯，能够最大化地提升策略的可理解性和可执行性。 -->
    <!-- Visualization & Content Choices: 1. **市场诊断** (目标: 比较/告知) -> 使用HTML/CSS/JS构建的交互式标签页，模拟EMA/BOLL/ADX在不同市况下的状态。交互方式为点击切换，替代报告中的静态表格，更直观。 2. **入场协议** (目标: 组织/流程) -> 使用HTML/CSS/JS构建的分步动画图，展示“突破-回踩”的完整过程。交互为自动播放或点击下一步，将文字描述的流程视觉化。 3. **仓位管理** (目标: 交互/计算) -> 使用HTML表单和JS构建的“金字塔加仓计算器”，并结合Chart.js动态展示持仓成本与止损位。用户可输入数据进行实时计算，将报告中的理论示例转化为实用工具。 4. **离场策略** (目标: 告知/识别) -> 使用Chart.js创建清晰的静态图表，展示“动量背离”形态，并用HTML/CSS图示“市场结构破坏”。目标是帮助用户建立形态识别能力。所有图表和图示均未使用SVG或Mermaid。 -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <style>
        body {
            font-family: 'Inter', 'Noto Sans SC', sans-serif;
            background-color: #FDFBF8;
            color: #383838;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            height: 300px;
            max-height: 400px;
        }
        @media (min-width: 768px) {
            .chart-container {
                height: 350px;
            }
        }
        .step-item {
            transition: all 0.5s ease-in-out;
        }
        .step-item.active {
            opacity: 1;
            transform: translateY(0);
        }
        .step-item.inactive {
            opacity: 0;
            transform: translateY(20px);
            display: none;
        }
        .btn-primary {
            background-color: #D97706; /* Amber-600 */
            color: white;
            transition: background-color 0.3s;
        }
        .btn-primary:hover {
            background-color: #B45309; /* Amber-700 */
        }
        .btn-secondary {
            background-color: #4B5563; /* Gray-600 */
            color: white;
            transition: background-color 0.3s;
        }
        .btn-secondary:hover {
            background-color: #374151; /* Gray-700 */
        }
        .indicator-line {
            border-style: solid;
            border-width: 2px;
            position: absolute;
            width: 100%;
            transition: all 0.5s ease-in-out;
        }
        .indicator-price {
            position: absolute;
            background-color: #4B5563;
            height: 20px;
            width: 5px;
            border-radius: 2px;
            transition: all 0.5s ease-in-out;
        }
    </style>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;700&family=Noto+Sans+SC:wght@400;700&display=swap" rel="stylesheet">
</head>
<body class="antialiased">

    <header class="bg-[#FDFBF8]/80 backdrop-blur-sm sticky top-0 z-50 border-b border-gray-200">
        <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
            <h1 class="text-xl md:text-2xl font-bold text-amber-700">DOGE 15M 交易策略 playbook</h1>
            <div class="hidden md:flex space-x-6 text-gray-600">
                <a href="#section-1" class="hover:text-amber-600">市场诊断</a>
                <a href="#section-2" class="hover:text-amber-600">入场协议</a>
                <a href="#section-3" class="hover:text-amber-600">仓位管理</a>
                <a href="#section-4" class="hover:text-amber-600">离场策略</a>
            </div>
        </nav>
    </header>

    <main class="container mx-auto px-6 py-12">
        
        <section id="intro" class="mb-16 text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-4">将理论转化为行动</h2>
            <p class="max-w-3xl mx-auto text-gray-600">本应用旨在将报告中复杂的交易系统，转化为一个直观、可交互的学习和决策辅助工具。它将引导您完成从识别市场状态到最终平仓的每一个关键步骤，帮助您建立纪律性、系统性的交易思维。</p>
        </section>

        <!-- Section 1: Market Diagnosis -->
        <section id="section-1" class="mb-20 scroll-mt-24">
            <div class="text-center mb-12">
                <span class="text-sm font-bold tracking-wider text-amber-600 uppercase">步骤 1</span>
                <h2 class="text-3xl font-bold mt-2">市场状态诊断：构建您的仪表盘</h2>
                 <p class="max-w-2xl mx-auto mt-4 text-gray-500">
                    在进行任何交易前，首要任务是准确判断当前市场处于何种状态。本模块将报告中的“市场状态诊断矩阵”转化为一个交互式仪表盘。您可以点击不同状态，观察EMA、布林带和ADX指标的典型特征，从而学习如何客观地识别市场是处于趋势中还是盘整中。
                </p>
            </div>
            
            <div class="bg-white p-6 md:p-8 rounded-2xl shadow-lg">
                <div class="flex flex-wrap justify-center gap-2 mb-8">
                    <button onclick="updateDashboard('uptrend')" class="dashboard-btn active btn-primary px-4 py-2 rounded-lg text-sm font-semibold">强劲上升趋势</button>
                    <button onclick="updateDashboard('downtrend')" class="dashboard-btn btn-secondary px-4 py-2 rounded-lg text-sm font-semibold">强劲下降趋势</button>
                    <button onclick="updateDashboard('ranging')" class="dashboard-btn btn-secondary px-4 py-2 rounded-lg text-sm font-semibold">盘整/积蓄能量</button>
                    <button onclick="updateDashboard('fading')" class="dashboard-btn btn-secondary px-4 py-2 rounded-lg text-sm font-semibold">趋势衰竭</button>
                </div>
                
                <div class="grid md:grid-cols-3 gap-6 text-sm">
                    <!-- EMA -->
                    <div class="border p-4 rounded-lg bg-gray-50">
                        <h4 class="font-bold mb-2">EMA (10, 20, 50)</h4>
                        <div id="ema-viz" class="relative h-24 w-full bg-white rounded my-2 overflow-hidden">
                           <div id="ema10" class="indicator-line" style="border-color: #3B82F6;"></div>
                           <div id="ema20" class="indicator-line" style="border-color: #F59E0B;"></div>
                           <div id="ema50" class="indicator-line" style="border-color: #EF4444;"></div>
                        </div>
                        <p id="ema-desc" class="text-gray-600"></p>
                    </div>
                    <!-- BOLL -->
                    <div class="border p-4 rounded-lg bg-gray-50">
                        <h4 class="font-bold mb-2">布林带 (20, 2)</h4>
                        <div id="boll-viz" class="relative h-24 w-full bg-white rounded my-2 overflow-hidden">
                            <div id="boll-upper" class="indicator-line" style="border-color: #10B981; border-style: dashed;"></div>
                            <div id="boll-middle" class="indicator-line" style="border-color: #6B7280; border-style: dotted;"></div>
                            <div id="boll-lower" class="indicator-line" style="border-color: #10B981; border-style: dashed;"></div>
                            <div class="indicator-price" style="left: 20%; top: 50%;"></div>
                            <div class="indicator-price" style="left: 40%; top: 40%;"></div>
                            <div class="indicator-price" style="left: 60%; top: 30%;"></div>
                            <div class="indicator-price" style="left: 80%; top: 20%;"></div>
                        </div>
                        <p id="boll-desc" class="text-gray-600"></p>
                    </div>
                    <!-- ADX -->
                    <div class="border p-4 rounded-lg bg-gray-50">
                        <h4 class="font-bold mb-2">ADX (14)</h4>
                        <div class="relative h-24 w-full bg-white rounded my-2">
                             <div class="absolute bottom-0 left-0 w-full h-px bg-gray-300"></div>
                             <div class="absolute" style="bottom: 25%; left:0; width: 100%; border-top: 1px dashed #EF4444;">
                                 <span class="text-xs text-red-500 absolute -top-4 right-0">25</span>
                             </div>
                             <div id="adx-bar" class="absolute bottom-0 left-0 w-full bg-amber-500 transition-all duration-500" style="height: 10%;"></div>
                        </div>
                        <p id="adx-desc" class="text-gray-600"></p>
                    </div>
                </div>

                <div id="dashboard-summary" class="mt-8 p-4 bg-amber-50 border border-amber-200 rounded-lg">
                    <h4 class="font-bold text-amber-800">可执行的解读:</h4>
                    <p id="summary-desc" class="text-amber-700"></p>
                </div>
            </div>
        </section>

        <!-- Section 2: Entry Protocol -->
        <section id="section-2" class="mb-20 scroll-mt-24">
            <div class="text-center mb-12">
                <span class="text-sm font-bold tracking-wider text-amber-600 uppercase">步骤 2</span>
                <h2 class="text-3xl font-bold mt-2">突破-回踩入场协议</h2>
                 <p class="max-w-2xl mx-auto mt-4 text-gray-500">
                    识别趋势后，下一步是寻找高概率的入场点。本策略的核心是耐心等待“突破-回踩”信号，以过滤假突破。这个交互式动画将为您分步展示这一过程，从识别盘整区到最终在确认支撑位后入场，帮助您内化这一关键的交易纪律。
                </p>
            </div>
            
            <div class="bg-white p-6 md:p-8 rounded-2xl shadow-lg">
                <div class="relative w-full max-w-2xl mx-auto h-64 bg-gray-50 rounded-lg p-4 border" id="breakout-animation-container">
                    <!-- Price Action Path -->
                    <svg class="absolute top-0 left-0 w-full h-full" viewBox="0 0 300 150">
                        <path id="price-path" d="M 10 75 L 50 75 L 60 65 L 80 85 L 110 70 L 150 75 L 180 30 L 210 50 L 230 45 L 260 80 L 290 70" stroke="#4B5563" fill="none" stroke-width="2" stroke-dasharray="500" stroke-dashoffset="500"></path>
                    </svg>
                    <!-- Range lines -->
                    <div class="absolute w-1/2 h-px bg-red-500" style="top: 40%; left: 5%;">
                        <span class="text-xs text-red-500 absolute -top-5 left-0">阻力位</span>
                    </div>
                     <div class="absolute w-1/2 h-px bg-green-500" style="top: 60%; left: 5%;">
                         <span class="text-xs text-green-500 absolute -bottom-5 left-0">支撑位</span>
                     </div>
                     <!-- Volume Bars -->
                     <div class="absolute bottom-4 flex items-end gap-1" style="left: 5%; height: 20%;">
                         <div class="w-2 bg-gray-300" style="height: 20%;"></div>
                         <div class="w-2 bg-gray-300" style="height: 40%;"></div>
                         <div class="w-2 bg-gray-300" style="height: 30%;"></div>
                         <div class="w-2 bg-gray-300" style="height: 50%;"></div>
                         <div id="volume-spike" class="w-2 bg-red-500 transition-all duration-500" style="height: 10%;"></div>
                     </div>
                </div>
                 <div class="mt-6 flex flex-wrap justify-center items-start gap-4" id="breakout-steps">
                    <!-- Steps will be injected by JS -->
                </div>
            </div>
        </section>

        <!-- Section 3: Position Management -->
        <section id="section-3" class="mb-20 scroll-mt-24">
            <div class="text-center mb-12">
                <span class="text-sm font-bold tracking-wider text-amber-600 uppercase">步骤 3</span>
                <h2 class="text-3xl font-bold mt-2">仓位管理：金字塔加仓与ATR止损</h2>
                 <p class="max-w-2xl mx-auto mt-4 text-gray-500">
                    盈利的交易不仅在于入场，更在于管理。本模块将报告中的“金字塔式加仓”和“ATR止损”策略结合成一个实用的计算器。您可以输入您的交易数据，系统将自动计算您的平均成本和动态的、基于市场波动率的统一止损位。这能帮您在放大盈利的同时，严格控制风险。
                </p>
            </div>

            <div class="bg-white p-6 md:p-8 rounded-2xl shadow-lg">
                <div class="grid lg:grid-cols-2 gap-8">
                    <div>
                        <h4 class="text-xl font-bold mb-4">金字塔加仓计算器</h4>
                        <div id="pyramid-entries">
                            <!-- Entry form will be injected by JS -->
                        </div>
                        <button onclick="addPyramidEntry()" class="mt-4 w-full text-sm btn-secondary px-4 py-2 rounded-lg">+ 添加加仓</button>
                    </div>
                    <div>
                        <h4 class="text-xl font-bold mb-4">交易状态总览</h4>
                        <div class="space-y-3 text-sm p-4 bg-gray-50 rounded-lg border">
                            <div class="flex justify-between"><span>总持仓量:</span><span id="total-position" class="font-bold">0</span></div>
                            <div class="flex justify-between"><span>平均成本:</span><span id="avg-cost" class="font-bold">0.00</span></div>
                            <div class="flex justify-between text-red-600"><span>统一ATR止损位:</span><span id="unified-stoploss" class="font-bold">0.00</span></div>
                        </div>
                        <div class="mt-4">
                            <div class="chart-container" style="height:250px;">
                                <canvas id="pyramidChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 4: Exit Strategy -->
        <section id="section-4" class="scroll-mt-24">
            <div class="text-center mb-12">
                <span class="text-sm font-bold tracking-wider text-amber-600 uppercase">步骤 4</span>
                <h2 class="text-3xl font-bold mt-2">离场策略：识别趋势衰竭</h2>
                 <p class="max-w-2xl mx-auto mt-4 text-gray-500">
                    善于离场是保护利润的关键。本模块通过清晰的图表案例，为您展示两种核心的离场信号。第一部分是“动量背离”，一种预示趋势可能反转的领先信号。第二部分是“市场结构破坏”，一个确认趋势已经结束的最终信号。学习识别这些模式，能帮助您在最佳时机退出交易。
                </p>
            </div>

             <div class="grid md:grid-cols-2 gap-8">
                 <div class="bg-white p-6 rounded-2xl shadow-lg">
                     <h4 class="text-xl font-bold mb-4">领先信号：动量背离</h4>
                     <p class="text-sm text-gray-600 mb-4">当价格创出新高，而动量指标（如RSI）未能同步创出新高时，形成“看跌背离”，这是趋势动能衰竭的早期预警。</p>
                     <div class="chart-container">
                         <canvas id="divergenceChart"></canvas>
                     </div>
                 </div>
                 <div class="bg-white p-6 rounded-2xl shadow-lg">
                     <h4 class="text-xl font-bold mb-4">确认信号：市场结构破坏</h4>
                     <p class="text-sm text-gray-600 mb-4">上升趋势由一系列“更高的低点”定义。当价格跌破最后一个显著的“更高的低点”时，市场结构被破坏，是最终的离场信号。</p>
                     <div class="chart-container">
                          <canvas id="structureBreakChart"></canvas>
                     </div>
                 </div>
             </div>
        </section>

    </main>

    <footer class="bg-gray-800 text-white mt-20">
        <div class="container mx-auto px-6 py-8 text-center text-sm">
            <p>本应用仅为基于报告内容的教育和演示工具，不构成任何投资建议。</p>
            <p class="mt-2 opacity-70">市场有风险，投资需谨慎。</p>
        </div>
    </footer>


    <script>
        document.addEventListener('DOMContentLoaded', function () {
            
            // Section 1: Dashboard
            const dashboardData = {
                uptrend: {
                    ema: {
                        desc: '多头排列 (10>20>50)，斜率陡峭，均线间距清晰。',
                        styles: {
                            ema10: { top: '20%', transform: 'rotate(-15deg) scaleX(1.1)', left: '-5%' },
                            ema20: { top: '40%', transform: 'rotate(-15deg) scaleX(1.1)', left: '-5%' },
                            ema50: { top: '60%', transform: 'rotate(-15deg) scaleX(1.1)', left: '-5%' }
                        }
                    },
                    boll: {
                        desc: '布林带开口向上扩张；价格沿上轨运行。',
                        styles: {
                           upper: { top: '10%', height: '30%', transform: 'rotate(-20deg) scaleX(1.2)', left: '-10%' },
                           middle: { top: '45%', transform: 'rotate(-15deg) scaleX(1.1)', left: '-5%' },
                           lower: { top: '80%', transform: 'rotate(-5deg) scaleX(1.05)', left: '-2.5%' },
                           prices: [{left: '20%', top: '25%'}, {left: '40%', top: '20%'}, {left: '60%', top: '15%'}, {left: '80%', top: '12%'}]
                        }
                    },
                    adx: {
                        desc: 'ADX > 25 且持续上升；+DI > -DI。',
                        style: { height: '80%' }
                    },
                    summary: '趋势已确认。在回调时寻找金字塔式加仓机会。'
                },
                downtrend: {
                    ema: {
                        desc: '空头排列 (10<20<50)，斜率陡峭，均线间距清晰。',
                        styles: {
                            ema10: { top: '80%', transform: 'rotate(15deg) scaleX(1.1)', left: '-5%' },
                            ema20: { top: '60%', transform: 'rotate(15deg) scaleX(1.1)', left: '-5%' },
                            ema50: { top: '40%', transform: 'rotate(15deg) scaleX(1.1)', left: '-5%' }
                        }
                    },
                    boll: {
                        desc: '布林带开口向下扩张；价格沿下轨运行。',
                        styles: {
                           upper: { top: '20%', transform: 'rotate(5deg) scaleX(1.05)', left: '-2.5%' },
                           middle: { top: '55%', transform: 'rotate(15deg) scaleX(1.1)', left: '-5%' },
                           lower: { top: '90%', height: '30%', transform: 'rotate(20deg) scaleX(1.2)', left: '-10%' },
                           prices: [{left: '20%', top: '75%'}, {left: '40%', top: '80%'}, {left: '60%', top: '85%'}, {left: '80%', top: '88%'}]
                        }
                    },
                    adx: {
                        desc: 'ADX > 25 且持续上升；-DI > +DI。',
                        style: { height: '75%' }
                    },
                    summary: '趋势已确认。在反弹时寻找金字塔式加仓机会。'
                },
                ranging: {
                     ema: {
                        desc: '均线走平，相互缠绕，频繁交叉。',
                        styles: {
                            ema10: { top: '50%', transform: 'rotate(2deg) scaleX(1)', left: '0%' },
                            ema20: { top: '52%', transform: 'rotate(-2deg) scaleX(1)', left: '0%' },
                            ema50: { top: '48%', transform: 'rotate(0deg) scaleX(1)', left: '0%' }
                        }
                    },
                    boll: {
                        desc: '布林带通道狭窄且水平运行（挤压）。',
                        styles: {
                           upper: { top: '40%', transform: 'rotate(0deg) scaleX(1)', left: '0%' },
                           middle: { top: '50%', transform: 'rotate(0deg) scaleX(1)', left: '0%' },
                           lower: { top: '60%', transform: 'rotate(0deg) scaleX(1)', left: '0%' },
                           prices: [{left: '20%', top: '55%'}, {left: '40%', top: '45%'}, {left: '60%', top: '52%'}, {left: '80%', top: '48%'}]
                        }
                    },
                    adx: {
                        desc: 'ADX < 25。',
                        style: { height: '15%' }
                    },
                    summary: '禁止交易。市场正在为下一次突破做准备。耐心等待。'
                },
                fading: {
                    ema: {
                        desc: '均线开始收敛，价格跌破关键均线。',
                         styles: {
                            ema10: { top: '30%', transform: 'rotate(-5deg) scaleX(1.05)', left: '-2.5%' },
                            ema20: { top: '45%', transform: 'rotate(-8deg) scaleX(1.05)', left: '-2.5%' },
                            ema50: { top: '60%', transform: 'rotate(-10deg) scaleX(1.05)', left: '-2.5%' }
                        }
                    },
                    boll: {
                        desc: '价格从上轨回落至通道内部，向中轨靠拢。',
                         styles: {
                           upper: { top: '15%', height: '30%', transform: 'rotate(-10deg) scaleX(1.1)', left: '-5%' },
                           middle: { top: '50%', transform: 'rotate(-5deg) scaleX(1.05)', left: '-2.5%' },
                           lower: { top: '85%', transform: 'rotate(0deg) scaleX(1)', left: '0%' },
                           prices: [{left: '20%', top: '25%'}, {left: '40%', top: '20%'}, {left: '60%', top: '40%'}, {left: '80%', top: '50%'}]
                        }
                    },
                    adx: {
                        desc: 'ADX 从高位（如 >40）回落。',
                        style: { height: '40%' }
                    },
                    summary: '现有头寸应考虑获利了结。不应再开立新的顺势仓位。'
                }
            };

            window.updateDashboard = function(state) {
                const data = dashboardData[state];
                
                document.querySelectorAll('.dashboard-btn').forEach(btn => {
                    btn.classList.remove('active', 'btn-primary');
                    btn.classList.add('btn-secondary');
                });
                const activeBtn = document.querySelector(`button[onclick="updateDashboard('${state}')"]`);
                activeBtn.classList.add('active', 'btn-primary');
                activeBtn.classList.remove('btn-secondary');

                // EMA
                document.getElementById('ema-desc').textContent = data.ema.desc;
                Object.assign(document.getElementById('ema10').style, data.ema.styles.ema10);
                Object.assign(document.getElementById('ema20').style, data.ema.styles.ema20);
                Object.assign(document.getElementById('ema50').style, data.ema.styles.ema50);
                
                // BOLL
                document.getElementById('boll-desc').textContent = data.boll.desc;
                const bollViz = document.getElementById('boll-viz');
                Object.assign(bollViz.querySelector('#boll-upper').style, data.boll.styles.upper);
                Object.assign(bollViz.querySelector('#boll-middle').style, data.boll.styles.middle);
                Object.assign(bollViz.querySelector('#boll-lower').style, data.boll.styles.lower);
                const prices = bollViz.querySelectorAll('.indicator-price');
                prices.forEach((p, i) => {
                    Object.assign(p.style, data.boll.styles.prices[i]);
                });

                // ADX
                document.getElementById('adx-desc').textContent = data.adx.desc;
                document.getElementById('adx-bar').style.height = data.adx.style.height;

                // Summary
                document.getElementById('summary-desc').textContent = data.summary;
            }
            updateDashboard('uptrend');

            // Section 2: Breakout Animation
            const breakoutStepsContainer = document.getElementById('breakout-steps');
            const pricePath = document.getElementById('price-path');
            const volumeSpike = document.getElementById('volume-spike');
            
            const breakoutSteps = [
                { title: '1. 识别盘整区间', content: '市场进入横盘，价格在明确的支撑和阻力位之间波动。成交量低迷。' },
                { title: '2. 初始突破', content: '价格决定性地收在阻力位上方，关键是这根K线的成交量必须显著放大。' },
                { title: '3. 等待回踩', content: '不要追高！耐心等待价格回落，重新测试刚刚被突破的阻力位。此前的阻力现在应转为支撑。' },
                { title: '4. 入场触发', content: '在新的支撑位上出现看涨K线形态（如Pin Bar），确认支撑有效后，方可入场。' },
            ];
            
            let currentStep = 0;
            breakoutSteps.forEach((step, index) => {
                const stepEl = document.createElement('div');
                stepEl.className = `step-item p-4 border rounded-lg w-full md:w-1/4 ${index === 0 ? 'active' : 'inactive'}`;
                stepEl.innerHTML = `<h5 class="font-bold">${step.title}</h5><p class="text-xs text-gray-500 mt-1">${step.content}</p>`;
                breakoutStepsContainer.appendChild(stepEl);
            });

            function runAnimationStep(step) {
                const steps = breakoutStepsContainer.querySelectorAll('.step-item');
                steps.forEach((s, i) => {
                    s.classList.toggle('active', i === step);
                    s.classList.toggle('inactive', i !== step);
                });

                if (step === 0) {
                    pricePath.style.strokeDashoffset = '500';
                    volumeSpike.style.height = '10%';
                } else if (step === 1) {
                    pricePath.style.strokeDashoffset = '280'; // Breakout part
                    volumeSpike.style.height = '80%';
                } else if (step === 2) {
                    pricePath.style.strokeDashoffset = '210'; // Retest part
                    volumeSpike.style.height = '80%';
                } else if (step === 3) {
                    pricePath.style.strokeDashoffset = '0'; // Move up after retest
                }
            }

            setInterval(() => {
                currentStep = (currentStep + 1) % breakoutSteps.length;
                runAnimationStep(currentStep);
            }, 3000);
            runAnimationStep(0);


            // Section 3: Pyramid Calculator
            const pyramidEntriesContainer = document.getElementById('pyramid-entries');
            let pyramidChart;
            let entryCount = 0;

            window.addPyramidEntry = function() {
                entryCount++;
                const entryHtml = `
                    <div class="entry-item mt-4 border p-3 rounded-lg bg-gray-50/50" data-id="${entryCount}">
                        <h5 class="font-semibold text-sm mb-2">${entryCount === 1 ? '初始入场' : `第 ${entryCount - 1} 次加仓`}</h5>
                        <div class="grid grid-cols-3 gap-2">
                            <div>
                                <label class="text-xs text-gray-500">价格</label>
                                <input type="number" class="w-full p-1 border rounded text-sm" name="price" oninput="calculatePyramid()" placeholder="e.g. 0.15" step="0.001">
                            </div>
                            <div>
                                <label class="text-xs text-gray-500">数量</label>
                                <input type="number" class="w-full p-1 border rounded text-sm" name="size" oninput="calculatePyramid()" placeholder="e.g. 10000">
                            </div>
                            <div>
                                <label class="text-xs text-gray-500">当时ATR</label>
                                <input type="number" class="w-full p-1 border rounded text-sm" name="atr" oninput="calculatePyramid()" placeholder="e.g. 0.003" step="0.001">
                            </div>
                        </div>
                    </div>
                `;
                pyramidEntriesContainer.insertAdjacentHTML('beforeend', entryHtml);
            }
            
            window.calculatePyramid = function() {
                const entries = [];
                document.querySelectorAll('.entry-item').forEach(item => {
                    const price = parseFloat(item.querySelector('[name="price"]').value);
                    const size = parseFloat(item.querySelector('[name="size"]').value);
                    const atr = parseFloat(item.querySelector('[name="atr"]').value);
                    if (!isNaN(price) && price > 0 && !isNaN(size) && size > 0 && !isNaN(atr) && atr > 0) {
                        entries.push({ price, size, atr });
                    }
                });

                if (entries.length === 0) {
                    document.getElementById('total-position').textContent = '0';
                    document.getElementById('avg-cost').textContent = '0.00';
                    document.getElementById('unified-stoploss').textContent = '0.00';
                    updatePyramidChart([], 0, 0);
                    return;
                };

                let totalCost = 0;
                let totalPosition = 0;
                entries.forEach(e => {
                    totalCost += e.price * e.size;
                    totalPosition += e.size;
                });

                const avgCost = totalCost / totalPosition;
                const lastAtr = entries[entries.length - 1].atr;
                const unifiedStoploss = avgCost - (2 * lastAtr);

                document.getElementById('total-position').textContent = totalPosition.toLocaleString();
                document.getElementById('avg-cost').textContent = avgCost.toFixed(4);
                document.getElementById('unified-stoploss').textContent = unifiedStoploss.toFixed(4);
                
                updatePyramidChart(entries, avgCost, unifiedStoploss);
            }

            function updatePyramidChart(entries, avgCost, unifiedStoploss) {
                const labels = entries.map((e, i) => i === 0 ? '入场' : `加仓${i}`);
                const prices = entries.map(e => e.price);

                if (pyramidChart) {
                    pyramidChart.data.labels = labels;
                    pyramidChart.data.datasets[0].data = prices;
                    pyramidChart.options.plugins.annotation.annotations.avgLine.yMin = avgCost;
                    pyramidChart.options.plugins.annotation.annotations.avgLine.yMax = avgCost;
                    pyramidChart.options.plugins.annotation.annotations.avgLine.label.content = `平均成本: ${avgCost.toFixed(4)}`;
                    pyramidChart.options.plugins.annotation.annotations.stopLine.yMin = unifiedStoploss;
                    pyramidChart.options.plugins.annotation.annotations.stopLine.yMax = unifiedStoploss;
                    pyramidChart.options.plugins.annotation.annotations.stopLine.label.content = `统一止损: ${unifiedStoploss.toFixed(4)}`;
                    pyramidChart.update();
                }
            }

            function initPyramidChart() {
                 const ctx = document.getElementById('pyramidChart').getContext('2d');
                 pyramidChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: [],
                        datasets: [{
                            label: '入场/加仓价格',
                            data: [],
                            borderColor: '#3B82F6',
                            backgroundColor: '#BFDBFE',
                            pointRadius: 5,
                            pointBackgroundColor: '#3B82F6',
                            tension: 0.1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: { y: { beginAtZero: false } },
                        plugins: {
                            legend: { display: false },
                            annotation: {
                                annotations: {
                                    avgLine: {
                                        type: 'line',
                                        yMin: 0,
                                        yMax: 0,
                                        borderColor: '#16A34A',
                                        borderWidth: 2,
                                        borderDash: [6, 6],
                                        label: {
                                            content: '平均成本',
                                            enabled: true,
                                            position: 'end',
                                            backgroundColor: '#16A34A'
                                        }
                                    },
                                    stopLine: {
                                        type: 'line',
                                        yMin: 0,
                                        yMax: 0,
                                        borderColor: '#DC2626',
                                        borderWidth: 2,
                                        label: {
                                            content: '统一止损',
                                            enabled: true,
                                            position: 'end',
                                            backgroundColor: '#DC2626'
                                        }
                                    }
                                }
                            }
                        }
                    }
                });
            }
            initPyramidChart();
            addPyramidEntry();
            
            // Section 4: Exit charts
            // Divergence Chart
            const divCtx = document.getElementById('divergenceChart').getContext('2d');
            const priceData = [10, 12, 15, 13, 18, 16, 20, 18, 19, 17];
            const rsiData = [60, 65, 75, 68, 80, 72, 78, 70, 70, 65];
            const divLabels = ['T1', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'T8', 'T9', 'T10'];

            new Chart(divCtx, {
                type: 'line',
                data: {
                    labels: divLabels,
                    datasets: [
                        {
                            label: '价格',
                            data: priceData,
                            borderColor: '#4B5563',
                            yAxisID: 'y',
                            tension: 0.2
                        },
                        {
                            label: 'RSI',
                            data: rsiData,
                            borderColor: '#D97706',
                            backgroundColor: '#FEF3C7',
                            fill: true,
                            yAxisID: 'y1',
                            tension: 0.2
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: { type: 'linear', display: true, position: 'left', title: { display: true, text: '价格' }},
                        y1: { type: 'linear', display: true, position: 'right', title: { display: true, text: 'RSI' }, grid: { drawOnChartArea: false }},
                    },
                    plugins: {
                        legend: { display: false },
                        title: { display: true, text: '看跌背离示例' },
                        annotation: {
                            annotations: {
                                priceLine: {
                                    type: 'line',
                                    xMin: 'T5',
                                    yMin: 18,
                                    xMax: 'T7',
                                    yMax: 20,
                                    borderColor: '#4B5563',
                                    borderWidth: 2,
                                    label: {
                                        content: '更高的高点',
                                        enabled: true,
                                        position: 'start',
                                        color: '#4B5563',
                                        font: {weight: 'bold'}
                                    }
                                },
                                rsiLine: {
                                    type: 'line',
                                    scaleID: 'y1',
                                    xMin: 'T5',
                                    yMin: 80,
                                    xMax: 'T7',
                                    yMax: 78,
                                    borderColor: '#D97706',
                                    borderWidth: 2,
                                    label: {
                                        content: '更低的高点',
                                        enabled: true,
                                        position: 'start',
                                        color: '#D97706',
                                        font: {weight: 'bold'}
                                    }
                                }
                            }
                        }
                    }
                }
            });

            // Structure Break Chart
            const sbCtx = document.getElementById('structureBreakChart').getContext('2d');
            new Chart(sbCtx, {
                type: 'line',
                data: {
                     labels: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11'],
                     datasets: [{
                         label: 'Price',
                         data: [10, 15, 12, 18, 14, 22, 18, 20, 13, 16, 11],
                         borderColor: '#4B5563',
                         pointBackgroundColor: '#4B5563',
                         pointRadius: (context) => (context.dataIndex === 4 ? 6 : 3),
                         pointBorderColor: (context) => (context.dataIndex === 4 ? '#DC2626' : '#4B5563'),
                         tension: 0.1,
                     }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false },
                        title: { display: true, text: '市场结构破坏示例' },
                         annotation: {
                            annotations: {
                                line1: {
                                    type: 'line',
                                    xMin: 0,
                                    xMax: 10,
                                    yMin: 14,
                                    yMax: 14,
                                    borderColor: '#DC2626',
                                    borderWidth: 2,
                                    borderDash: [6, 6],
                                    label: {
                                        content: '最后一个更高的低点 (Higher Low)',
                                        enabled: true,
                                        position: 'start',
                                        font: { size: 10 },
                                        backgroundColor: '#DC2626'
                                    }
                                },
                                box1: {
                                    type: 'box',
                                    xMin: 8, xMax: 9,
                                    yMin: 12, yMax: 14,
                                    backgroundColor: 'rgba(220, 38, 38, 0.25)',
                                    borderColor: 'rgba(220, 38, 38, 1)',
                                    borderWidth: 1
                                }
                            }
                        }
                    },
                    scales: {
                        x: { grid: { display: false } },
                        y: { beginAtZero: true, grid: { display: false } }
                    }
                }
            });
        });
    </script>
</body>
</html>
