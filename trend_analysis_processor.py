import re
import pandas as pd
import numpy as np
import ast # 用于安全地评估字符串为Python字典

# --- 猴子补丁开始 ---
if not hasattr(np, 'NaN'):
    np.NaN = np.nan # 确保 numpy.NaN 可用
# --- 猴子补丁结束 ---

class TrendReportParser:
    """
    负责解析原始趋势报告的类。
    遵循关注点分离原则，专注于数据提取。
    现在直接接收并标准化字典列表。
    """
    def __init__(self):
        pass

    def parse(self, report_dicts: list[dict]) -> list[dict]:
        """
        解析给定的字典列表，提取并标准化关键信息。
        现在不再从字符串中解析，而是直接处理传入的字典列表。
        """
        reports = []
        for report_dict in report_dicts:
            standardized_data = self._standardize_report_data(report_dict)
            if standardized_data: # 确保标准化成功
                reports.append(standardized_data)
        return reports

    def _standardize_report_data(self, data: dict) -> dict:
        """
        标准化解析后的字典数据，统一键名和类型。
        这是面向接口编程中的“接口定义”部分。
        """
        if not isinstance(data, dict):
            print(f"警告: 传入的数据不是字典类型，跳过标准化。数据: {data}")
            return None

        standardized = {
            "数据点周期": int(data.get("data_points")) if data.get("data_points") is not None else np.nan, # 从新的data_points键获取
            "基础趋势": data.get("trend"),
            "基础趋势斜率": float(data.get("slope")) if data.get("slope") is not None else np.nan,
            "相对变化": float(data.get("relative_change_percent")) if data.get("relative_change_percent") is not None else np.nan,
            "移动平均趋势": data.get("moving_average_trend"),
            "波动性": float(data.get("volatility")) if data.get("volatility") is not None else np.nan,
        }
        
        # 极值拐点分析
        extrema_analysis = data.get("extrema_analysis", {})
        standardized["当前百分位"] = float(extrema_analysis.get("current_percentile")) if extrema_analysis.get("current_percentile") is not None else np.nan
        standardized["当前拐点概率"] = float(extrema_analysis.get("current_turning_probability")) if extrema_analysis.get("current_turning_probability") is not None else np.nan
        standardized["预测类型"] = extrema_analysis.get("prediction_type")
        standardized["发现拐点数"] = extrema_analysis.get("turning_points_found", 0)

        # === 重点修改部分开始 ===
        strongest_turning_point = extrema_analysis.get("strongest_turning_point") # 获取可能为None的值
        
        # 检查 strongest_turning_point 是否为 None
        if strongest_turning_point: # 如果 strongest_turning_point 不是 None
            standardized["最强拐点类型"] = strongest_turning_point.get("type")
            standardized["最强拐点可信度"] = float(strongest_turning_point.get("credibility")) if strongest_turning_point.get("credibility") is not None else np.nan
            standardized["最强拐点距离"] = int(strongest_turning_point.get("distance_from_end")) if strongest_turning_point.get("distance_from_end") is not None else np.nan
        else: # 如果 strongest_turning_point 是 None (例如发现拐点数为0时)
            standardized["最强拐点类型"] = None
            standardized["最强拐点可信度"] = np.nan
            standardized["最强拐点距离"] = np.nan
        # === 重点修改部分结束 ===
        
        # 回撤分析
        drawdown_analysis = data.get("drawdown_analysis", {})
        current_status = drawdown_analysis.get("current_status", {})
        recovery_analysis = drawdown_analysis.get("recovery_analysis", {})

        standardized["回撤强度"] = drawdown_analysis.get("intensity_label")
        standardized["回撤模式"] = drawdown_analysis.get("pattern_type")
        standardized["风险档案"] = drawdown_analysis.get("risk_profile")
        standardized["当前回撤"] = float(current_status.get("current_drawdown")) if current_status.get("current_drawdown") is not None else np.nan
        standardized["正在回撤"] = bool(current_status.get("is_in_drawdown", False)) # 默认为False
        standardized["恢复能力"] = recovery_analysis.get("capability")
        standardized["恢复能力评分"] = int(recovery_analysis.get("score")) if recovery_analysis.get("score") is not None else np.nan

        # 对于当前回撤为0的情况，判断为不在回撤中
        if standardized["当前回撤"] == 0.0:
            standardized["正在回撤"] = False

        # 趋势确认
        trend_confirmation = data.get("trend_confirmation", {})
        standardized["趋势一致性"] = float(trend_confirmation.get("trend_consistency_percent")) if trend_confirmation.get("trend_consistency_percent") is not None else np.nan
        standardized["趋势强度"] = float(trend_confirmation.get("trend_strength")) if trend_confirmation.get("trend_strength") is not None else np.nan
        standardized["趋势强度描述"] = trend_confirmation.get("strength_label")

        return standardized

# (TrendEvaluator 和 UniqueTrendVerifier 类保持不变，与您上次提供的版本相同)
class TrendEvaluator:
    """
    根据解析后的趋势数据评估投资建议。
    遵循面向接口编程原则，只依赖于Parser提供的标准化数据。
    """
    def __init__(self):
        pass

    def evaluate(self, parsed_reports: list[dict]) -> dict:
        """
        根据多个时间周期的趋势报告，提供综合投资建议。
        """
        if not parsed_reports:
            return {
                "主导趋势": "不明确",
                "建议操作": "观望",
                "风险提示": ["无有效报告数据进行评估。"],
                "潜在机会": []
            }

        summary_df = pd.DataFrame(parsed_reports)
        
        # 初始化判断变量
        all_periods_declining = True
        all_periods_rising = True
        
        # 风险和回撤状态汇总
        overall_severe_drawdown = False
        overall_high_risk_profile = False
        
        # 拐点信号汇总
        bottom_pivot_signals_count = 0
        top_pivot_signals_count = 0

        for _, row in summary_df.iterrows():
            # 趋势方向判断
            # 如果任一周期不是下降趋势，则all_periods_declining为False
            if "下降" not in str(row["基础趋势"]) and "下降" not in str(row["移动平均趋势"]):
                all_periods_declining = False
            # 如果任一周期不是上升趋势，则all_periods_rising为False
            if "上升" not in str(row["基础趋势"]) and "上升" not in str(row["移动平均趋势"]):
                all_periods_rising = False
            
            # 回撤风险判断
            if row["正在回撤"] and row["回撤强度"] in ["严重", "重度", "极端"]:
                overall_severe_drawdown = True
            if row["风险档案"] == "高风险":
                overall_high_risk_profile = True
                
            # 拐点信号计数
            if row["预测类型"] == "可能形成底部拐点":
                bottom_pivot_signals_count += 1
            # 峰值拐点通常预测“趋势可能发生变化”
            if row["最强拐点类型"] == "峰值" and row["预测类型"] == "趋势可能发生变化":
                top_pivot_signals_count += 1

        # 综合投资建议初始化
        overall_advice = {
            "主导趋势": "不明确",
            "建议操作": "观望",
            "风险提示": [],
            "潜在机会": []
        }

        # 判断主导趋势和建议操作
        # 强下降趋势的判断：所有周期都在下降，且存在严重回撤
        if all_periods_declining and overall_severe_drawdown:
            overall_advice["主导趋势"] = "强烈下降"
            overall_advice["建议操作"] = "考虑做空"
            overall_advice["风险提示"].append("市场处于深度下降趋势，回撤严重，风险极高。")
            overall_advice["潜在机会"].append("可考虑在反弹时做空，或等待底部信号。")
        # 强上升趋势的判断：所有周期都在上升，且没有严重回撤
        elif all_periods_rising and not overall_severe_drawdown:
            overall_advice["主导趋势"] = "强烈上升"
            overall_advice["建议操作"] = "考虑做多"
            overall_advice["潜在机会"].append("在趋势确认度高时顺势做多，但需关注拐点风险。")
        else:
            # 否则，趋势混合或不明确
            overall_advice["主导趋势"] = "混合/震荡"
            overall_advice["建议操作"] = "谨慎观望"
            overall_advice["风险提示"].append("不同周期趋势可能存在分歧，或市场处于高不确定性区域。")
            
        # 叠加拐点和回撤的风险提示
        # 如果任一周期当前拐点概率为100%
        if any(row.get("当前拐点概率") == 100.0 for _, row in summary_df.iterrows()):
            overall_advice["风险提示"].append("当前处于高概率拐点区域，操作需谨慎。")
        
        # 如果存在整体严重回撤
        if overall_severe_drawdown:
            overall_advice["风险提示"].append("多周期存在严重回撤，务必做好风险控制和资金管理。")
        
        # 如果存在高风险档案
        if overall_high_risk_profile:
            overall_advice["风险提示"].append("多周期风险档案为高风险，波动性较大。")

        # 具体的拐点信号对建议操作的影响
        if top_pivot_signals_count > 0:
            overall_advice["风险提示"].append(f"发现{top_pivot_signals_count}个顶部拐点信号，短期可能面临回调。")
            # 如果之前建议做多，现在要更谨慎
            if overall_advice["建议操作"] == "考虑做多":
                overall_advice["建议操作"] = "做多需极度谨慎，或考虑减仓。"

        if bottom_pivot_signals_count > 0:
            overall_advice["潜在机会"].append(f"发现{bottom_pivot_signals_count}个底部拐点信号，关注反弹机会。")
            # 如果之前建议做空，现在要更谨慎
            if overall_advice["建议操作"] == "考虑做空":
                overall_advice["建议操作"] = "做空需谨慎，关注潜在反转。"

        # 移除重复的风险提示和潜在机会
        overall_advice["风险提示"] = list(set(overall_advice["风险提示"]))
        overall_advice["潜在机会"] = list(set(overall_advice["潜在机会"]))

        # 如果没有具体建议，添加通用建议
        if not overall_advice["风险提示"] and not overall_advice["潜在机会"] and overall_advice["建议操作"] == "观望":
            overall_advice["风险提示"].append("市场趋势不明确，建议保持观望，等待更清晰的信号。")

        return overall_advice

class UniqueTrendVerifier:
    """
    整合解析和评估功能，提供唯一的综合检验结果。
    这是面向接口编程的“客户接口”。
    """
    def __init__(self):
        self.parser = TrendReportParser()
        self.evaluator = TrendEvaluator()

    def verify_and_advise(self, report_dicts: list[dict]) -> dict:
        """
        处理原始报告字典列表，生成唯一的综合投资检验结果和建议。
        """
        # 1. 解析数据 (现在直接标准化传入的字典列表)
        parsed_reports = self.parser.parse(report_dicts)
        
        if not parsed_reports:
            return {
                "状态": "失败",
                "消息": "未能从报告内容中解析出任何有效的趋势数据。",
                "综合趋势判断": "未知",
                "建议操作": "请检查输入报告格式是否正确。"
            }

        # 2. 评估趋势并给出综合建议
        overall_evaluation = self.evaluator.evaluate(parsed_reports)

        return {
            "状态": "成功",
            "综合趋势判断": overall_evaluation["主导趋势"],
            "建议操作": overall_evaluation["建议操作"],
            "风险提示": overall_evaluation["风险提示"],
            "潜在机会": overall_evaluation["潜在机会"],
            "原始解析数据": parsed_reports # 包含原始解析出的详细数据，方便追溯
        }


# 使用示例 (假设 sample_report_content_with_dict 是一个字典列表)
if __name__ == "__main__":
    # 将您提供的三个字典分析结果整理成一个列表
    all_analysis_results_sample = [
        {'trend': '强烈上升', 'slope': np.float64(0.11645157712948263), 'relative_change_percent': np.float64(-1.69), 'moving_average_trend': '上升',
        'volatility': np.float64(1.3201), 'start_value': np.float64(69.66944366524619), 'end_value': np.float64(68.49526287835633), 'min_value': np.float64(68.49526287835633), 'max_value': np.float64(72.46429555973535), 'data_points': 15, 'extrema_analysis': {'current_percentile': np.float64(np.nan), 'is_extreme_high': np.False_, 'is_extreme_low': np.False_, 'is_high_zone': np.False_, 'is_low_zone': np.False_, 'turning_points_found': 1, 'credible_turning_points': [{'index': 9, 'value': np.float64(72.46429555973535), 'strength': np.float64(0.3619164199994316), 'credibility': np.float64(46.5), 'distance_from_end': 5, 'type': '峰值'}], 'strongest_turning_point': {'index': 9, 'value': np.float64(72.46429555973535), 'strength': np.float64(0.3619164199994316), 'credibility': np.float64(46.5), 'distance_from_end': 5, 'type': '峰值'}, 'current_turning_probability': 100, 'prediction_type': '趋势可能发生变化', 'extrema_summary': {'total_peaks': 1, 'total_troughs': 0, 'avg_peak_credibility': np.float64(46.5), 'avg_trough_credibility': 0}}, 'drawdown_analysis': {'max_drawdown_percent': np.float64(-5.48), 'intensity_label': '严重', 'intensity_level': 4, 'pattern_type': '周期性回撤', 'risk_profile': '中等风险', 'drawdown_periods_count': 2, 'drawdown_periods': [{'start': 1, 'end': 3, 'duration': 3, 'max_drawdown': np.float64(-1.0185135810504025), 'recovery_speed': np.float64(0.10074885531975715)}, {'start': 11, 'end': 14, 'duration': 4, 'max_drawdown': np.float64(-5.477225233090384), 'recovery_speed': None, 'ongoing': True}], 'recovery_analysis': {'capability': '较弱恢复能力', 'score': 40, 'avg_recovery_speed': np.float64(0.101), 'recovery_consistency': 0}, 'current_status': {'current_drawdown': np.float64(-5.48), 'is_in_drawdown': np.True_, 'time_from_peak': np.int64(0), 'peak_value': np.float64(72.4643)}, 'risk_metrics': {'avg_drawdown_duration': np.float64(3.5), 'max_drawdown_duration': 4, 'drawdown_frequency': 13.333333333333334}}, 'trend_confirmation': {'trend_consistency_percent': 66.7, 'trend_strength': np.float64(8.82), 'strength_label': '强劲', 'short_term_slope': np.float64(-0.931577), 'medium_term_slope': np.float64(-0.179433), 'long_term_slope': np.float64(0.116452)}},

        {'trend': '强烈上升', 'slope': np.float64(0.20676981709850253), 'relative_change_percent': np.float64(3.01), 'moving_average_trend': '上升', 'volatility': np.float64(1.8241), 'start_value': np.float64(66.49201621773369), 'end_value': np.float64(68.49526287835633), 'min_value': np.float64(66.49201621773369), 'max_value': np.float64(72.46429555973535), 'data_points': 25, 'extrema_analysis': {'current_percentile': np.float64(np.nan), 'is_extreme_high': np.False_, 'is_extreme_low': np.False_, 'is_high_zone': np.False_, 'is_low_zone': np.False_, 'turning_points_found': 2, 'credible_turning_points': [{'index': 19, 'value': np.float64(72.46429555973535), 'strength': np.float64(0.3619164199994316), 'credibility': np.float64(48.4), 'distance_from_end': 5, 'type': '峰值'}, {'index': 12, 'value': np.float64(68.9598509196734), 'strength': np.float64(0.4910108771139363), 'credibility': np.float64(31.9), 'distance_from_end': 12, 'type': '谷值'}], 'strongest_turning_point': {'index': 19, 'value': np.float64(72.46429555973535), 'strength': np.float64(0.3619164199994316), 'credibility': np.float64(48.4), 'distance_from_end': 5, 'type': '峰值'}, 'current_turning_probability': 100, 'prediction_type': '趋势可能发生变化', 'extrema_summary': {'total_peaks': 1, 'total_troughs': 1, 'avg_peak_credibility': np.float64(48.4), 'avg_trough_credibility': np.float64(31.9)}}, 'drawdown_analysis': {'max_drawdown_percent': np.float64(-5.48), 'intensity_label': '严重', 'intensity_level': 4, 'pattern_type': '周期性回撤', 'risk_profile': '中等风险', 'drawdown_periods_count': 2, 'drawdown_periods': [{'start': 11, 'end': 13, 'duration': 3, 'max_drawdown': np.float64(-1.2139249562414034), 'recovery_speed': np.float64(0.10054995476694444)}, {'start': 21, 'end': 24, 'duration': 4, 'max_drawdown': np.float64(-5.477225233090384), 'recovery_speed': None, 'ongoing': True}], 'recovery_analysis': {'capability': '较弱恢复能力', 'score': 40, 'avg_recovery_speed': np.float64(0.101), 'recovery_consistency': 0}, 'current_status': {'current_drawdown': np.float64(-5.48), 'is_in_drawdown': np.True_, 'time_from_peak': np.int64(0), 'peak_value': np.float64(72.4643)}, 'risk_metrics': {'avg_drawdown_duration': np.float64(3.5), 'max_drawdown_duration': 4, 'drawdown_frequency': 8.0}}, 'trend_confirmation': {'trend_consistency_percent': 66.7, 'trend_strength': np.float64(11.34), 'strength_label': '强劲', 'short_term_slope': np.float64(-0.931577), 'medium_term_slope': np.float64(-0.179433), 'long_term_slope': np.float64(0.20677)}},

        {'trend': '强烈上升', 'slope': np.float64(0.6295389941382977), 'relative_change_percent': np.float64(50.88), 'moving_average_trend': '上升', 'volatility': np.float64(8.4354), 'start_value': np.float64(45.39603520382836), 'end_value': np.float64(68.49526287835633), 'min_value': np.float64(45.39603520382836), 'max_value': np.float64(72.46429555973535), 'data_points': 40, 'extrema_analysis': {'current_percentile': np.float64(np.nan), 'is_extreme_high': np.False_, 'is_extreme_low': np.False_, 'is_high_zone': np.False_, 'is_low_zone': np.False_, 'turning_points_found': 0, 'credible_turning_points': [], 'strongest_turning_point': None, 'current_turning_probability': 100, 'prediction_type': '趋势可能发生变化', 'extrema_summary': {'total_peaks': 0, 'total_troughs': 0, 'avg_peak_credibility': 0, 'avg_trough_credibility': 0}}, 'drawdown_analysis': {'max_drawdown_percent': np.float64(-5.48), 'intensity_label': '严重', 'intensity_level': 4, 'pattern_type': '周期性回撤', 'risk_profile': '中等风险', 'drawdown_periods_count': 2, 'drawdown_periods': [{'start': 26, 'end': 28, 'duration': 3, 'max_drawdown': np.float64(-1.2139249562414034), 'recovery_speed': np.float64(0.10054995476694444)}, {'start': 36, 'end': 39, 'duration': 4, 'max_drawdown': np.float64(-5.477225233090384), 'recovery_speed': None, 'ongoing': True}], 'recovery_analysis': {'capability': '较弱恢复能力', 'score': 40, 'avg_recovery_speed': np.float64(0.101), 'recovery_consistency': 0}, 'current_status': {'current_drawdown': np.float64(-5.48), 'is_in_drawdown': np.True_, 'time_from_peak': np.int64(0), 'peak_value': np.float64(72.4643)}, 'risk_metrics': {'avg_drawdown_duration': np.float64(3.5), 'max_drawdown_duration': 4, 'drawdown_frequency': 5.0}}, 'trend_confirmation': {'trend_consistency_percent': 66.7, 'trend_strength': np.float64(7.46), 'strength_label': '强劲', 'short_term_slope': np.float64(-0.931577), 'medium_term_slope': np.float64(-0.179433), 'long_term_slope': np.float64(0.629539)}}
    ]

    verifier = UniqueTrendVerifier()
    result = verifier.verify_and_advise(all_analysis_results_sample)

    print("===== 综合趋势检验结果与投资建议 =====")
    print(f"状态: {result['状态']}")
    print(f"综合趋势判断: {result['综合趋势判断']}")
    print(f"建议操作: {result['建议操作']}")
    print("风险提示:")
    for risk in result['风险提示']:
        print(f"  - {risk}")
    print("潜在机会:")
    for opp in result['潜在机会']:
        print(f"  - {opp}")
    print("\n===== 原始解析数据预览 (前3条) =====")
    for i, data in enumerate(result['原始解析数据'][:3]):
        print(f"--- 报告 {i+1} (周期: {data['数据点周期']}) ---")
        for k, v in data.items():
            print(f"  {k}: {v}")
        print("-" * 20)
    if len(result['原始解析数据']) > 3:
        print(f"... 还有 {len(result['原始解析数据']) - 3} 条报告未显示 ...")