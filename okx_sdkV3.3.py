import os
import requests
import pandas as pd
import time
import sqlite3
from datetime import datetime, timezone
import pytz
from typing import List, Tuple, Optional
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, as_completed

class BinanceKlineDataFetcher:
    def __init__(self,
                 db_subfolder: str = "binance_kline_db",
                 db_filename: str = "kline.db",
                 api_urls: Optional[List[str]] = None):
        script_dir = os.path.dirname(os.path.abspath(__file__))
        self.data_dir = os.path.join(script_dir, db_subfolder)
        os.makedirs(self.data_dir, exist_ok=True)
        self.db_path = os.path.join(self.data_dir, db_filename)
        self.api_urls = api_urls or [
            "https://api.binance.com",
            "https://api-gcp.binance.com",
            "https://api1.binance.com",
            "https://api2.binance.com",
            "https://api3.binance.com",
            "https://api4.binance.com"
        ]
        self._init_db()

    def _init_db(self):
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS kline_tables (
                    table_name TEXT PRIMARY KEY,
                    symbol TEXT,
                    interval TEXT
                )
            ''')
            conn.commit()

    def _get_table_name(self, symbol: str, interval: str) -> str:
        base = symbol.replace('USDT', '').lower()
        return f"{base}{interval}"

    def datetime_to_open_time(self, dt_str: str) -> int:
        tz = pytz.timezone('Asia/Shanghai')
        dt = datetime.strptime(dt_str, "%Y-%m-%d %H:%M")
        dt = tz.localize(dt)
        # 正确的代码
        open_time = int(dt.astimezone(timezone.utc).timestamp() * 1000)
        return open_time

    def get_klines_batch(self, symbol: str, interval: str, start_time: int, end_time: int, limit: int = 1500, max_retry: int = 5) -> Optional[list]:
        path = "/api/v3/klines"
        params = {
            'symbol': symbol,
            'interval': interval,
            'startTime': start_time,
            'endTime': end_time,
            'limit': limit
        }
        for attempt in range(max_retry):
            for api_url in self.api_urls:
                url = api_url + path
                try:
                    response = requests.get(url, params=params, timeout=10)
                    response.raise_for_status()
                    return response.json()
                except requests.exceptions.RequestException as e:
                    print(f"[{api_url}] API请求失败: {e}，尝试下一个API")
                    continue
            print(f"所有API都失败，重试 {attempt+1}/{max_retry}")
            time.sleep(2 ** attempt)
        print("多次重试后所有API均失败。")
        return None

    def get_historical_klines(self, symbol: str, interval: str, start_time_str: str, end_time_str: str) -> Optional[pd.DataFrame]:
        start_open_time = self.datetime_to_open_time(start_time_str)
        end_open_time = self.datetime_to_open_time(end_time_str)
        all_klines = []
        current_start = start_open_time

        # 计算K线间隔毫秒数
        interval_map = {'m': 60 * 1000, 'h': 60 * 60 * 1000, 'd': 24 * 60 * 60 * 1000}
        number = int(''.join(filter(str.isdigit, interval)))
        unit = ''.join(filter(str.isalpha, interval))
        ms_per_kline = number * interval_map[unit]
        total_klines = (end_open_time - start_open_time) // ms_per_kline + 1
        total_batches = int(total_klines // 1500 + 1)

        with tqdm(total=total_batches, desc=f"{symbol} {interval} downloading", ncols=120) as pbar:
            while current_start < end_open_time:
                batch_start = time.time()
                klines_batch = self.get_klines_batch(
                    symbol=symbol,
                    interval=interval,
                    start_time=current_start,
                    end_time=end_open_time,
                    limit=1500
                )
                if not klines_batch or len(klines_batch) == 0:
                    break
                all_klines.extend(klines_batch)
                last_open_time = klines_batch[-1][0]
                if current_start >= last_open_time:
                    break
                current_start = last_open_time + 1
                pbar.update(1)
                # 根据API上限动态调整sleep
                batch_elapsed = time.time() - batch_start
                min_interval = 60.0 / 1000  # 1000次/分钟的最短间隔=0.06s
                if batch_elapsed < min_interval:
                    time.sleep(min_interval - batch_elapsed)
        if not all_klines:
            return None
        return self.convert_to_dataframe(all_klines)

    def convert_to_dataframe(self, klines_data: list) -> pd.DataFrame:
        columns = [
            'open_time', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_volume', 'count', 'taker_buy_volume',
            'taker_buy_quote_volume', 'ignore'
        ]
        df = pd.DataFrame(klines_data, columns=columns)
        df['open_time'] = pd.to_datetime(df['open_time'], unit='ms', utc=True).dt.tz_convert('Asia/Shanghai')
        for col in ['open', 'high', 'low', 'close']:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        df['confirm'] = 1
        if len(df) > 0:
            df = df.sort_values('open_time', ascending=False).reset_index(drop=True)
            df.iloc[0, df.columns.get_loc('confirm')] = 0
        result_df = df[['open_time', 'open', 'high', 'low', 'close', 'confirm']].copy()
        return result_df

    def save_to_database(self, df: pd.DataFrame, symbol: str, interval: str):
        if df is None or df.empty:
            return
        table_name = self._get_table_name(symbol, interval)
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(f'''
                CREATE TABLE IF NOT EXISTS "{table_name}" (
                    open_time TEXT PRIMARY KEY,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    confirm INTEGER
                )
            ''')
            cursor.execute('''
                INSERT OR REPLACE INTO kline_tables (table_name, symbol, interval)
                VALUES (?, ?, ?)
            ''', (table_name, symbol, interval))
            data_tuples = [
                (
                    str(row['open_time']),
                    row['open'],
                    row['high'],
                    row['low'],
                    row['close'],
                    row['confirm']
                )
                for _, row in df.iterrows()
            ]
            cursor.executemany(f'''
                INSERT OR REPLACE INTO "{table_name}"
                (open_time, open, high, low, close, confirm)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', data_tuples)
            conn.commit()
        print(f"已保存{len(df)}条数据到表 {table_name}")

    def robust_save_to_database(self, df: pd.DataFrame, symbol: str, interval: str, max_retry: int = 10):
        for attempt in range(max_retry):
            try:
                self.save_to_database(df, symbol, interval)
                return
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e):
                    print(f"数据库锁定，重试第{attempt+1}次...")
                    time.sleep(0.5)
                else:
                    raise
        raise RuntimeError("多次重试后数据库依然被锁定")

    def _load_local_data(self, table_name: str, start_time_str: str, end_time_str: str) -> Optional[pd.DataFrame]:
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
                if not cursor.fetchone():
                    return None
                query = f'''
                    SELECT open_time, open, high, low, close, confirm
                    FROM "{table_name}"
                    WHERE open_time >= ? AND open_time <= ?
                    ORDER BY open_time DESC
                '''
                start_dt = pd.to_datetime(start_time_str).tz_localize('Asia/Shanghai')
                end_dt = pd.to_datetime(end_time_str).tz_localize('Asia/Shanghai')
                df = pd.read_sql_query(query, conn, params=[
                    str(start_dt), str(end_dt)
                ])
                if not df.empty:
                    df['open_time'] = pd.to_datetime(df['open_time'])
                    return df
                return None
        except Exception as e:
            print(f"加载本地数据失败: {e}")
            return None

    def _find_missing_ranges(self, local_data: pd.DataFrame, start_time_str: str, end_time_str: str, interval: str) -> List[Tuple[str, str]]:
        target_start = pd.to_datetime(start_time_str).tz_localize('Asia/Shanghai')
        target_end = pd.to_datetime(end_time_str).tz_localize('Asia/Shanghai')
        local_open_times = pd.to_datetime(local_data['open_time'])
        local_start = local_open_times.min()
        local_end = local_open_times.max()
        missing_ranges = []
        if target_start < local_start:
            gap_end = min(local_start, target_end)
            missing_ranges.append((
                target_start.strftime("%Y-%m-%d %H:%M"),
                gap_end.strftime("%Y-%m-%d %H:%M")
            ))
        if target_end > local_end:
            gap_start = max(local_end, target_start)
            missing_ranges.append((
                gap_start.strftime("%Y-%m-%d %H:%M"),
                target_end.strftime("%Y-%m-%d %H:%M")
            ))
        return missing_ranges

    def get_data_with_cache(self, symbol: str, interval: str, start_time_str: str, end_time_str: str) -> Optional[pd.DataFrame]:
        table_name = self._get_table_name(symbol, interval)
        local_data = self._load_local_data(table_name, start_time_str, end_time_str)
        if local_data is not None and not local_data.empty:
            print(f"本地已有{len(local_data)}条数据")
            missing_ranges = self._find_missing_ranges(local_data, start_time_str, end_time_str, interval)
            if not missing_ranges:
                return local_data
        else:
            print("本地无数据")
            missing_ranges = [(start_time_str, end_time_str)]
        for gap_start, gap_end in missing_ranges:
            print(f"补充缺失段: {gap_start} 到 {gap_end}")
            gap_data = self.get_historical_klines(symbol, interval, gap_start, gap_end)
            if gap_data is not None:
                self.robust_save_to_database(gap_data, symbol, interval)
        return self._load_local_data(table_name, start_time_str, end_time_str)

def fetch_symbol_interval(symbol, interval, start_time, end_time, api_urls):
    fetcher = BinanceKlineDataFetcher(api_urls=api_urls)
    print(f"\n开始获取 {symbol} {interval} {start_time} ~ {end_time} 数据")
    df = fetcher.get_data_with_cache(symbol, interval, start_time, end_time)
    if df is not None and not df.empty:
        print(f"{symbol} {interval} 共获取到 {len(df)} 条数据")
        return (symbol, interval, len(df))
    else:
        print(f"{symbol} {interval} 无数据")
        return (symbol, interval, 0)

if __name__ == "__main__":
    api_urls = [
        "https://api.binance.com",
        "https://api-gcp.binance.com",
        "https://api1.binance.com",
        "https://api2.binance.com",
        "https://api3.binance.com",
        "https://api4.binance.com"
    ]
    # symbols = ["DOGEUSDT", "ETHUSDT", "BTCUSDT"]
    # intervals = ["1m", "3m", "15m", "30m", "1h", "4h"]
    symbols = ["DOGEUSDT", ]
    intervals = [ "3m", "15m", "30m", "1h", "4h"]
    start_time = "2020-9-1 00:00"
    end_time = "2025-06-29 12:00"

    tasks = []
    with ThreadPoolExecutor(max_workers=1) as executor:
        for symbol in symbols:
            for interval in intervals:
                tasks.append(executor.submit(fetch_symbol_interval, symbol, interval, start_time, end_time, api_urls))
        for future in as_completed(tasks):
            symbol, interval, count = future.result()
            print(f"完成: {symbol} {interval}，共{count}条数据")