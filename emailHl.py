# -*- coding: utf-8 -*-

import smtplib
import os
import sys
import zipfile
import time
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.image import MIMEImage
from email.mime.application import MIMEApplication
from dotenv import load_dotenv
from PIL import Image
import io

# ==============================================================================
# 核心功能函数
# ==============================================================================
def compress_image(image_path: str, max_size_mb: float = 5.0, quality: int = 85) -> str:
    """
    压缩图片以避免邮箱限制
    
    Args:
        image_path (str): 原图片路径
        max_size_mb (float): 最大文件大小（MB）
        quality (int): 压缩质量 (1-100)
    
    Returns:
        str: 压缩后的图片路径
    """
    max_size_bytes = max_size_mb * 1024 * 1024
    current_size = os.path.getsize(image_path)
    
    if current_size <= max_size_bytes:
        print(f"图片大小 {current_size/1024/1024:.2f}MB，无需压缩")
        return image_path
    
    print(f"图片大小 {current_size/1024/1024:.2f}MB，开始压缩...")
    
    # 生成压缩后的文件名
    name, ext = os.path.splitext(image_path)
    compressed_path = f"{name}_compressed{ext}"
    
    try:
        with Image.open(image_path) as img:
            # 如果是PNG，转换为RGB（避免RGBA导致的问题）
            if img.mode in ('RGBA', 'LA'):
                # 创建白色背景
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'RGBA':
                    background.paste(img, mask=img.split()[-1])  # 使用alpha通道作为蒙版
                else:
                    background.paste(img)
                img = background
            elif img.mode != 'RGB':
                img = img.convert('RGB')
            
            # 逐步减小质量直到满足大小要求
            current_quality = quality
            while current_quality > 10:
                # 保存到内存缓冲区测试大小
                buffer = io.BytesIO()
                img.save(buffer, format='JPEG', quality=current_quality, optimize=True)
                
                if buffer.tell() <= max_size_bytes:
                    # 满足大小要求，保存到文件
                    with open(compressed_path, 'wb') as f:
                        f.write(buffer.getvalue())
                    
                    final_size = os.path.getsize(compressed_path)
                    print(f"压缩完成: {final_size/1024/1024:.2f}MB (质量: {current_quality})")
                    return compressed_path
                
                current_quality -= 10
            
            # 如果质量降到很低还是太大，尝试缩小尺寸
            print("降低质量无效，尝试缩小尺寸...")
            width, height = img.size
            scale_factor = 0.8
            
            while scale_factor > 0.3:
                new_width = int(width * scale_factor)
                new_height = int(height * scale_factor)
                resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                
                buffer = io.BytesIO()
                resized_img.save(buffer, format='JPEG', quality=70, optimize=True)
                
                if buffer.tell() <= max_size_bytes:
                    with open(compressed_path, 'wb') as f:
                        f.write(buffer.getvalue())
                    
                    final_size = os.path.getsize(compressed_path)
                    print(f"压缩完成: {final_size/1024/1024:.2f}MB (尺寸: {new_width}x{new_height})")
                    return compressed_path
                
                scale_factor -= 0.1
            
            print("警告: 无法将图片压缩到指定大小，使用原图片")
            return image_path
            
    except Exception as e:
        print(f"压缩图片时出错: {e}，使用原图片")
        return image_path

def create_zip_attachment(image_path: str) -> str:
    """
    将图片打包为ZIP文件，避免被邮箱安全检测拦截
    """
    zip_path = os.path.splitext(image_path)[0] + "_attachment.zip"
    
    try:
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            zipf.write(image_path, os.path.basename(image_path))
        
        zip_size = os.path.getsize(zip_path)
        print(f"创建ZIP附件: {zip_path} ({zip_size/1024/1024:.2f}MB)")
        return zip_path
        
    except Exception as e:
        print(f"创建ZIP文件失败: {e}")
        return image_path

def send_email_with_attachment_enhanced(list_a: list, list_b: list, image_path: str, 
                                       compress_image_flag: bool = True,
                                       use_zip: bool = False,
                                       max_image_size_mb: float = 5.0) -> bool:
    """
    增强版邮件发送，解决图片被屏蔽的问题
    
    Args:
        list_a (list): 第一个数据列表
        list_b (list): 第二个数据列表  
        image_path (str): 本地图片文件路径
        compress_image_flag (bool): 是否压缩图片
        use_zip (bool): 是否将图片打包为ZIP
        max_image_size_mb (float): 图片最大大小(MB)
    
    Returns:
        bool: 发送是否成功
    """
    load_dotenv('123.env')

    # 1. 获取邮箱配置
    host = os.getenv("EMAIL_HOST")
    sender = os.getenv("EMAIL_SENDER")
    password = os.getenv("EMAIL_PASSWORD")
    receiver = os.getenv("EMAIL_RECEIVER")

    required_configs = {"主机": host, "发件人": sender, "密码": password, "收件人": receiver}
    missing_configs = [name for name, value in required_configs.items() if not value]
    if missing_configs:
        print(f"[错误] .env 文件中缺少以下配置: {', '.join(missing_configs)}", file=sys.stderr)
        return False

    # 2. 验证和处理图片文件
    if not os.path.exists(image_path):
        print(f"[错误] 图片文件不存在: {image_path}", file=sys.stderr)
        return False
    
    original_size = os.path.getsize(image_path)
    print(f"原始图片信息: {image_path}, 大小: {original_size/1024/1024:.2f}MB")
    
    # 处理图片 - 根据参数决定是否压缩和打包
    processed_image_path = image_path
    temp_files = []  # 记录临时文件，用于清理
    
    try:
        # 压缩图片（如果需要）
        if compress_image_flag:
            compressed_path = compress_image(image_path, max_image_size_mb)
            if compressed_path != image_path:
                processed_image_path = compressed_path
                temp_files.append(compressed_path)
        
        # 打包为ZIP（如果需要）
        if use_zip:
            zip_path = create_zip_attachment(processed_image_path)
            if zip_path.endswith('.zip'):
                processed_image_path = zip_path
                temp_files.append(zip_path)
        
        final_size = os.path.getsize(processed_image_path)
        print(f"最终附件: {processed_image_path}, 大小: {final_size/1024/1024:.2f}MB")

        # 3. 创建邮件
        msg = MIMEMultipart()
        msg['Subject'] = "【数据报告】包含列表和图片附件"
        msg['From'] = sender
        msg['To'] = receiver

        # 4. 邮件正文
        body_content = "您好，\n\n这是本次任务生成的数据报告。\n\n"
        body_content += "--- 数据列表 A ---\n"
        body_content += "\n".join(f"- {item}" for item in list_a)
        body_content += "\n\n--- 数据列表 B ---\n"
        body_content += "\n".join(f"- {item}" for item in list_b)
        body_content += "\n\n详情请见附件"
        
        if use_zip:
            body_content += "（ZIP压缩包，请下载后解压查看图片）"
        elif compress_image_flag and processed_image_path != image_path:
            body_content += "（图片已优化压缩）"
        
        body_content += "。\n\n祝好！"
        msg.attach(MIMEText(body_content, 'plain', 'utf-8'))

        # 5. 添加附件
        print(f"正在添加附件: {processed_image_path}")
        with open(processed_image_path, 'rb') as f:
            file_data = f.read()
        
        attachment_name = os.path.basename(processed_image_path)
        
        if processed_image_path.endswith('.zip'):
            # ZIP文件附件
            attachment = MIMEApplication(file_data, _subtype='zip')
            attachment.add_header('Content-Disposition', 'attachment', filename=attachment_name)
        else:
            # 图片附件
            file_ext = os.path.splitext(processed_image_path)[1].lower()
            if file_ext in ['.jpg', '.jpeg']:
                attachment = MIMEImage(file_data, 'jpeg')
            elif file_ext == '.png':
                attachment = MIMEImage(file_data, 'png')
            else:
                attachment = MIMEImage(file_data, 'png')
            
            attachment.add_header('Content-Disposition', 'attachment', 
                                filename=('utf-8', '', attachment_name))
        
        msg.attach(attachment)
        print(f"附件添加成功: {attachment_name}")

        # 6. 发送邮件 - 简化版，发送成功后立即返回
        print(f"正在连接到邮件服务器 {host}...")
        
        try:
            with smtplib.SMTP_SSL(host, 465, timeout=30) as server:
                server.set_debuglevel(0)
                print("SSL连接建立成功")
                server.login(sender, password)
                print("登录成功")
                
                # 发送邮件
                server.send_message(msg)
                print(f"✅ 邮件已成功发送至 {receiver}！")
                
                # 发送成功后立即返回，忽略后续可能的VPN连接错误
                return True
                
        except smtplib.SMTPAuthenticationError as auth_error:
            print(f"[错误] 邮箱认证失败: {auth_error}", file=sys.stderr)
            return False
            
        except Exception as e:
            error_msg = str(e)
            print(f"SSL方式出现问题: {error_msg}")
            
            # 如果是VPN导致的特定错误，直接忽略
            if "(-1," in error_msg or b'\x00\x00\x00' in str(e).encode('utf-8', errors='ignore'):
                print("⚠️  检测到VPN连接异常，但邮件可能已发送成功")
                print("💡 请检查收件箱确认是否收到邮件")
                return True  # 当作成功处理
            
            # 其他错误尝试备用方式
            try:
                print("尝试备用方式: SMTP + STARTTLS (端口587)")
                with smtplib.SMTP(host, 587, timeout=30) as server:
                    server.ehlo()
                    server.starttls()
                    server.ehlo()
                    server.login(sender, password)
                    server.send_message(msg)
                    print(f"✅ 邮件发送成功！(备用方式)")
                    return True
            except Exception as backup_error:
                print(f"备用方式也失败: {backup_error}")
                return False

    except Exception as e:
        print(f"[错误] 邮件发送过程中出现异常: {e}", file=sys.stderr)
        return False
    
    finally:
        # 清理临时文件
        for temp_file in temp_files:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    print(f"已清理临时文件: {temp_file}")
            except:
                pass

# ==============================================================================
# 示例用法
# ==============================================================================
if __name__ == '__main__':
    
    # 准备测试数据
    demo_list_a = ["信号触发于 2024-07-01 10:30:00", "指标RSI: 75.3", "交易对: BTC/USDT"]
    demo_list_b = ["策略名称: AlphaGo", "风险等级: 高", "建议仓位: 10%"]
    
    # 使用指定的本地图片
    image_path = "dogeusdt_rsi_price_dual_axis.png"
    
    # 检查图片文件
    if not os.path.exists(image_path):
        print(f"[错误] 图片文件不存在: {image_path}")
        sys.exit(1)
    
    print(f"找到图片文件: {image_path}")
    original_size = os.path.getsize(image_path)
    print(f"原始图片大小: {original_size/1024/1024:.2f}MB")

    print("\n" + "="*20 + " 开始发送邮件 " + "="*20)
    
    # 直接使用模式1：直接发送原图
    success = send_email_with_attachment_enhanced(
        list_a=demo_list_a,
        list_b=demo_list_b,
        image_path=image_path,
        compress_image_flag=False,  # 不压缩
        use_zip=False,              # 不打包ZIP
        max_image_size_mb=50.0      # 使用QQ邮箱最大限制
    )
    
    print("="*20 + " 发送任务结束 " + "="*20 + "\n")

    if success:
        print("✅ 任务流程执行成功。")
    else:
        print("❌ 任务流程执行失败，请检查上面的错误信息。")