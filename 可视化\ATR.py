import pandas as pd
import numpy as np
from typing import Union, Dict, List, Optional
from okx_sdkV1 import get_kline_data, OKXClient


def calculate_atr(
    data: Union[pd.DataFrame, Dict[str, List[float]]], 
    period: int = 14,
    method: str = 'wilder',
    price_cols: Optional[Dict[str, str]] = None,
    return_components: bool = False,
    normalize: bool = False,
    percentile_filter: Optional[float] = None
) -> Union[pd.Series, Dict]:
    """计算Average True Range (ATR)指标
    
    参数:
    data: K线数据，DataFrame或字典格式，需包含high/low/close列
    period: ATR计算周期，默认14
    method: 计算方法 'wilder'/'sma'/'ema'，默认'wilder'
    price_cols: 自定义列名映射，如{'high': 'High', 'low': 'Low', 'close': 'Close'}
    return_components: 是否返回详细计算组件，默认False
    normalize: 是否标准化为百分比，默认False
    percentile_filter: TR异常值过滤百分位数(0-100)，默认None
    
    返回:
    简单模式: ATR的pandas.Series
    详细模式: 包含ATR、TR、统计信息等的字典
    """
    
    # 数据预处理
    if isinstance(data, dict):
        df = pd.DataFrame(data)
    else:
        df = data.copy()
    
    # 设置列名映射
    cols = {'high': 'high', 'low': 'low', 'close': 'close'}
    if price_cols:
        cols.update(price_cols)
    
    # 检查必要列是否存在
    required_cols = [cols['high'], cols['low'], cols['close']]
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        raise ValueError(f"缺少必要的列: {missing_cols}")
    
    # 获取价格数据
    high = df[cols['high']]
    low = df[cols['low']]
    close = df[cols['close']]
    prev_close = close.shift(1)
    
    # 计算True Range的三个组件
    tr1 = high - low  # 当前高低价差
    tr2 = (high - prev_close).abs()  # 当前高价与前收盘价差
    tr3 = (low - prev_close).abs()   # 当前低价与前收盘价差
    
    # 计算True Range（取三者最大值）
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    
    # 异常值过滤
    if percentile_filter:
        threshold = np.percentile(tr.dropna(), percentile_filter)
        tr = tr.where(tr <= threshold, threshold)
    
    # 根据方法计算ATR
    if method == 'wilder':
        # Wilder原始方法
        atr = tr.ewm(alpha=1/period, adjust=False).mean()
    elif method == 'sma':
        # 简单移动平均
        atr = tr.rolling(window=period).mean()
    elif method == 'ema':
        # 指数移动平均
        atr = tr.ewm(span=period, adjust=False).mean()
    else:
        raise ValueError(f"不支持的方法: {method}")
    
    # 标准化ATR
    atr_normalized = None
    if normalize:
        atr_normalized = (atr / close) * 100
    
    # 计算统计信息
    atr_stats = {
        'mean': atr.mean(),
        'std': atr.std(),
        'min': atr.min(),
        'max': atr.max(),
        'current': atr.iloc[-1] if len(atr) > 0 else np.nan,
        'percentile_25': atr.quantile(0.25),
        'percentile_75': atr.quantile(0.75)
    }
    
    # 返回结果
    if return_components:
        tr_components = pd.DataFrame({
            'tr1_hl': tr1,
            'tr2_hc': tr2,
            'tr3_lc': tr3,
            'tr_final': tr
        })
        
        result = {
            'atr': atr,
            'tr': tr,
            'tr_components': tr_components,
            'stats': atr_stats
        }
        
        if normalize:
            result['atr_normalized'] = atr_normalized
            
        return result
    else:
        return atr


def atr_signals(
    atr_data: pd.Series,
    entry_price: float,
    stop_multiplier: float = 2.0,
    target_multiplier: float = 2.0,
    position_type: str = 'long'
) -> Dict[str, float]:
    """基于ATR计算止损和目标价位
    
    参数:
    atr_data: ATR数据Series
    entry_price: 入场价格
    stop_multiplier: 止损倍数，默认2.0
    target_multiplier: 目标倍数，默认2.0
    position_type: 持仓类型 'long'/'short'，默认'long'
    
    返回:
    包含止损价、目标价等信息的字典
    """
    
    # 获取当前ATR值
    current_atr = atr_data.iloc[-1]
    
    # 计算止损和目标价位
    if position_type.lower() == 'long':
        stop_loss = entry_price - (current_atr * stop_multiplier)
        target_price = entry_price + (current_atr * target_multiplier)
    else:  # short
        stop_loss = entry_price + (current_atr * stop_multiplier)
        target_price = entry_price - (current_atr * target_multiplier)
    
    # 计算风险收益比
    risk_amount = abs(entry_price - stop_loss)
    reward_amount = abs(target_price - entry_price)
    risk_reward_ratio = reward_amount / risk_amount if risk_amount > 0 else np.inf
    
    return {
        'entry_price': entry_price,
        'stop_loss': stop_loss,
        'target_price': target_price,
        'current_atr': current_atr,
        'risk_amount': risk_amount,
        'reward_amount': reward_amount,
        'risk_reward_ratio': risk_reward_ratio
    }



def get_holding_time_suggestions(timeframe: str, position_type: str) -> dict:
    """根据K线周期和持仓类型提供持仓时间建议
    
    参数:
    timeframe: K线周期
    position_type: 持仓类型
    
    返回:
    包含时间建议的字典
    """
    
    # K线周期对应的基础持仓时间建议（小时）
    timeframe_mapping = {
        '1m': {'min': 0.1, 'optimal': 0.5, 'max': 2},       # 1分钟线: 6分钟-2小时
        '5m': {'min': 0.5, 'optimal': 2, 'max': 8},         # 5分钟线: 30分钟-8小时  
        '15m': {'min': 1, 'optimal': 4, 'max': 12},         # 15分钟线: 1-12小时
        '30m': {'min': 2, 'optimal': 6, 'max': 18},         # 30分钟线: 2-18小时
        '1H': {'min': 2, 'optimal': 8, 'max': 24},          # 1小时线: 2-24小时
        '4H': {'min': 8, 'optimal': 24, 'max': 72},         # 4小时线: 8小时-3天
        '1D': {'min': 24, 'optimal': 168, 'max': 720},      # 日线: 1天-1个月
        '1W': {'min': 168, 'optimal': 720, 'max': 2160}     # 周线: 1周-3个月
    }
    
    # 获取基础时间设置
    base_times = timeframe_mapping.get(timeframe, {'min': 4, 'optimal': 24, 'max': 72})
    
    # 根据持仓类型调整
    if position_type.lower() == 'short':
        # 空头持仓通常持有时间更短
        adjustment_factor = 0.7
    else:
        # 多头持仓
        adjustment_factor = 1.0
    
    # 计算调整后的时间
    min_hours = base_times['min'] * adjustment_factor
    optimal_hours = base_times['optimal'] * adjustment_factor
    max_hours = base_times['max'] * adjustment_factor
    
    # 格式化时间显示
    def format_time(hours):
        if hours < 1:
            return f"{int(hours * 60)}分钟"
        elif hours < 24:
            return f"{hours:.1f}小时"
        elif hours < 168:
            return f"{hours/24:.1f}天"
        else:
            return f"{hours/168:.1f}周"
    
    return {
        '最短持仓时间': format_time(min_hours),
        '最优持仓时间': format_time(optimal_hours),
        '最长持仓时间': format_time(max_hours),
        '监控频率建议': f"每{timeframe}检查一次",
        '风控建议': f"超过{format_time(max_hours)}应考虑减仓或平仓"
    }

def get_comprehensive_trading_signals(
    df: pd.DataFrame,
    symbol: str,
    timeframe: str,
    atr_method: str = 'ema',
    atr_period: int = 14,
    stop_multiplier: float = 0.5,
    target_multiplier: float = 2.5
) -> Dict[str, Dict]:
    """获取简化的交易信号，同时返回多头和空头建议
    
    参数:
    df: K线数据DataFrame
    symbol: 虚拟币名称
    timeframe: K线周期
    atr_method: ATR计算方法，默认'ema'
    atr_period: ATR周期，默认14
    stop_multiplier: 止损倍数，默认0.5
    target_multiplier: 目标价位倍数，默认2.5
    
    返回:
    简化的交易信号字典
    """
    
    try:
        # 计算ATR
        atr_result = calculate_atr(
            df, 
            period=atr_period, 
            method=atr_method,
            return_components=True,
            normalize=True
        )
        
        current_price = float(df['close'].iloc[-1])
        current_atr = float(atr_result['atr'].iloc[-1])
        
        # 计算多头信号
        long_signals = atr_signals(
            atr_result['atr'],
            entry_price=current_price,
            stop_multiplier=stop_multiplier,
            target_multiplier=target_multiplier,
            position_type='long'
        )
        
        # 计算空头信号
        short_signals = atr_signals(
            atr_result['atr'],
            entry_price=current_price,
            stop_multiplier=stop_multiplier,
            target_multiplier=target_multiplier,
            position_type='short'
        )
        
        # 获取时间管理建议
        long_time_suggestions = get_holding_time_suggestions(timeframe, 'long')
        short_time_suggestions = get_holding_time_suggestions(timeframe, 'short')
        
        # 返回简化的结果，字段名称使用中文
        return {
            '交易对': symbol,
            'K线周期': timeframe,
            '当前价格': round(current_price, 5),
            'ATR值': round(current_atr, 6),
            'ATR百分比': round((current_atr/current_price)*100, 2),
            '多头': {
                '入场': round(float(long_signals['entry_price']), 4),
                '止损': round(float(long_signals['stop_loss']), 4),
                '目标': round(float(long_signals['target_price']), 4),
                '风险收益比': round(float(long_signals['risk_reward_ratio']), 1),
                '风险': round((float(long_signals['risk_amount']) / float(long_signals['entry_price'])) * 100, 1),
                '收益': round((float(long_signals['reward_amount']) / float(long_signals['entry_price'])) * 100, 1),
                '建议持仓': long_time_suggestions['最优持仓时间'],
                '最长': long_time_suggestions['最长持仓时间']
            },
            '空头': {
                '入场': round(float(short_signals['entry_price']), 4),
                '止损': round(float(short_signals['stop_loss']), 4),
                '目标': round(float(short_signals['target_price']), 4),
                '风险收益比': round(float(short_signals['risk_reward_ratio']), 1),
                '风险': round((float(short_signals['risk_amount']) / float(short_signals['entry_price'])) * 100, 1),
                '收益': round((float(short_signals['reward_amount']) / float(short_signals['entry_price'])) * 100, 1),
                '建议持仓': short_time_suggestions['最优持仓时间'],
                '最长': short_time_suggestions['最长持仓时间']
            }
        }
        
    except Exception as e:
        print(f"错误：获取交易信号失败 - {e}")
        return {}
# 使用示例

def main(
    symbol: str = 'DOGE-USDT-SWAP',
    timeframe: str = '4H',
    atr_method: str = 'wilder',
    atr_period: int = 14,
    position_type: str = 'long',
    stop_multiplier: float = 2.0,
    target_multiplier: float = 2.5,
    count: int = 200
):
    """OKX交易所ATR分析主函数
    
    参数:
    symbol: 交易对，默认'DOGE-USDT-SWAP'
    timeframe: K线周期，默认'4H'
    atr_method: ATR计算方法，默认'wilder'
    atr_period: ATR周期，默认14
    position_type: 持仓类型 'long'/'short'，默认'long'
    stop_multiplier: 止损倍数，默认2.0
    target_multiplier: 目标倍数，默认2.5
    count: K线数量，默认200
    """
    
    print("=== OKX交易所ATR分析系统 ===\n")
    symbol= 'DOGE-USDT-SWAP'
    timeframe= '15m'
    atr_method= 'ema' # ATR计算方法：'wilder'/'sma'/'ema'
    atr_period = 14
    position_type= 'kong'# 持仓类型：'long'多头/'short'空头
    stop_multiplier= 0.5   # 止损倍数
    target_multiplier = 2.5  # 目标价位倍数
    count= 200
    # 初始化客户端
    try:
        dotenv_path = r'C:\Users\<USER>\Desktop\my_python\claudeV1\量化\123.env'
        client = OKXClient(dotenv_path=dotenv_path)
        print("✓ OKX客户端初始化成功")
    except Exception as e:
        print(f"✗ 初始化失败: {e}")
        return
    
    # 获取K线数据
    print(f"\n正在获取 {symbol} {timeframe} 最新{count}条K线数据...")
    print("-" * 50)
    
    try:
        df = get_kline_data(client, symbol, timeframe=timeframe, count=count)
        if df.empty:
            print("✗ 获取K线数据失败")
            return
        
        # 移除最后一根未完成的K线
        df = df.iloc[:-1]
        print(f"✓ 成功获取{len(df)}条完整K线")
        print("\n最新5条K线:")
        print(df.iloc[:,:6].tail())
        
    except Exception as e:
        print(f"✗ 获取K线数据失败: {e}")
        return
    
    signals = get_comprehensive_trading_signals(df, 'DOGE-USDT-SWAP', '3m', 'ema', 14, 0.5, 2.5)
    print(signals)

    # # 计算ATR
    # print(f"\n=== ATR分析 (方法: {atr_method}, 周期: {atr_period}) ===")
    # print("-" * 50)
    
    # try:
    #     # 详细ATR计算
    #     atr_result = calculate_atr(
    #         df, 
    #         period=atr_period, 
    #         method=atr_method,
    #         return_components=True,
    #         normalize=True
    #     )
        
    #     current_price = df['close'].iloc[-1]
    #     current_atr = atr_result['atr'].iloc[-1]
        
    #     print(f"当前价格: {current_price:.6f}")
    #     print(f"当前ATR值: {current_atr:.6f}")
    #     print(f"ATR百分比: {(current_atr/current_price)*100:.2f}%")
        
    #     # ATR统计信息
    #     print(f"\nATR统计信息:")
    #     stats = atr_result['stats']
    #     print(f"ATR范围: {stats['min']:.4f} - {stats['max']:.4f}")
    # except Exception as e:
    #     print(f"✗ ATR计算失败: {e}")
    #     return
    
    # # 计算交易信号
    # print(f"\n=== 交易信号 ({position_type.upper()}仓) ===")
    # print("-" * 50)
    
    # try:
    #     signals = atr_signals(
    #         atr_result['atr'],
    #         entry_price=current_price,
    #         stop_multiplier=stop_multiplier,
    #         target_multiplier=target_multiplier,
    #         position_type=position_type
    #     )
        
    #      # 显示交易信号
    #     print(f"入场价格: {signals['entry_price']:.4f}")
    #     print(f"止损价格: {signals['stop_loss']:.4f}")  
    #     print(f"目标价格: {signals['target_price']:.4f}")
    #     print(f"风险收益比: 1:{signals['risk_reward_ratio']:.1f}")
        
    #     # 风险收益百分比
    #     risk_percent = (signals['risk_amount'] / signals['entry_price']) * 100
    #     reward_percent = (signals['reward_amount'] / signals['entry_price']) * 100
    #     print(f"风险: {risk_percent:.1f}% | 收益: {reward_percent:.1f}%")
        
        
    # except Exception as e:
    #     print(f"✗ 交易信号计算失败: {e}")
    #     return
    
    # # 根据K线周期设计平仓时间建议
    # print(f"\n=== 时间管理建议 ===")
    # print("-" * 50)
    
    # holding_suggestions = get_holding_time_suggestions(timeframe, position_type)
    # print(f"建议持仓: {holding_suggestions['最优持仓时间']}")
    # print(f"监控频率: {holding_suggestions['监控频率建议']}")
    # print(f"风控提醒: {holding_suggestions['风控建议']}")



if __name__ == "__main__":
    main()