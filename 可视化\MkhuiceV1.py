from okx_sdkV1 import get_kline_data, OKXClient
from MkKu import analyze_numerical_series,get_local_kline_data
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import os
from datetime import datetime

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import pandas as pd
import numpy as np
from datetime import datetime
import os


import pandas as pd
import numpy as np
from typing import Optional, Dict,Tu<PERSON>


def calculate_ema(df: pd.DataFrame, periods: list = [10, 20, 50], names: Optional[list] = None,
                  price_column: str = 'close', inplace: bool = True) -> Optional[pd.DataFrame]:
    """
    计算指数移动平均线 (EMA)
    
    Parameters:
    -----------
    df : pd.DataFrame
        包含价格数据的DataFrame
    periods : list, default [10, 20, 50]
        EMA周期列表
    names : list, optional
        与周期对应的列名列表
    price_column : str, default 'close'
        用于计算EMA的价格列名
    inplace : bool, default True
        是否直接修改原DataFrame
    
    Returns:
    --------
    pd.DataFrame or None
        如果inplace=False，返回修改后的DataFrame；否则返回None
    
    Raises:
    -------
    ValueError: 当数据不足或参数错误时抛出异常
    """
    # 数据验证
    if df.empty:
        raise ValueError("DataFrame不能为空")
    
    if price_column not in df.columns:
        raise ValueError(f"列 '{price_column}' 不存在于DataFrame中")
    
    # 检查数据量是否足够
    max_period = max(periods)
    if len(df) < max_period:
        raise ValueError(f"数据量不足：需要至少 {max_period} 条数据，当前只有 {len(df)} 条")
    
    # 检查价格数据是否有效
    if df[price_column].isnull().any():
        raise ValueError(f"价格列 '{price_column}' 包含空值")
    
    # 确保价格为数值类型
    try:
        df[price_column] = pd.to_numeric(df[price_column], errors='raise')
    except ValueError:
        raise ValueError(f"价格列 '{price_column}' 包含非数值数据")
    
    # 选择目标DataFrame
    target_df = df if inplace else df.copy()
    
    # 计算各周期的EMA
    if names and len(names) != len(periods):
        raise ValueError("The length of 'names' must match the length of 'periods'")

    for i, period in enumerate(periods):
        if period <= 0:
            raise ValueError(f"EMA周期必须为正数，当前为: {period}")
        
        column_name = names[i] if names else f'ema_{period}'
        target_df[column_name] = target_df[price_column].ewm(span=period, adjust=False).mean()
    
    if not inplace:
        return target_df
    
    return None


def calculate_bollinger_bands(df: pd.DataFrame, period: int = 20, std_dev: float = 2, 
                             price_column: str = 'close', inplace: bool = True) -> Optional[pd.DataFrame]:

    # 数据验证
    if df.empty:
        raise ValueError("DataFrame不能为空")
    
    if price_column not in df.columns:
        raise ValueError(f"列 '{price_column}' 不存在于DataFrame中")
    
    # 检查数据量是否足够
    if len(df) < period:
        raise ValueError(f"数据量不足：需要至少 {period} 条数据，当前只有 {len(df)} 条")
    
    # 参数验证
    if period <= 0:
        raise ValueError(f"周期必须为正数，当前为: {period}")
    
    if std_dev <= 0:
        raise ValueError(f"标准差倍数必须为正数，当前为: {std_dev}")
    
    # 检查价格数据是否有效
    if df[price_column].isnull().any():
        raise ValueError(f"价格列 '{price_column}' 包含空值")
    
    # 确保价格为数值类型
    try:
        df[price_column] = pd.to_numeric(df[price_column], errors='raise')
    except ValueError:
        raise ValueError(f"价格列 '{price_column}' 包含非数值数据")
    
    # 选择目标DataFrame
    target_df = df if inplace else df.copy()
    
    # 计算布林带
    # 中轨：简单移动平均线
    target_df['BB_middle'] = target_df[price_column].rolling(window=period).mean()
    
    # 标准差
    rolling_std = target_df[price_column].rolling(window=period).std()
    
    # 上轨和下轨
    target_df['BB_upper'] = target_df['BB_middle'] + (rolling_std * std_dev)
    target_df['BB_down'] = target_df['BB_middle'] - (rolling_std * std_dev)
    
    # 计算布林带宽度和位置
    target_df['BB_width'] = target_df['BB_upper'] - target_df['BB_down']
    target_df['BB_position'] = ((target_df[price_column] - target_df['BB_down']) /
                                         target_df['BB_width']) * 100
    
    if not inplace:
        return target_df
    
    return None


def calculate_adx(df: pd.DataFrame, period: int = 14, price_columns: dict = None,
                  inplace: bool = True) -> Optional[pd.DataFrame]:
    """
    计算平均方向指数 (ADX)、正方向指标 (+DI) 和负方向指标 (-DI)
    
    Parameters:
    -----------
    df : pd.DataFrame
        包含价格数据的DataFrame
    period : int, default 14
        ADX计算周期
    price_columns : dict, default None
        价格列名字典，格式：{'high': 'high', 'low': 'low', 'close': 'close'}
        如果为None，使用默认列名
    inplace : bool, default True
        是否直接修改原DataFrame
    
    Returns:
    --------
    pd.DataFrame or None
        如果inplace=False，返回修改后的DataFrame；否则返回None
        添加的列：'+DI', '-DI', 'ADX'
    
    Raises:
    -------
    ValueError: 当数据不足或参数错误时抛出异常
    """
    # 默认列名
    if price_columns is None:
        price_columns = {'high': 'high', 'low': 'low', 'close': 'close'}
    
    # 数据验证
    if df.empty:
        raise ValueError("DataFrame不能为空")
    
    required_columns = ['high', 'low', 'close']
    for col_type in required_columns:
        col_name = price_columns.get(col_type, col_type)
        if col_name not in df.columns:
            raise ValueError(f"列 '{col_name}' 不存在于DataFrame中")
    
    # 检查数据量是否足够（ADX需要更多数据来稳定）
    min_required = period * 2
    if len(df) < min_required:
        raise ValueError(f"数据量不足：需要至少 {min_required} 条数据，当前只有 {len(df)} 条")
    
    # 参数验证
    if period <= 0:
        raise ValueError(f"周期必须为正数，当前为: {period}")
    
    # 检查价格数据是否有效
    for col_type in required_columns:
        col_name = price_columns.get(col_type, col_type)
        if df[col_name].isnull().any():
            raise ValueError(f"价格列 '{col_name}' 包含空值")
        
        try:
            df[col_name] = pd.to_numeric(df[col_name], errors='raise')
        except ValueError:
            raise ValueError(f"价格列 '{col_name}' 包含非数值数据")
    
    # 选择目标DataFrame
    target_df = df if inplace else df.copy()
    
    # 获取列名
    high_col = price_columns['high']
    low_col = price_columns['low']
    close_col = price_columns['close']
    
    # 计算True Range (TR)
    target_df['H-L'] = target_df[high_col] - target_df[low_col]
    target_df['H-PC'] = abs(target_df[high_col] - target_df[close_col].shift(1))
    target_df['L-PC'] = abs(target_df[low_col] - target_df[close_col].shift(1))
    target_df['TR'] = target_df[['H-L', 'H-PC', 'L-PC']].max(axis=1)
    
    # 计算方向移动 (Directional Movement)
    target_df['H-PH'] = target_df[high_col] - target_df[high_col].shift(1)
    target_df['PL-L'] = target_df[low_col].shift(1) - target_df[low_col]
    
    # 计算+DM和-DM
    target_df['+DM'] = np.where(
        (target_df['H-PH'] > target_df['PL-L']) & (target_df['H-PH'] > 0),
        target_df['H-PH'], 0
    )
    target_df['-DM'] = np.where(
        (target_df['PL-L'] > target_df['H-PH']) & (target_df['PL-L'] > 0),
        target_df['PL-L'], 0
    )
    
    # 计算平滑的TR、+DM、-DM
    target_df['TR_smooth'] = target_df['TR'].ewm(span=period, adjust=False).mean()
    target_df['+DM_smooth'] = target_df['+DM'].ewm(span=period, adjust=False).mean()
    target_df['-DM_smooth'] = target_df['-DM'].ewm(span=period, adjust=False).mean()
    
    # 计算方向指标 (+DI, -DI) - 这些列将被保留
    target_df['+DI'] = 100 * (target_df['+DM_smooth'] / target_df['TR_smooth'])
    target_df['-DI'] = 100 * (target_df['-DM_smooth'] / target_df['TR_smooth'])
    
    # 计算DX
    target_df['DX'] = 100 * (abs(target_df['+DI'] - target_df['-DI']) /
                             (target_df['+DI'] + target_df['-DI']))
    
    # 计算ADX
    target_df['ADX'] = target_df['DX'].ewm(span=period, adjust=False).mean()
    
    # 清理临时列（注意：+DI和-DI不在清理列表中）
    temp_columns = ['H-L', 'H-PC', 'L-PC', 'TR', 'H-PH', 'PL-L', '+DM', '-DM',
                    'TR_smooth', '+DM_smooth', '-DM_smooth', 'DX']
    target_df.drop(columns=temp_columns, inplace=True)
    
    if not inplace:
        return target_df
    
    return None


def apply_all_indicators(df: pd.DataFrame, 
                        ema_periods: list = [10, 20, 50],
                        bb_period: int = 20,
                        bb_std_dev: float = 2,
                        adx_period: int = 14,
                        price_columns: dict = None,
                        inplace: bool = True) -> Optional[pd.DataFrame]:
    """
    一次性应用所有技术指标
    
    Parameters:
    -----------
    df : pd.DataFrame
        包含价格数据的DataFrame
    ema_periods : list, default [10, 20, 50]
        EMA周期列表
    bb_period : int, default 20
        布林带周期
    bb_std_dev : float, default 2
        布林带标准差倍数
    adx_period : int, default 14
        ADX周期
    price_columns : dict, default None
        价格列名字典
    inplace : bool, default True
        是否直接修改原DataFrame
    
    Returns:
    --------
    pd.DataFrame or None
        如果inplace=False，返回修改后的DataFrame；否则返回None
    """
    # 选择目标DataFrame
    target_df = df if inplace else df.copy()
    
    # 应用EMA
    calculate_ema(target_df, periods=ema_periods, inplace=True)
    
    # 应用布林带
    calculate_bollinger_bands(target_df, period=bb_period, std_dev=bb_std_dev, inplace=True)
    
    # 应用ADX
    calculate_adx(target_df, period=adx_period, price_columns=price_columns, inplace=True)
    
    if not inplace:
        return target_df
    
    return None




def mark_invalid_indicators(df: pd.DataFrame, ema_periods: list = [10, 20, 50], 
                           bb_period: int = 20, adx_period: int = 14, inplace: bool = True) -> Optional[pd.DataFrame]:
    """标记技术指标的无效区域
    
    参数:
    df (pd.DataFrame): 包含技术指标的DataFrame
    ema_periods (list): EMA计算周期列表
    bb_period (int): 布林带计算周期  
    adx_period (int): ADX计算周期
    inplace (bool): 是否直接修改原DataFrame
    
    返回:
    pd.DataFrame or None: 如果inplace=False，返回修改后的DataFrame
    """
    target_df = df if inplace else df.copy()
    
    # 计算各指标的有效起始位置
    max_ema_period = max(ema_periods)
    ema_valid_start = max_ema_period - 1
    bb_valid_start = bb_period - 1
    adx_valid_start = adx_period * 2 - 1
    
    # 标记EMA指标无效区域
    ema_columns = ['ema_kuai', 'ema_zhong', 'ema_man']
    for col in ema_columns:
        if col in target_df.columns:
            target_df.loc[:ema_valid_start-1, col] = np.nan
    
    # 标记布林带指标无效区域
    bb_columns = ['BB_upper', 'BB_down', 'BB_middle']
    for col in bb_columns:
        if col in target_df.columns:
            target_df.loc[:bb_valid_start-1, col] = np.nan
    
    # 标记ADX指标无效区域
    adx_columns = ['ADX', '+DI', '-DI']
    for col in adx_columns:
        if col in target_df.columns:
            target_df.loc[:adx_valid_start-1, col] = np.nan
    
    if not inplace:
        return target_df
    
    return None


def print_validity_report(df: pd.DataFrame, ema_periods: list = [10, 20, 50], 
                         bb_period: int = 20, adx_period: int = 14):
    """打印指标有效性报告
    
    参数:
    df (pd.DataFrame): 包含技术指标的DataFrame
    ema_periods (list): EMA计算周期列表
    bb_period (int): 布林带计算周期
    adx_period (int): ADX计算周期
    """
    total_bars = len(df)
    max_ema_period = max(ema_periods)
    
    ema_valid_start = max_ema_period - 1
    bb_valid_start = bb_period - 1
    adx_valid_start = adx_period * 2 - 1
    
    ema_valid_count = max(0, total_bars - ema_valid_start)
    bb_valid_count = max(0, total_bars - bb_valid_start)
    adx_valid_count = max(0, total_bars - adx_valid_start)
    
    # print("=" * 50)
    # print("技术指标有效性报告")
    # print("=" * 50)
    # print(f"总K线数量: {total_bars}")
    # print()
    
    # print(f"EMA指标 (周期: {ema_periods}):")
    # print(f"  无效K线: 前{ema_valid_start}根")
    # print(f"  有效K线: {ema_valid_count}根")
    # print(f"  有效起始位置: 第{ema_valid_start + 1}根K线")
    # print()
    
    # print(f"布林带指标 (周期: {bb_period}):")
    # print(f"  无效K线: 前{bb_valid_start}根")
    # print(f"  有效K线: {bb_valid_count}根")
    # print(f"  有效起始位置: 第{bb_valid_start + 1}根K线")
    # print()
    
    # print(f"ADX指标 (周期: {adx_period}):")
    # print(f"  无效K线: 前{adx_valid_start}根")
    # print(f"  有效K线: {adx_valid_count}根")
    # print(f"  有效起始位置: 第{adx_valid_start + 1}根K线")
    # print("=" * 50)


def filter_valid_data(df: pd.DataFrame, indicator_type: str = 'all') -> pd.DataFrame:
    """过滤出有效的指标数据
    
    参数:
    df (pd.DataFrame): 包含技术指标的DataFrame
    indicator_type (str): 指标类型 ('ema', 'bb', 'adx', 'all')
    
    返回:
    pd.DataFrame: 过滤后的DataFrame
    """
    if indicator_type == 'ema':
        ema_cols = ['ema_kuai', 'ema_zhong', 'ema_man']
        existing_cols = [col for col in ema_cols if col in df.columns]
        mask = df[existing_cols].notna().all(axis=1)
        
    elif indicator_type == 'bb':
        bb_cols = ['BB_upper', 'BB_down']
        existing_cols = [col for col in bb_cols if col in df.columns]
        mask = df[existing_cols].notna().all(axis=1)
        
    elif indicator_type == 'adx':
        adx_cols = ['ADX', '+DI', '-DI']
        existing_cols = [col for col in adx_cols if col in df.columns]
        mask = df[existing_cols].notna().all(axis=1)
    
    elif indicator_type == 'di_enhanced':
        # DI增强指标（差值和比值）
        di_enhanced_cols = ['DI_Difference', 'DI_Ratio', 'DI_Direction', 'DI_Strength']
        existing_cols = [col for col in di_enhanced_cols if col in df.columns]
        mask = df[existing_cols].notna().all(axis=1)
        
    else:  # 'all'
        all_indicator_cols = ['ema_kuai', 'ema_zhong', 'ema_man', 
                     'BB_upper', 'BB_down', 'ADX', '+DI', '-DI',
                     'DI_Difference', 'DI_Ratio', 'DI_Direction', 'DI_Strength']
        existing_cols = [col for col in all_indicator_cols if col in df.columns]
        mask = df[existing_cols].notna().all(axis=1)
    
    return df[mask].copy()



def create_technical_charts(df, path_png=r"C:\Users\<USER>\Desktop\my_python\claudeV1\量化\可视化\png", 
                           filter_invalid=True):
    """创建技术指标可视化图表
    
    参数:
    df (pd.DataFrame): 包含价格和指标数据的DataFrame
    path_png (str): 图片保存路径
    filter_invalid (bool): 是否过滤无效数据
    """
    # 确保目录存在
    try:
        os.makedirs(path_png, exist_ok=True)
        print(f"图片将保存到: {path_png}")
    except OSError:
        print(f"错误：无法创建目录 {path_png}")
        return
    
    # 设置matplotlib为非交互模式
    plt.ioff()  # 关闭交互模式
    plt.rcParams['axes.unicode_minus'] = False
    
    # 如果需要过滤无效数据
    if filter_invalid:
        plot_df = filter_valid_data(df, indicator_type='all')
        print(f"过滤后有效数据: {len(plot_df)}行 (原始数据: {len(df)}行)")
    else:
        plot_df = df
    
    # 创建四个图表
    try:
        _create_ema_chart(plot_df, path_png)
        print("✓ EMA图表已保存")
        
        _create_bb_chart(plot_df, path_png)
        print("✓ 布林带图表已保存")
        
        _create_adx_chart(plot_df, path_png)
        print("✓ ADX图表已保存")
        
        _create_di_ratio_chart(plot_df, path_png)
        print("✓ DI比值图表已保存")
        
        _create_di_difference_chart(plot_df, path_png)
        print("✓ DI差值图表已保存")
        
        print("所有图表（5张）已成功保存到本地")
        
    except Exception as e:
        print(f"生成图表时出错: {e}")


def _create_ema_chart(df, path_png):
    """创建EMA指标图表"""
    fig, ax1 = plt.subplots(figsize=(12, 8))
    
    # 左轴：价格
    ax1.set_ylabel('Price', color='black')
    ax1.plot(df.index, df['close'], label='close', color='black', linewidth=2)
    
    # 检查EMA列是否存在
    if 'ema_man' in df.columns:
        ax1.plot(df.index, df['ema_man'], label='ema_man', color='blue', alpha=0.8)
    if 'ema_zhong' in df.columns:
        ax1.plot(df.index, df['ema_zhong'], label='ema_zhong', color='orange', alpha=0.8)
    if 'ema_kuai' in df.columns:
        ax1.plot(df.index, df['ema_kuai'], label='ema_kuai', color='red', alpha=0.8)
    
    ax1.tick_params(axis='y', labelcolor='black')
    ax1.grid(True, alpha=0.3)
    ax1.legend(loc='upper left')
    
    plt.title('EMA Indicator and Price Chart', fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    # 保存图片
    save_path = os.path.join(path_png, 'EMA_Chart.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()  # 关闭图形以释放内存


def _create_bb_chart(df, path_png):
    """创建布林带指标图表"""
    fig, ax1 = plt.subplots(figsize=(12, 8))
    
    # 左轴：价格
    ax1.set_ylabel('Price', color='black')
    ax1.plot(df.index, df['close'], label='close', color='black', linewidth=2)
    
    # 检查布林带列是否存在
    if 'BB_upper' in df.columns and 'BB_down' in df.columns:
        ax1.plot(df.index, df['BB_upper'], label='BB_upper', color='red', alpha=0.7)
        ax1.plot(df.index, df['BB_down'], label='BB_down', color='green', alpha=0.7)
        
        # 填充布林带区域
        ax1.fill_between(df.index, df['BB_upper'], df['BB_down'], 
                         alpha=0.2, color='gray', label='BB_zone')
    
    ax1.tick_params(axis='y', labelcolor='black')
    ax1.grid(True, alpha=0.3)
    ax1.legend(loc='upper left')
    
    plt.title('Bollinger Bands and Price Chart', fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    # 保存图片
    save_path = os.path.join(path_png, 'BB_Chart.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()  # 关闭图形以释放内存


def _create_adx_chart(df, path_png):
    """创建ADX指标图表（仅显示ADX，不显示+DI和-DI）"""
    fig, ax1 = plt.subplots(figsize=(12, 8))
    
    # 左轴：价格
    ax1.set_ylabel('Price', color='black')
    ax1.plot(df.index, df['close'], label='close', color='black', linewidth=2)
    ax1.tick_params(axis='y', labelcolor='black')
    ax1.grid(True, alpha=0.3)
    
    # 右轴：仅显示ADX指标
    ax2 = ax1.twinx()
    ax2.set_ylabel('ADX Values', color='blue')
    
    # 只检查和绘制ADX
    if 'ADX' in df.columns:
        ax2.plot(df.index, df['ADX'], label='ADX', color='blue', linewidth=2)
    
    ax2.tick_params(axis='y', labelcolor='blue')
    
    # 添加ADX参考线
    ax2.axhline(y=25, color='gray', linestyle='--', alpha=0.5, label='Strong Trend(25)')
    ax2.axhline(y=50, color='gray', linestyle='--', alpha=0.5, label='Very Strong(50)')
    
    # 合并图例
    lines1, labels1 = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
    
    plt.title('ADX Indicator and Price Chart', fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    # 保存图片
    save_path = os.path.join(path_png, 'ADX_Chart.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()  # 关闭图形以释放内存

def _create_di_ratio_chart(df, path_png):
    """创建DI比值指标图表（改进版：使用颜色区分方向）
    
    参数:
    df (pd.DataFrame): 包含价格和指标数据的DataFrame
    path_png (str): 图片保存路径
    """
    fig, ax1 = plt.subplots(figsize=(12, 8))
    
    # 左轴：价格
    ax1.set_ylabel('Price', color='black')
    ax1.plot(df.index, df['close'], label='close', color='black', linewidth=2)
    ax1.tick_params(axis='y', labelcolor='black')
    ax1.grid(True, alpha=0.3)
    
    # 右轴：DI比值
    ax2 = ax1.twinx()
    ax2.set_ylabel('DI Ratio', color='purple')
    
    # 检查DI列是否存在并计算比值
    if '+DI' in df.columns and '-DI' in df.columns:
        plus_di = df['+DI']
        minus_di = df['-DI']
        
        # 计算比值和方向
        bullish_ratios = []  # 看涨比值（+DI > -DI时）
        bearish_ratios = []  # 看跌比值（-DI > +DI时）
        bullish_indices = []
        bearish_indices = []
        
        for i in range(len(plus_di)):
            if plus_di.iloc[i] >= minus_di.iloc[i]:
                # 看涨：+DI >= -DI，计算+DI/-DI
                if minus_di.iloc[i] < 0.1:
                    ratio = min(5.0, plus_di.iloc[i] / 0.1)
                else:
                    ratio = min(5.0, plus_di.iloc[i] / minus_di.iloc[i])
                bullish_ratios.append(ratio)
                bullish_indices.append(df.index[i])
                bearish_ratios.append(None)
                bearish_indices.append(df.index[i])
            else:
                # 看跌：-DI > +DI，计算-DI/+DI
                if plus_di.iloc[i] < 0.1:
                    ratio = min(5.0, minus_di.iloc[i] / 0.1)
                else:
                    ratio = min(5.0, minus_di.iloc[i] / plus_di.iloc[i])
                bearish_ratios.append(ratio)
                bearish_indices.append(df.index[i])
                bullish_ratios.append(None)
                bullish_indices.append(df.index[i])
        
        # 绘制不同颜色的比值线
        ax2.plot(bullish_indices, bullish_ratios, label='+DI/-DI (Bullish)', 
                color='green', linewidth=2, alpha=0.8)
        ax2.plot(bearish_indices, bearish_ratios, label='-DI/+DI (Bearish)', 
                color='red', linewidth=2, alpha=0.8)
        
        # 添加参考线
        ax2.axhline(y=1.0, color='gray', linestyle='-', alpha=0.7, label='Neutral(1.0)')
        ax2.axhline(y=1.5, color='orange', linestyle='--', alpha=0.5, label='Moderate(1.5)')
        ax2.axhline(y=2.0, color='blue', linestyle='--', alpha=0.5, label='Strong(2.0)')
        ax2.axhline(y=3.0, color='purple', linestyle='--', alpha=0.5, label='Very Strong(3.0)')
        
        # 设置Y轴范围
        ax2.set_ylim(0, 5)
    
    ax2.tick_params(axis='y', labelcolor='purple')
    
    # 合并图例
    lines1, labels1 = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
    
    plt.title('DI Ratio Chart with Direction Colors', fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    # 保存图片
    save_path = os.path.join(path_png, 'DI_Ratio_Chart.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()


def _create_di_difference_chart(df, path_png):
    """创建DI差值指标图表
    
    参数:
    df (pd.DataFrame): 包含价格和指标数据的DataFrame
    path_png (str): 图片保存路径
    """
    fig, ax1 = plt.subplots(figsize=(12, 8))
    
    # 左轴：价格
    ax1.set_ylabel('Price', color='black')
    ax1.plot(df.index, df['close'], label='close', color='black', linewidth=2)
    ax1.tick_params(axis='y', labelcolor='black')
    ax1.grid(True, alpha=0.3)
    
    # 右轴：DI差值
    ax2 = ax1.twinx()
    ax2.set_ylabel('DI Difference', color='blue')
    
    # 检查DI列是否存在并计算差值
    if '+DI' in df.columns and '-DI' in df.columns:
        # 计算+DI - (-DI)的差值
        di_difference = df['+DI'] - df['-DI']
        
        # 根据差值正负使用不同颜色
        positive_diff = di_difference.where(di_difference >= 0)
        negative_diff = di_difference.where(di_difference < 0)
        
        # 绘制差值线
        ax2.plot(df.index, positive_diff, label='+DI > -DI (Bullish)', 
                color='green', linewidth=2, alpha=0.8)
        ax2.plot(df.index, negative_diff, label='+DI < -DI (Bearish)', 
                color='red', linewidth=2, alpha=0.8)
        
        # 填充区域
        ax2.fill_between(df.index, 0, positive_diff, where=(positive_diff >= 0), 
                        color='green', alpha=0.2, interpolate=True)
        ax2.fill_between(df.index, 0, negative_diff, where=(negative_diff < 0), 
                        color='red', alpha=0.2, interpolate=True)
        
        # 添加参考线
        ax2.axhline(y=0, color='gray', linestyle='-', alpha=0.7, label='Neutral(0)')
        ax2.axhline(y=5, color='green', linestyle='--', alpha=0.5, label='Strong Bull(+5)')
        ax2.axhline(y=-5, color='red', linestyle='--', alpha=0.5, label='Strong Bear(-5)')
        ax2.axhline(y=10, color='darkgreen', linestyle='--', alpha=0.5, label='Very Strong Bull(+10)')
        ax2.axhline(y=-10, color='darkred', linestyle='--', alpha=0.5, label='Very Strong Bear(-10)')
    
    ax2.tick_params(axis='y', labelcolor='blue')
    
    # 合并图例
    lines1, labels1 = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
    
    plt.title('DI Difference Chart (+DI minus -DI)', fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    # 保存图片
    save_path = os.path.join(path_png, 'DI_Difference_Chart.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

def calculate_di_enhanced_indicators(df: pd.DataFrame, inplace: bool = True) -> Optional[pd.DataFrame]:
    # 数据验证
    if df.empty:
        raise ValueError("DataFrame不能为空")
    
    required_columns = ['+DI', '-DI']
    for col in required_columns:
        if col not in df.columns:
            raise ValueError(f"列 '{col}' 不存在于DataFrame中")
    
    # 选择目标DataFrame
    target_df = df if inplace else df.copy()
    
    # 获取DI数据
    plus_di = target_df['+DI']
    minus_di = target_df['-DI']
    
    # 计算DI差值
    target_df['DI_Difference'] = plus_di - minus_di
    
    # 计算智能DI比值和方向
    di_ratio = []
    di_direction = []
    
    for i in range(len(plus_di)):
        if plus_di.iloc[i] >= minus_di.iloc[i]:
            # 看涨：+DI >= -DI
            direction = 1
            if minus_di.iloc[i] < 0.1:
                ratio = min(5.0, plus_di.iloc[i] / 0.1)
            else:
                ratio = min(5.0, plus_di.iloc[i] / minus_di.iloc[i])
        else:
            # 看跌：-DI > +DI
            direction = -1
            if plus_di.iloc[i] < 0.1:
                ratio = min(5.0, minus_di.iloc[i] / 0.1)
            else:
                ratio = min(5.0, minus_di.iloc[i] / plus_di.iloc[i])
        
        di_ratio.append(ratio)
        di_direction.append(direction)
    
    target_df['DI_Ratio'] = di_ratio
    target_df['DI_Direction'] = di_direction
    
    # 计算趋势强度分类
    def classify_strength(ratio, difference):
        """分类趋势强度"""
        abs_diff = abs(difference)
        
        if ratio >= 3.0 or abs_diff >= 15:
            return "极强"
        elif ratio >= 2.0 or abs_diff >= 10:
            return "强"
        elif ratio >= 1.5 or abs_diff >= 5:
            return "中等"
        elif ratio >= 1.2 or abs_diff >= 2:
            return "弱"
        else:
            return "震荡"
    
    target_df['DI_Strength'] = [
        classify_strength(ratio, diff) 
        for ratio, diff in zip(target_df['DI_Ratio'], target_df['DI_Difference'])
    ]
    
    if not inplace:
        return target_df
    
    return None
def process_technical_indicators(df, 
                                 ema_periods=[10, 20, 50],
                                 ema_names=['ema_kuai', 'ema_zhong', 'ema_man'],
                                 bb_period=20,
                                 bb_std_dev=2,
                                 adx_period=14,
                                 price_columns=None,
                                 filter_type='all',
                                 show_progress=True,
                                 show_samples=True):
    """
    综合处理技术指标：计算EMA、布林带、ADX和DI增强指标，返回有效数据
    
    参数:
    df (pd.DataFrame): 包含OHLC数据的DataFrame
    ema_periods (list): EMA周期列表，默认[10, 20, 50]
    ema_names (list): EMA列名列表，默认['ema_kuai', 'ema_zhong', 'ema_man']
    bb_period (int): 布林带周期，默认20
    bb_std_dev (float): 布林带标准差倍数，默认2
    adx_period (int): ADX周期，默认14
    price_columns (dict): 价格列名映射，默认None（使用标准列名）
    filter_type (str): 过滤类型，默认'all'（可选：'ema', 'bb', 'adx', 'di_enhanced', 'all'）
    show_progress (bool): 是否显示处理进度，默认True
    show_samples (bool): 是否显示数据样本，默认True
    
    返回:
    pd.DataFrame: 包含所有技术指标的有效数据DataFrame
    
    异常:
    ValueError: 当输入数据无效时抛出异常
    """
    # 数据验证
    if df.empty:
        raise ValueError("输入DataFrame不能为空")
    
    # 必需的列检查
    required_columns = ['open', 'high', 'low', 'close']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"缺少必需的列: {missing_columns}")
    
    # 检查EMA参数
    if len(ema_periods) != len(ema_names):
        raise ValueError("ema_periods和ema_names的长度必须相等")
    
    if show_progress:
        print("原始数据:")
        print(df.iloc[:,:6].tail())
        
        print("\n开始计算技术指标")
        print("-" * 30)
    
    try:
        # 1. 计算EMA指标
        if show_progress:
            print("正在计算EMA指标...")
        calculate_ema(df, periods=ema_periods, names=ema_names)
        
        # 2. 计算布林带指标
        if show_progress:
            print("正在计算布林带指标...")
        calculate_bollinger_bands(df, period=bb_period, std_dev=bb_std_dev)
        
        # 3. 计算ADX指标
        if show_progress:
            print("正在计算ADX指标...")
        calculate_adx(df, period=adx_period, price_columns=price_columns)
        
        if show_progress:
            print("基础技术指标计算完成")
            
        # 显示基础指标结果
        if show_samples:
            basic_cols = ['close'] + ema_names + ['BB_upper', 'BB_down', 'ADX', '+DI', '-DI']
            existing_basic_cols = [col for col in basic_cols if col in df.columns]
            print("\n基础指标数据样本:")
            print(df[existing_basic_cols].tail())
        
        # 4. 计算DI增强指标
        if show_progress:
            print("\n正在计算DI增强指标...")
            
        if '+DI' in df.columns and '-DI' in df.columns:
            calculate_di_enhanced_indicators(df, inplace=True)
            if show_progress:
                print("DI增强指标计算完成")
                
            # 显示增强指标结果
            if show_samples:
                enhanced_cols = ['close', '+DI', '-DI', 'DI_Difference', 'DI_Ratio', 'DI_Direction', 'DI_Strength']
                existing_enhanced_cols = [col for col in enhanced_cols if col in df.columns]
                print("\n增强指标数据样本:")
                print(df[existing_enhanced_cols].tail())
        else:
            if show_progress:
                print("警告：缺少+DI或-DI数据，跳过DI增强指标计算")
        
        # 5. 指标有效性分析
        if show_progress:
            # print("\n指标有效性分析")
            # print("-" * 30)
            print_validity_report(df, ema_periods=ema_periods, bb_period=bb_period, adx_period=adx_period)
        
        # 6. 标记无效数据
        # if show_progress:
        #     print("\n标记无效数据")
        #     print("-" * 30)
        mark_invalid_indicators(df, ema_periods=ema_periods, bb_period=bb_period, adx_period=adx_period)
        
        # 7. 获取有效数据
        valid_df = filter_valid_data(df, indicator_type=filter_type)
        
        if show_progress:
            print("\n最终数据统计")
            # print("-" * 30)
            print(f"原始数据行数: {len(df)}")
            print(f"有效数据行数: {len(valid_df)}")
            # print(f"数据有效率: {len(valid_df)/len(df)*100:.1f}%")
        
        # 8. 显示最终有效数据样本
        if show_samples:
            final_cols = ['close', 'ADX', '+DI', '-DI', 'DI_Difference', 'DI_Ratio', 'DI_Direction', 'DI_Strength']
            existing_final_cols = [col for col in final_cols if col in valid_df.columns]
            # if show_progress:
            #     print(f"\n最终有效数据样本（包含DI增强指标）:")
            # print(valid_df[existing_final_cols].tail())
        
        if show_progress:
            print("\n✓ 技术指标处理完成")
            
        return valid_df
        
    except Exception as e:
        print(f"计算技术指标时出错: {e}")
        raise

def realtime_backtest_marker(df: pd.DataFrame, 
                            ema_periods=[10, 20, 50],
                            ema_names=['ema_kuai', 'ema_zhong', 'ema_man'],
                            bb_period=20,
                            bb_std_dev=2,
                            adx_period=14,
                            lookback_window=100,
                            start_delay=None) -> pd.DataFrame:
    """
    优化的真实模拟回测标记函数：逐K线增量计算指标并标记信号
    
    参数:
    df (pd.DataFrame): 原始OHLC数据，包含['open', 'high', 'low', 'close']列
    ema_periods (list): EMA周期列表，默认[10, 20, 50]
    ema_names (list): EMA列名列表，默认['ema_kuai', 'ema_zhong', 'ema_man']
    bb_period (int): 布林带周期，默认20
    bb_std_dev (float): 布林带标准差倍数，默认2
    adx_period (int): ADX周期，默认14
    lookback_window (int): 统计分析回望窗口，默认100
    start_delay (int): 延后起始点，默认自动计算
    
    返回:
    pd.DataFrame: 包含技术指标和信号标记的完整DataFrame
    """
    # 数据验证
    if df.empty:
        raise ValueError("输入DataFrame不能为空")
    
    required_columns = ['open', 'high', 'low', 'close']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"缺少必需的OHLC列: {missing_columns}")
    
    # 计算自动延后起始点
    if start_delay is None:
        max_ema_period = max(ema_periods)
        min_required = max(max_ema_period, bb_period, adx_period * 2)
        start_delay = min_required + lookback_window
    
    total_length = len(df)
    if total_length < start_delay + 10:
        raise ValueError(f"数据长度不足：需要至少{start_delay + 10}行，当前{total_length}行")
    
    print(f"开始优化的真实模拟回测：总长度{total_length}，延后起始{start_delay}，统计窗口{lookback_window}")
    
    # 复制原始数据
    result_df = df.copy()
    
    # 初始化所有指标和信号列
    _initialize_columns(result_df, ema_names)
    
    # 初始化计算所需的历史数据结构
    calc_data = _initialize_calculation_data(result_df, ema_periods, bb_period, adx_period)
    
    signal_count = 0
    
    # 逐K线模拟实时计算
    for current_bar in range(total_length):
        try:
            # 1. 增量计算当前K线的技术指标
            _calculate_indicators_incremental(result_df, current_bar, calc_data, 
                                            ema_periods, ema_names, bb_period, 
                                            bb_std_dev, adx_period)
            
            # 2. 只有在达到起始延迟后才开始信号检测
            if current_bar >= start_delay:
                # 3. 获取统计分析窗口
                analysis_start = max(0, current_bar - lookback_window)
                analysis_window = result_df.iloc[analysis_start:current_bar + 1]
                
                # 4. 过滤有效数据
                valid_adx = analysis_window['ADX'].dropna()
                valid_di_diff = analysis_window['DI_Difference'].dropna()
                valid_di_ratio = analysis_window['DI_Ratio'].dropna()
                
                # 5. 确保有足够的数据进行统计
                if len(valid_adx) < 20 or len(valid_di_diff) < 20 or len(valid_di_ratio) < 20:
                    continue
                
                # 6. 获取当前指标值
                current_adx = result_df.iloc[current_bar]['ADX']
                current_di_diff = result_df.iloc[current_bar]['DI_Difference']
                current_di_ratio = result_df.iloc[current_bar]['DI_Ratio']
                
                if pd.isna(current_adx) or pd.isna(current_di_diff) or pd.isna(current_di_ratio):
                    continue
                
                # 7. 计算统计指标
                adx_stats = analyze_numerical_series(valid_adx)
                di_diff_stats = analyze_numerical_series(valid_di_diff)
                di_ratio_stats = analyze_numerical_series(valid_di_ratio)
                
                # 8. 标记信号
                signals_detected = _mark_signals(result_df, current_bar, 
                                               current_adx, current_di_diff, current_di_ratio,
                                               adx_stats, di_diff_stats, di_ratio_stats)
                
                if signals_detected:
                    signal_count += 1
            
            # 每100个K线打印一次进度
            if current_bar % 100 == 0 and current_bar > 0:
                print(f"处理进度: {current_bar}/{total_length}, 信号数: {signal_count}")
                
        except Exception as e:
            print(f"处理第{current_bar}根K线时出错: {e}")
            continue
    
    print(f"优化的真实模拟回测完成：共标记{signal_count}个信号点")
    return result_df



def _mark_signals(result_df: pd.DataFrame, current_bar: int,
                 current_adx, current_di_diff, current_di_ratio,
                 adx_stats, di_diff_stats, di_ratio_stats) -> bool:
    """根据2σ区间标记当前K线的信号
    
    标记规则（仅使用2σ区间）:
    - ADX: 小于2σ下界（震荡）或大于2σ上界（强趋势）
    - DI_Difference: 小于2σ下界（空头强势）或大于2σ上界（多头强势）
    - DI_Ratio: 大于2σ上界（单边强势）
    
    参数:
    result_df (pd.DataFrame): 结果DataFrame
    current_bar (int): 当前K线索引
    current_adx, current_di_diff, current_di_ratio: 当前指标值
    adx_stats, di_diff_stats, di_ratio_stats: 统计数据
    
    返回:
    bool: 是否检测到任何信号
    """
    # 初始化信号标志
    signal_detected = False
    
    # 获取2σ区间边界
    adx_2sigma_lower = adx_stats['conf_interval_2sigma'][0]
    adx_2sigma_upper = adx_stats['conf_interval_2sigma'][1]
    
    di_diff_2sigma_lower = di_diff_stats['conf_interval_2sigma'][0]
    di_diff_2sigma_upper = di_diff_stats['conf_interval_2sigma'][1]
    
    di_ratio_2sigma_upper = di_ratio_stats['conf_interval_2sigma'][1]
    
    # ADX信号检查：小于下界（震荡）或大于上界（强趋势）
    if current_adx < adx_2sigma_lower:
        # ADX异常低 - 市场震荡
        result_df.iloc[current_bar, result_df.columns.get_loc('signal_adx_low')] = True
        signal_detected = True
        
    if current_adx > adx_2sigma_upper:
        # ADX异常高 - 强趋势
        result_df.iloc[current_bar, result_df.columns.get_loc('signal_adx_high')] = True
        signal_detected = True
    
    # DI_Difference信号检查：小于下界（空头强势）或大于上界（多头强势）
    if current_di_diff < di_diff_2sigma_lower:
        # DI差值异常低 - 空头强势
        result_df.iloc[current_bar, result_df.columns.get_loc('signal_di_diff_bearish')] = True
        signal_detected = True
        
    if current_di_diff > di_diff_2sigma_upper:
        # DI差值异常高 - 多头强势
        result_df.iloc[current_bar, result_df.columns.get_loc('signal_di_diff_bullish')] = True
        signal_detected = True
    
    # DI_Ratio信号检查：大于上界（单边强势）
    if current_di_ratio > di_ratio_2sigma_upper:
        # DI比值异常高 - 单边强势
        result_df.iloc[current_bar, result_df.columns.get_loc('signal_di_ratio_strong')] = True
        signal_detected = True
    
    # 设置综合信号：任一条件满足即标记
    if signal_detected:
        result_df.iloc[current_bar, result_df.columns.get_loc('signal_combined')] = True
    
    return signal_detected

def _initialize_columns(df: pd.DataFrame, ema_names: list):
    """初始化所有指标和信号列"""
    # 初始化技术指标列
    for name in ema_names:
        df[name] = np.nan
    df['BB_upper'] = np.nan
    df['BB_middle'] = np.nan
    df['BB_down'] = np.nan
    df['ADX'] = np.nan
    df['+DI'] = np.nan
    df['-DI'] = np.nan
    df['DI_Difference'] = np.nan
    df['DI_Ratio'] = np.nan
    
    # --- 修复：添加缺失的列初始化 ---
    df['DI_Direction'] = np.nan  # DI方向是数值 (1, -1)
    df['DI_Strength'] = ""       # DI强度是字符串，用空字符串初始化
    # --- 修复结束 ---
    
    # 初始化信号列
    df['signal_adx_low'] = False
    df['signal_adx_high'] = False
    df['signal_di_diff_bearish'] = False
    df['signal_di_diff_bullish'] = False
    df['signal_di_ratio_strong'] = False
    df['signal_combined'] = False


def _initialize_calculation_data(df: pd.DataFrame, ema_periods: list, 
                               bb_period: int, adx_period: int) -> dict:
    """初始化计算所需的历史数据结构"""
    calc_data = {
        'ema_values': {period: None for period in ema_periods},
        'tr_smooth': None,
        'plus_dm_smooth': None,
        'minus_dm_smooth': None,
        'dx_values': [],
        'adx_value': None
    }
    return calc_data


def _calculate_indicators_incremental(df: pd.DataFrame, current_bar: int, 
                                    calc_data: dict, ema_periods: list, 
                                    ema_names: list, bb_period: int, 
                                    bb_std_dev: float, adx_period: int):
    """增量计算当前K线的技术指标"""
    
    # 1. 计算EMA（使用指数平滑）
    close_price = df.iloc[current_bar]['close']
    
    for i, period in enumerate(ema_periods):
        if current_bar == 0:
            # 第一个值使用收盘价
            calc_data['ema_values'][period] = close_price
        else:
            # EMA计算公式：EMA = α * close + (1-α) * EMA_prev
            alpha = 2 / (period + 1)
            prev_ema = calc_data['ema_values'][period]
            if prev_ema is not None:
                new_ema = alpha * close_price + (1 - alpha) * prev_ema
                calc_data['ema_values'][period] = new_ema
                
                # 只有在足够的数据后才记录EMA值
                if current_bar >= period - 1:
                    df.iloc[current_bar, df.columns.get_loc(ema_names[i])] = new_ema
    
    # 2. 计算布林带（需要回看period个数据）
    if current_bar >= bb_period - 1:
        # 获取最近bb_period个收盘价
        recent_closes = df.iloc[max(0, current_bar - bb_period + 1):current_bar + 1]['close']
        
        # 计算移动平均和标准差
        bb_middle = recent_closes.mean()
        bb_std = recent_closes.std()
        
        df.iloc[current_bar, df.columns.get_loc('BB_middle')] = bb_middle
        df.iloc[current_bar, df.columns.get_loc('BB_upper')] = bb_middle + bb_std_dev * bb_std
        df.iloc[current_bar, df.columns.get_loc('BB_down')] = bb_middle - bb_std_dev * bb_std
    
    # 3. 计算ADX相关指标
    if current_bar > 0:
        _calculate_adx_incremental(df, current_bar, calc_data, adx_period)
    
    # 4. 计算DI增强指标
    if current_bar >= adx_period * 2 - 1:
        _calculate_di_enhanced_incremental(df, current_bar)


def _calculate_adx_incremental(df: pd.DataFrame, current_bar: int, 
                              calc_data: dict, period: int):
    """增量计算ADX相关指标"""
    
    # 获取当前和前一根K线的数据
    high = df.iloc[current_bar]['high']
    low = df.iloc[current_bar]['low']
    close = df.iloc[current_bar]['close']
    
    prev_high = df.iloc[current_bar - 1]['high']
    prev_low = df.iloc[current_bar - 1]['low']
    prev_close = df.iloc[current_bar - 1]['close']
    
    # 计算TR (True Range)
    hl = high - low
    hpc = abs(high - prev_close)
    lpc = abs(low - prev_close)
    tr = max(hl, hpc, lpc)
    
    # 计算方向移动
    hph = high - prev_high
    pll = prev_low - low
    
    # 计算+DM和-DM
    plus_dm = hph if (hph > pll) and (hph > 0) else 0
    minus_dm = pll if (pll > hph) and (pll > 0) else 0
    
    # 初始化或更新平滑值
    alpha = 1 / period
    
    if calc_data['tr_smooth'] is None:
        # 初始化
        calc_data['tr_smooth'] = tr
        calc_data['plus_dm_smooth'] = plus_dm
        calc_data['minus_dm_smooth'] = minus_dm
    else:
        # 指数平滑更新
        calc_data['tr_smooth'] = alpha * tr + (1 - alpha) * calc_data['tr_smooth']
        calc_data['plus_dm_smooth'] = alpha * plus_dm + (1 - alpha) * calc_data['plus_dm_smooth']
        calc_data['minus_dm_smooth'] = alpha * minus_dm + (1 - alpha) * calc_data['minus_dm_smooth']
    
    # 计算+DI和-DI
    if calc_data['tr_smooth'] > 0:
        plus_di = 100 * (calc_data['plus_dm_smooth'] / calc_data['tr_smooth'])
        minus_di = 100 * (calc_data['minus_dm_smooth'] / calc_data['tr_smooth'])
        
        df.iloc[current_bar, df.columns.get_loc('+DI')] = plus_di
        df.iloc[current_bar, df.columns.get_loc('-DI')] = minus_di
        
        # 计算DX
        di_sum = plus_di + minus_di
        if di_sum > 0:
            dx = 100 * abs(plus_di - minus_di) / di_sum
            calc_data['dx_values'].append(dx)
            
            # 计算ADX（DX的指数平滑）
            if len(calc_data['dx_values']) >= period:
                if calc_data['adx_value'] is None:
                    # 初始ADX使用简单平均
                    calc_data['adx_value'] = sum(calc_data['dx_values'][-period:]) / period
                else:
                    # 后续使用指数平滑
                    calc_data['adx_value'] = alpha * dx + (1 - alpha) * calc_data['adx_value']
                
                df.iloc[current_bar, df.columns.get_loc('ADX')] = calc_data['adx_value']


def _calculate_di_enhanced_incremental(df: pd.DataFrame, current_bar: int):
    """增量计算DI增强指标"""
    
    plus_di = df.iloc[current_bar]['+DI']
    minus_di = df.iloc[current_bar]['-DI']
    
    if pd.notna(plus_di) and pd.notna(minus_di):
        # 计算DI差值
        di_diff = plus_di - minus_di
        df.iloc[current_bar, df.columns.get_loc('DI_Difference')] = di_diff
        
        # 计算DI比值和方向
        if plus_di >= minus_di:
            direction = 1
            if minus_di < 0.1:
                ratio = min(5.0, plus_di / 0.1)
            else:
                ratio = min(5.0, plus_di / minus_di)
        else:
            direction = -1
            if plus_di < 0.1:
                ratio = min(5.0, minus_di / 0.1)
            else:
                ratio = min(5.0, minus_di / plus_di)
        
        df.iloc[current_bar, df.columns.get_loc('DI_Ratio')] = ratio
        df.iloc[current_bar, df.columns.get_loc('DI_Direction')] = direction
        
        # 计算趋势强度
        abs_diff = abs(di_diff)
        if ratio >= 3.0 or abs_diff >= 15:
            strength = "极强"
        elif ratio >= 2.0 or abs_diff >= 10:
            strength = "强"
        elif ratio >= 1.5 or abs_diff >= 5:
            strength = "中等"
        elif ratio >= 1.2 or abs_diff >= 2:
            strength = "弱"
        else:
            strength = "震荡"
        
        df.iloc[current_bar, df.columns.get_loc('DI_Strength')] = strength
        
def analyze_backtest_results(marked_df: pd.DataFrame) -> Dict:
    """
    分析回测标记结果
    
    参数:
    marked_df (pd.DataFrame): 包含信号标记的DataFrame
    
    返回:
    Dict: 分析结果统计
    """
    # 统计各类信号数量
    signal_columns = [col for col in marked_df.columns if col.startswith('signal_')]
    
    results = {}
    for col in signal_columns:
        signal_count = marked_df[col].sum()
        signal_rate = signal_count / len(marked_df) * 100
        results[col] = {
            'count': int(signal_count),
            'rate': round(signal_rate, 2)
        }
    
    # 找出信号发生的时间点
    signal_indices = marked_df[marked_df['signal_combined'] == True].index.tolist()
    
    results['signal_summary'] = {
        'total_bars': len(marked_df),
        'signal_bars': len(signal_indices),
        'signal_rate': round(len(signal_indices) / len(marked_df) * 100, 2),
        'signal_indices': signal_indices[:10]  # 只显示前10个信号点
    }
    
    return results
def print_backtest_summary(marked_df: pd.DataFrame):
    """
    打印回测结果摘要
    
    参数:
    marked_df (pd.DataFrame): 包含信号标记的DataFrame
    """
    results = analyze_backtest_results(marked_df)
    
    print("\n=== 回测信号标记结果摘要 ===")
    print(f"总K线数量: {results['signal_summary']['total_bars']}")
    print(f"信号K线数量: {results['signal_summary']['signal_bars']}")
    print(f"信号触发率: {results['signal_summary']['signal_rate']}%")
    
    print("\n--- 各指标信号统计 ---")
    for signal_type, stats in results.items():
        if signal_type != 'signal_summary':
            print(f"{signal_type}: {stats['count']}次 ({stats['rate']}%)")
    
    # 显示最近的几个信号样本
    recent_signals = marked_df[marked_df['signal_combined'] == True].tail(5)
    if not recent_signals.empty:
        print("\n--- 最近5个信号点样本 ---")
        display_cols = ['ADX', 'DI_Difference', 'DI_Ratio', 'signal_combined']
        existing_cols = [col for col in display_cols if col in recent_signals.columns]
        print(recent_signals[existing_cols])
   
   

def plot_kline_with_signals(df, save_path=None, figsize=(15, 10)):
    """绘制K线图并标记信号点
    
    参数:
    df (pd.DataFrame): 包含OHLC数据和信号标记的DataFrame
    save_path (str): 图片保存路径，默认None不保存
    figsize (tuple): 图片尺寸，默认(15, 10)
    """
    try:
        # 数据验证
        required_columns = ['open_time', 'open', 'high', 'low', 'close']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"缺少必需的列: {missing_columns}")
        
        # 检查是否有信号列
        signal_columns = [col for col in df.columns if col.startswith('signal_')]
        if not signal_columns:
            print("警告：未发现信号列，将只绘制K线图")
        
        # 设置matplotlib参数
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        plt.ioff()  # 关闭交互模式
        
        # 创建图形和子图
        fig, ax = plt.subplots(figsize=figsize)
        
        # 绘制K线图
        _draw_candlestick(ax, df)
        
        # 绘制信号标记
        if signal_columns:
            _draw_signal_markers(ax, df)
        
        # 设置图表格式
        _format_chart(ax, df)
        
        # 保存图片
        if save_path:
            _save_chart(fig, save_path)
        
        # 显示图表
        plt.tight_layout()
        plt.show()
        
        print("K线图绘制完成")
        
    except Exception as e:
        print(f"绘制K线图时出错: {e}")
        plt.close('all')


def _draw_candlestick(ax, df):
    """绘制K线图
    
    参数:
    ax: matplotlib轴对象
    df: 数据DataFrame
    """
    # 转换时间格式
    if isinstance(df['open_time'].iloc[0], str):
        times = pd.to_datetime(df['open_time'])
    else:
        times = df['open_time']
    
    # 绘制K线
    for i in range(len(df)):
        open_price = df['open'].iloc[i]
        high_price = df['high'].iloc[i]
        low_price = df['low'].iloc[i]
        close_price = df['close'].iloc[i]
        time = times.iloc[i]
        
        # 确定K线颜色
        color = 'red' if close_price >= open_price else 'green'
        
        # 绘制高低线
        ax.plot([time, time], [low_price, high_price], color='black', linewidth=0.8)
        
        # 绘制实体
        body_height = abs(close_price - open_price)
        body_bottom = min(open_price, close_price)
        
        if body_height > 0:
            # 有实体的K线
            ax.bar(time, body_height, bottom=body_bottom, 
                  color=color, alpha=0.7, width=pd.Timedelta(minutes=10))
        else:
            # 十字星
            ax.plot([time, time], [open_price, close_price], color=color, linewidth=2)


def _draw_signal_markers(ax, df):
    """绘制信号标记点
    
    参数:
    ax: matplotlib轴对象
    df: 数据DataFrame
    """
    # 转换时间格式
    if isinstance(df['open_time'].iloc[0], str):
        times = pd.to_datetime(df['open_time'])
    else:
        times = df['open_time']
    
    # 定义信号类型和对应的标记样式
    signal_configs = {
        'adx': {
            'columns': ['signal_adx_low', 'signal_adx_high'],
            'marker': '^',  # 三角形
            'colors': ['green', 'red'],  # 低=绿色（震荡），高=红色（强趋势）
            'sizes': [80, 100],
            'labels': ['ADX异常低（震荡）', 'ADX异常高（强趋势）']
        },
        'di_diff': {
            'columns': ['signal_di_diff_bearish', 'signal_di_diff_bullish'],
            'marker': 's',  # 正方形
            'colors': ['red', 'green'],  # 空头=红色，多头=绿色
            'sizes': [60, 80],
            'labels': ['DI差值异常（空头强势）', 'DI差值异常（多头强势）']
        },
        'di_ratio': {
            'columns': ['signal_di_ratio_strong'],
            'marker': 'o',  # 圆形
            'colors': ['red'],  # 单边强势=红色
            'sizes': [70],
            'labels': ['DI比值异常（单边强势）']
        }
    }
    
    # 绘制各类信号标记
    for signal_type, config in signal_configs.items():
        for i, col in enumerate(config['columns']):
            if col in df.columns:
                # 获取信号点
                signal_mask = df[col] == True
                if signal_mask.any():
                    signal_times = times[signal_mask]
                    signal_highs = df.loc[signal_mask, 'high']
                    
                    # 标记点位置稍微高于K线最高点
                    marker_y = signal_highs * 1.002
                    
                    # 绘制标记
                    ax.scatter(signal_times, marker_y,
                             marker=config['marker'],
                             color=config['colors'][i],
                             s=config['sizes'][i],
                             alpha=0.8,
                             label=config['labels'][i],
                             zorder=5)


def _format_chart(ax, df):
    """设置图表格式
    
    参数:
    ax: matplotlib轴对象
    df: 数据DataFrame
    """
    # 设置标题和标签
    ax.set_title('K线图与信号标记', fontsize=16, fontweight='bold', pad=20)
    ax.set_xlabel('时间', fontsize=12)
    ax.set_ylabel('价格', fontsize=12)
    
    # 设置时间轴格式
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
    ax.xaxis.set_major_locator(mdates.HourLocator(interval=24))
    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
    
    # 设置网格
    ax.grid(True, alpha=0.3, linestyle='--')
    
    # 设置图例
    legend = ax.legend(loc='upper left', fontsize=10, framealpha=0.9)
    if legend:
        legend.set_zorder(10)
    
    # 自动调整Y轴范围
    y_min = df['low'].min() * 0.998
    y_max = df['high'].max() * 1.005
    ax.set_ylim(y_min, y_max)


def _save_chart(fig, save_path):
    """保存图表
    
    参数:
    fig: matplotlib图形对象
    save_path: 保存路径
    """
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        
        # 保存图片
        fig.savefig(save_path, dpi=300, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        print(f"图表已保存到: {save_path}")
        
    except Exception as e:
        print(f"保存图表时出错: {e}")


def analyze_signal_distribution(df):
    """分析信号分布情况
    
    参数:
    df (pd.DataFrame): 包含信号标记的DataFrame
    """
    try:
        print("\n=== 信号分布分析 ===")
        
        # 统计各类信号数量
        signal_columns = [col for col in df.columns if col.startswith('signal_')]
        
        if not signal_columns:
            print("未发现信号列")
            return
        
        total_bars = len(df)
        print(f"总K线数量: {total_bars}")
        
        for col in signal_columns:
            if col in df.columns:
                count = df[col].sum()
                rate = count / total_bars * 100
                print(f"{col}: {count}个信号 ({rate:.2f}%)")
        
        # 分析信号密集区域
        if 'signal_combined' in df.columns:
            combined_signals = df[df['signal_combined'] == True]
            if not combined_signals.empty:
                print(f"\n综合信号总数: {len(combined_signals)}")
                print("最近5个信号时间:")
                for idx in combined_signals.tail(5).index:
                    time_str = df.iloc[idx]['open_time']
                    price = df.iloc[idx]['close']
                    print(f"  {time_str}: 价格 {price:.5f}")
        
    except Exception as e:
        print(f"分析信号分布时出错: {e}")


        
def main():
    # print("=== OKX交易所SDK测试 ===\n")
    # try:
    #     dotenv_path = r'C:\Users\<USER>\Desktop\my_python\claudeV1\量化\123.env'
    #     client = OKXClient(dotenv_path=dotenv_path)
    # except Exception as e:
    #     print(f"初始化失败: {e}")
    #     return

    # symbol = 'DOGE-USDT-SWAP'
    # timeframe = '15m'
    # counts = 1400
    # ####实时数据回测
    # print("\n1. 测试获取最新K线数据")
    # print("-" * 30)
    # df = get_kline_data(client, symbol, timeframe=timeframe, count=counts)
    # df = df.iloc[:-1]
    
    ####历史数据回测
   
    Uname='doge'
    Ktime='15m'
    strTime='2024-8-1'
    endTime='2024-8-12'
    # print(strTime)
    try:
        # df = get_local_kline_data('doge', '15m', '2024-12-30 10:00:00','2025-2-1')
        df = get_local_kline_data(Uname, Ktime, strTime, endTime)
        print(df.head())
    except Exception as e:
        print(e)

    
    if not df.empty:
        print("原始数据:")
        print(df.iloc[:,:6].tail())
        
        print("\n2. 真实模拟回测标记")
        print("-" * 30)
        try:
            # 直接传入原始df，函数内部会逐步计算指标
            backtest_result = realtime_backtest_marker(
                df,  # 原始OHLC数据
                ema_periods=[10, 20, 50],
                ema_names=['ema_kuai', 'ema_zhong', 'ema_man'],
                bb_period=20,
                adx_period=14,
                lookback_window=100,
                start_delay=60  # 给足够时间让指标稳定
            )
            # print(backtest_result)
            # 显示回测结果摘要
            print_backtest_summary(backtest_result)
            
            plot_kline_with_signals(
            backtest_result,  # 你的回测结果DataFrame
            save_path=r"C:\Users\<USER>\Desktop\my_python\claudeV1\量化\可视化\png\signals_chart.png"
        )
    
            # 分析信号分布
            analyze_signal_distribution(backtest_result)
            # 可选：保存结果
            # backtest_result.to_csv('realtime_backtest_result.csv', index=False)
            
        except Exception as e:
            print(f"回测标记时出错: {e}")
            return

    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()