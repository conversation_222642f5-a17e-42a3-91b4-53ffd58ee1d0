from okx_sdkV1 import get_kline_data, OKXClient
from qushi import TrendDirection,KLineTrendAnalyzer
from MkKu import plot_capital_curve,get_local_kline_data,analyze_trend_changes,get_trade_decision_v2,calculate_trading_profit,plot_capital_curve_v1


import pandas as pd
import numpy as np
from typing import List, Tuple,Dict, Any
import mplfinance as mpf
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from matplotlib.font_manager import FontProperties
from typing import Dict, List, Any
import warnings
from matplotlib.font_manager import FontProperties, findfont, FontManager
import warnings
warnings.filterwarnings('ignore', category=UserWarning)

def setup_chinese_fonts():
    """配置中文字体"""
    plt.rcParams['font.sans-serif'] = [
        'SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 
        'WenQuanYi Micro Hei', 'sans-serif'
    ]
    plt.rcParams['axes.unicode_minus'] = False

def plot_kline_with_trade_signals(df: pd.DataFrame,
                                ohlc_cols: List[str] = ['open', 'high', 'low', 'close'],
                                time_col: str = 'open_time',
                                trade_col: str = 'tradeS',
                                state_col: str = 'emaStas',
                                title: str = 'K线图与交易信号',
                                figsize: tuple = (16, 10)):
    """
    绘制K线图并标记交易信号
    
    Args:
        df (pd.DataFrame): 包含K线和交易信号数据的DataFrame
        ohlc_cols (List[str]): OHLC四列的列名列表，默认['open', 'high', 'low', 'close']
        time_col (str): 时间列名，默认'open_time'
        trade_col (str): 交易信号列名，默认'tradeS'
        state_col (str): 状态列名，默认'emaStas'
        title (str): 图表标题
        figsize (tuple): 图表大小
    """
    
    # 设置中文字体
    setup_chinese_fonts()
    
    # 数据验证
    if df.empty:
        raise ValueError("DataFrame不能为空")
    
    required_cols = ohlc_cols + [time_col, trade_col]
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        raise ValueError(f"缺少必需的列: {missing_cols}")
    
    # 数据准备
    df_plot = df.copy()
    
    # 重命名OHLC列
    rename_map = {
        ohlc_cols[0]: 'Open',
        ohlc_cols[1]: 'High', 
        ohlc_cols[2]: 'Low',
        ohlc_cols[3]: 'Close'
    }
    df_plot.rename(columns=rename_map, inplace=True)
    
    # 设置时间索引
    if time_col in df_plot.columns:
        df_plot[time_col] = pd.to_datetime(df_plot[time_col])
        df_plot.set_index(time_col, inplace=True)
    else:
        # 如果没有时间列，使用虚拟时间索引
        dummy_index = pd.to_datetime(pd.date_range(start='2024-01-01', periods=len(df_plot)))
        df_plot.set_index(dummy_index, inplace=True)
    
    # 处理交易信号
    long_signals = df_plot[df_plot[trade_col] == 1]
    short_signals = df_plot[df_plot[trade_col] == -1]
    
    # 创建交易信号标记
    addplot_list = []
    
    # 做多信号 (绿色向上三角)
    if not long_signals.empty:
        long_markers = pd.Series(index=df_plot.index, dtype=float)
        long_markers.loc[long_signals.index] = long_signals['Low'] * 0.998  # 稍微低于最低价
        addplot_list.append(
            mpf.make_addplot(long_markers, type='scatter', markersize=100, 
                           marker='^', color='green', alpha=0.8)
        )
    
    # 做空信号 (红色向下三角)
    if not short_signals.empty:
        short_markers = pd.Series(index=df_plot.index, dtype=float)
        short_markers.loc[short_signals.index] = short_signals['High'] * 1.002  # 稍微高于最高价
        addplot_list.append(
            mpf.make_addplot(short_markers, type='scatter', markersize=100,
                           marker='v', color='red', alpha=0.8)
        )
    
    # 绘制K线图
    mc = mpf.make_marketcolors(up='g', down='r', inherit=True)
    s = mpf.make_mpf_style(marketcolors=mc, gridstyle=':')
    
    # 抑制警告
    with warnings.catch_warnings():
        warnings.simplefilter("ignore")
        
        try:
            if addplot_list:
                fig, axes = mpf.plot(
                    df_plot[['Open', 'High', 'Low', 'Close']], 
                    type='candle',
                    style=s,
                    addplot=addplot_list,
                    figsize=figsize,
                    returnfig=True,
                    show_nontrading=False
                )
            else:
                fig, axes = mpf.plot(
                    df_plot[['Open', 'High', 'Low', 'Close']], 
                    type='candle',
                    style=s,
                    figsize=figsize,
                    returnfig=True,
                    show_nontrading=False
                )
        except Exception as e:
            raise RuntimeError(f"绘制K线图失败: {e}")
    
    # 获取主图轴
    ax = axes[0]
    
    # 设置标题和标签
    ax.set_title(title, fontsize=14, pad=20, fontweight='bold')
    ax.set_ylabel('价格', fontsize=12)
    
    # 创建图例
    legend_elements = []
    
    if not long_signals.empty:
        legend_elements.append(
            plt.Line2D([0], [0], marker='^', color='w', markerfacecolor='green',
                      markersize=10, label='做多信号 (1)', linestyle='None')
        )
    
    if not short_signals.empty:
        legend_elements.append(
            plt.Line2D([0], [0], marker='v', color='w', markerfacecolor='red',
                      markersize=10, label='做空信号 (-1)', linestyle='None')
        )
    
    if legend_elements:
        ax.legend(handles=legend_elements, loc='upper left', fontsize=10)
    
    # 统计信息
    total_signals = len(long_signals) + len(short_signals)
    long_count = len(long_signals)
    short_count = len(short_signals)
    
    # 在图上添加统计信息
    stats_text = f"交易信号统计:\n做多: {long_count}次\n做空: {short_count}次\n总计: {total_signals}次"
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=9,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 优化布局
    plt.tight_layout()
    
    # 显示图表
    try:
        mpf.show()
    except Exception as e:
        print(f"显示图表时出错: {e}")
    
    # 返回统计信息
    return {
        'total_signals': total_signals,
        'long_signals': long_count,
        'short_signals': short_count,
        'long_signal_times': long_signals.index.tolist() if not long_signals.empty else [],
        'short_signal_times': short_signals.index.tolist() if not short_signals.empty else []
    }



def analyze_ema_state(
    ema_values: List[float] | Tuple[float, float, float],
    tolerance_percent: float = 0.005
) -> str:
    """
    根据快(A)、中(B)、慢(C)三条EMA线的相对值，分析并返回市场所处的9种状态之一。

    Args:
        ema_values (List[float] | Tuple[float, float, float]):
            一个包含三个EMA值的列表或元组，顺序为 [快线A, 中线B, 慢线C]。
            例如：[105.5, 102.1, 98.7]

        tolerance_percent (float, optional):
            用于判断两条EMA线是否“约等于(≈)”或“纠缠”的百分比阈值。
            默认为 0.005，即 0.5%。
            计算公式为: abs(v1 - v2) / v2 <= tolerance_percent。
            例如，如果中线B为100，阈值为0.005，那么快线A在[99.5, 100.5]范围内
            都将被视为与中线B约等于。

    Returns:
        str: 描述当前市场趋势状态的字符串。
    """
    # 检查输入是否合法
    if not isinstance(ema_values, (list, tuple)) or len(ema_values) != 3:
        raise ValueError("输入参数 'ema_values' 必须是一个包含3个数字的列表或元组。")
    
    # 检查值是否为NaN
    if any(np.isnan(v) for v in ema_values):
        return "数据不足/无法计算"

    a, b, c = ema_values

    # --- 第一步：判定 A 和 B 的关系 ---
    # 防止 B 为 0 导致除法错误
    if b != 0 and abs(a - b) / abs(b) <= tolerance_percent:
        ab_state = "approx"  # A ≈ B
    elif a > b:
        ab_state = "greater"  # A > B
    else:
        ab_state = "less"  # A < B

    # --- 第二步：判定 B 和 C 的关系 ---
    # 防止 C 为 0 导致除法错误
    if c != 0 and abs(b - c) / abs(c) <= tolerance_percent:
        bc_state = "approx"  # B ≈ C
    elif b > c:
        bc_state = "greater"  # B > C
    else:
        bc_state = "less"  # B < C
        
    # --- 第三步：根据两个关系组合，从9宫格中匹配结果 ---
    
    # 规则 1-3: 短期动能强于中期 (A > B)
    if ab_state == "greater":
        if bc_state == "greater":   # A > B and B > C
            return "1. 强势多头 (A > B > C)"
        elif bc_state == "less":    # A > B and B < C
            return "2. 下跌中反弹"
        else:  # bc_state == "approx"  # A > B and B ≈ C
            return "3. 盘整后突破"

    # 规则 4-6: 短期动能弱于中期 (A < B)
    elif ab_state == "less":
        if bc_state == "greater":   # A < B and B > C
            return "4. 上涨中回调"
        elif bc_state == "less":    # A < B and B < C
            return "5. 强势空头 (C > B > A)"
        else:  # bc_state == "approx"  # A < B and B ≈ C
            return "6. 盘整后破位"
            
    # 规则 7-9: 短中期动能纠缠 (A ≈ B)
    else:  # ab_state == "approx"
        if bc_state == "greater":   # A ≈ B and B > C
            return "7. 上涨趋势减弱"
        elif bc_state == "less":    # A ≈ B and B < C
            return "8. 下跌趋势减弱"
        else:  # bc_state == "approx"  # A ≈ B and B ≈ C
            return "9. 极限盘整/无趋势"
        
def calculate_ema(data, period):
    """
    计算指数移动平均值 (EMA)
    
    参数:
        data: pandas Series 或 list，输入数据列
        period: int，EMA周期
    
    返回:
        pandas Series，EMA值序列，长度与输入数据相同
    """
    # 转换为pandas Series以便处理
    if not isinstance(data, pd.Series):
        data = pd.Series(data)
    
    data_length = len(data)
    
    # 长度不足，返回相同长度的0
    if data_length < period:
        return pd.Series([0.0] * data_length, index=data.index)
    
    # 计算平滑因子
    alpha = 2 / (period + 1)
    
    # 初始化结果序列
    ema_values = pd.Series([0.0] * data_length, index=data.index)
    
    # 使用前period个有效值的平均值作为初始EMA
    valid_data = data.dropna()
    if len(valid_data) < period:
        return pd.Series([0.0] * data_length, index=data.index)
    
    # 计算初始EMA（使用前period个值的简单平均）
    start_idx = period - 1
    ema_values.iloc[start_idx] = valid_data.iloc[:period].mean()
    
    # 递推计算后续EMA值
    for i in range(start_idx + 1, data_length):
        if pd.notna(data.iloc[i]):
            ema_values.iloc[i] = alpha * data.iloc[i] + (1 - alpha) * ema_values.iloc[i-1]
        else:
            ema_values.iloc[i] = ema_values.iloc[i-1]
    
    return ema_values


def calculate_ema_optimized(df, column, period, N=3):
 
    df_length = len(df)
    threshold = N * period
    
    # 情况1：df长度小于period，数据不足
    if df_length < period:
        # print(f"数据长度({df_length})小于EMA周期({period})，返回全0序列")
        return pd.Series([0.0] * df_length, index=df.index)
    
    # 情况2：df长度在[period, N*period]之间，使用全部数据
    elif df_length <= threshold:
        # print(f"数据长度({df_length})在合理范围内，使用全部数据计算EMA{period}")
        slice_df = df
    
    # 情况3：df长度大于N*period，进行切片优化
    else:
        # print(f"数据长度({df_length})大于阈值({threshold})，切片使用最后{threshold}个数据点")
        slice_df = df.iloc[-threshold:]
    
    # 计算EMA
    ema_result = calculate_ema(slice_df[column], period)
    
    # 如果进行了切片，需要将结果扩展到原始DataFrame的长度
    if len(slice_df) < df_length:
        # 创建与原始DataFrame同长度的结果序列
        full_result = pd.Series([0.0] * df_length, index=df.index)
        # 将切片计算的结果放到对应位置
        full_result.iloc[-len(slice_df):] = ema_result.values
        return full_result
    
    return ema_result


def check_and_setup_chinese_fonts():
    """
    检查并设置中文字体，避免字符缺失警告
    """
    # 抑制字体相关警告
    warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')
    
    # 检查可用的中文字体
    fm = FontManager()
    chinese_fonts = []
    
    # 常见中文字体列表
    font_candidates = [
        'SimHei',           # 黑体
        'Microsoft YaHei',  # 微软雅黑
        'SimSun',          # 宋体
        'KaiTi',           # 楷体
        'Arial Unicode MS', # Mac
        'PingFang SC',     # Mac
        'Hiragino Sans GB', # Mac
        'WenQuanYi Micro Hei', # Linux
        'Noto Sans CJK SC', # Google
        'Source Han Sans SC', # Adobe
    ]
    
    # 查找可用的中文字体
    for font_name in font_candidates:
        try:
            font_path = findfont(FontProperties(family=font_name))
            if font_path and font_name.lower() not in font_path.lower().replace('DejaVu', '').lower():
                chinese_fonts.append(font_name)
        except:
            continue
    
    # 如果没有找到中文字体，尝试手动指定路径
    if not chinese_fonts:
        manual_font_paths = [
            'C:/Windows/Fonts/simhei.ttf',     # Windows 黑体
            'C:/Windows/Fonts/msyh.ttc',       # Windows 微软雅黑
            'C:/Windows/Fonts/simsun.ttc',     # Windows 宋体
            '/System/Library/Fonts/Arial Unicode.ttc',  # Mac
            '/System/Library/Fonts/PingFang.ttc',       # Mac
            '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc', # Linux
        ]
        
        for font_path in manual_font_paths:
            try:
                import os
                if os.path.exists(font_path):
                    # 从路径提取字体名称
                    font_name = os.path.basename(font_path).split('.')[0]
                    chinese_fonts.append(font_name)
                    break
            except:
                continue
    
    # 设置字体优先级列表
    if chinese_fonts:
        font_list = chinese_fonts + ['sans-serif']
        print(f"找到中文字体: {chinese_fonts}")
    else:
        # 如果实在找不到，使用fallback方案
        font_list = ['sans-serif']
        print("警告: 未找到中文字体，将使用系统默认字体")
    
    # 全局设置字体
    plt.rcParams['font.sans-serif'] = font_list
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建字体属性对象
    try:
        if chinese_fonts:
            chinese_font = FontProperties(family=chinese_fonts[0], size=10)
        else:
            chinese_font = FontProperties(size=10)
    except:
        chinese_font = FontProperties(size=10)
    
    return chinese_font

def plot_states_with_fixed_fonts(df: pd.DataFrame,
                                ohlc_cols: list = ['open', 'high', 'low', 'close'],
                                state_col: str = 'emaStas',
                                title: str = '市场状态分析'):
    """
    修复了所有字体和警告问题的绘图函数
    """
    
    # 1. 设置中文字体
    chinese_font = check_and_setup_chinese_fonts()
    
    # 2. 数据验证
    if df.empty:
        raise ValueError("DataFrame不能为空")
    
    missing_cols = [col for col in ohlc_cols if col not in df.columns]
    if missing_cols:
        raise ValueError(f"缺少必需的OHLC列: {missing_cols}")
    
    if state_col not in df.columns:
        raise ValueError(f"状态列 '{state_col}' 不存在于DataFrame中")
    
    # 3. 数据准备
    df_plot = df.copy()
    rename_map = {
        ohlc_cols[0]: 'Open', ohlc_cols[1]: 'High',
        ohlc_cols[2]: 'Low', ohlc_cols[3]: 'Close'
    }
    df_plot.rename(columns=rename_map, inplace=True)
    dummy_index = pd.to_datetime(pd.date_range(start='2000-01-01', periods=len(df_plot)))
    df_plot.set_index(dummy_index, inplace=True)

    # 4. 状态映射
    state_map = {
        0: {'desc': '0. 无效/初始状态', 'color': (0.0, 0.0, 0.0, 0.0)},
        1: {'desc': '1. 强势多头', 'color': (0.0, 1.0, 0.0, 0.3)},
        2: {'desc': '2. 下跌中反弹', 'color': (1.0, 0.647, 0.0, 0.3)},
        3: {'desc': '3. 盘整后突破', 'color': (0.678, 0.847, 0.902, 0.4)},
        4: {'desc': '4. 上涨中回调', 'color': (0.0, 0.392, 0.0, 0.3)},
        5: {'desc': '5. 强势空头', 'color': (1.0, 0.0, 0.0, 0.3)},
        6: {'desc': '6. 盘整后破位', 'color': (1.0, 0.753, 0.796, 0.4)},
        7: {'desc': '7. 上涨趋势减弱', 'color': (1.0, 1.0, 0.0, 0.3)},
        8: {'desc': '8. 下跌趋势减弱', 'color': (0.502, 0.0, 0.502, 0.3)},
        9: {'desc': '9. 极限盘整/无趋势', 'color': (0.502, 0.502, 0.502, 0.3)}
    }

    # 5. 绘制K线图（修复警告）
    mc = mpf.make_marketcolors(up='g', down='r', inherit=True)
    s = mpf.make_mpf_style(marketcolors=mc, gridstyle=':')
    
    # 抑制mplfinance的弃用警告
    with warnings.catch_warnings():
        warnings.simplefilter("ignore", DeprecationWarning)
        warnings.simplefilter("ignore", UserWarning)
        
        try:
            fig, axes = mpf.plot(
                df_plot, 
                type='candle', 
                style=s,
                ylabel='Price', 
                figsize=(16, 10),
                returnfig=True, 
                xrotation=0, 
                show_nontrading=False  # 新参数，替代no_xgaps
            )
        except Exception as e:
            raise RuntimeError(f"绘制K线图失败: {e}")

    # 6. 状态数据处理
    def extract_state_number(state_str):
        if pd.isna(state_str):
            return None
        
        state_str = str(state_str).strip()
        if state_str.isdigit():
            return int(state_str)
        
        import re
        match = re.match(r'^(\d+)', state_str)
        if match:
            return int(match.group(1))
        return None
    
    processed_states = []
    for i, raw_state in enumerate(df_plot[state_col]):
        try:
            state = extract_state_number(raw_state)
            if state is not None and 0 <= state <= 9:
                processed_states.append(state)
            else:
                processed_states.append(None)
        except:
            processed_states.append(None)

    # 7. 绘制背景着色
    ax = axes[0]
    current_state = -1
    start_idx = 0
    
    for i, state in enumerate(processed_states):
        if state is None:
            continue
        
        if state != current_state:
            if current_state not in [-1, 0, None]:
                try:
                    ax.axvspan(start_idx - 0.5, i - 0.5,
                               color=state_map[current_state]['color'],
                               zorder=0)
                except:
                    pass
            current_state = state
            start_idx = i

    if current_state not in [-1, 0, None]:
        try:
            ax.axvspan(start_idx - 0.5, len(df_plot) - 0.5,
                       color=state_map[current_state]['color'],
                       zorder=0)
        except:
            pass

    # 8. 创建图例
    used_states = set(s for s in processed_states if s is not None and s != 0)
    if used_states:
        legend_patches = [
            mpatches.Patch(color=state_map[k]['color'], label=state_map[k]['desc'])
            for k in sorted(used_states) if k in state_map
        ]
        
        if legend_patches:
            fig.legend(handles=legend_patches, 
                      loc='upper left', 
                      fontsize=9, 
                      title='市场状态',
                      prop=chinese_font)

    # 9. 设置标题和标签
    ax.set_title(title, fontproperties=chinese_font, fontsize=14, pad=20, fontweight='bold')
    ax.set_xlabel('K线序列号', fontproperties=chinese_font, fontsize=11)
    ax.set_ylabel('价格', fontproperties=chinese_font, fontsize=11)
    
    # 10. 格式化坐标轴
    ax.xaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{int(x)}'))
    
    # 11. 设置刻度标签字体
    for label in ax.get_xticklabels() + ax.get_yticklabels():
        label.set_fontproperties(chinese_font)

    # 12. 优化布局并显示
    plt.tight_layout()
    
    # 抑制显示时的警告
    with warnings.catch_warnings():
        warnings.simplefilter("ignore")
        mpf.show()
        
if __name__ == "__main__":
    
    
    # 示例数据（实际使用时替换为真实数据）
#     print("=== K线趋势分析系统 ===\n")
#     print("=== OKX交易所SDK测试 ===\n")
#     try:
#         # 指定.env文件的路径
#         dotenv_path = r'C:\Users\<USER>\Desktop\my_python\claudeV1\量化\123.env'
#         client = OKXClient(dotenv_path=dotenv_path)
#     except Exception as e:
#         print(f"初始化失败: {e}")
       
# ##在线数据回测
#     symbol = 'DOGE-USDT-SWAP'
#     timeframe = '5m'
#     kcount=600
#     df= get_kline_data(client, symbol, timeframe=timeframe, count=kcount)
    
    # df=df.tail(60)
    ###历史数据回测
    ####历史数据回测
   
    Uname='doge'
    Ktime='3m'
    strTime='2025-4-13'
    endTime='2025-4-23'
    # print(strTime)
    try:
        # df = get_local_kline_data('doge', '15m', '2024-12-30 10:00:00','2025-2-1')
        df = get_local_kline_data(Uname, Ktime, strTime, endTime)
        print(df.head())
    except Exception as e:
        print(e)
        
    print(f"分析数据量: {len(df)} 条K线")
    print(f"数据时间范围: {df['open_time'].iloc[0]} 到 {df['open_time'].iloc[-1]}")
    
    count=50
    max_start_index = len(df) - count
    emaStas= []
    k_sta=[]
    periods=[3, 6, 20]
    # periods=[10, 20, 50]
    price_columns=['low', 'close', 'high']
    # price_columns=['high', 'close', 'low']
    # price_columns=['high', 'close', 'open']
    # price_columns=['close', 'close', 'close']
    # 循环创建切片
    # print(df)
    ##交易参数
    lastSigle= 0
    nowSigle= 0
    current_position='flat'    ##当前持仓状态 ('flat', 'long', 'short')。
    trade_direction='both'  ###交易方向控制 ('both', 'long_only', 'short_only')。
    tradeS=[]
    tradePrice=[]
    for start_idx in range(max_start_index + 1):
        # 计算结束索引
        # print(start_idx)
       
        end_idx = start_idx + count
        # 创建切片,模拟实时数据
        slice_df = df.iloc[start_idx:end_idx]
        # print(len(slice_df))
        LivemaS=[ ]
        for i in [0,1,2]:
            results =calculate_ema_optimized(slice_df, price_columns[i], periods[i], N=3)
            LivemaS.append(results.iloc[-1])
        # print(results.iloc[-1],slice_df.iloc[-1])
        # print(emaS)
        state = analyze_ema_state(LivemaS,tolerance_percent=0.004)
        # print(f"输入 {LivemaS}: {state}")
        # print(state[:1])
        lastSigle=nowSigle
        nowSigle=int(state[:1])
        emaStas.append(state)
        trade_signal, reason=get_trade_decision_v2(lastSigle,nowSigle,current_position=current_position,trade_direction=trade_direction)
        # and '黄金' in reason
        if trade_signal=='BUY':
            tradeS.append(1)
            # tradeS.append(0)
            tradePrice.append(slice_df.iloc[-1]['close'])
        elif trade_signal=='SHORT':
            # tradeS.append(-1)
            tradeS.append(0)
            tradePrice.append(slice_df.iloc[-1]['close'])
        else:
            tradeS.append(0)
            tradePrice.append(0)
        # print(tradeSigle[1])
        # if trade_signal != 'STAND_ASIDE':
        #     print(f"第{start_idx + count}根：{trade_signal} {reason}  {slice_df.iloc[-1]['close']} ")
            
    emaStas=[0] * (count-1)+emaStas
    tradeS=[0] * (count-1)+tradeS
    tradePrice=[0] * (count-1)+tradePrice
    print(len(emaStas),len(df))
    # print(emaStas)
    df['emaStas']=emaStas
    df['tradeS']=tradeS
    df['tradePrice']=tradePrice
    # result = analyze_trend_changes(df['emaStas'], verbose=True, min_duration=1)
    
    ##画图交易
    plot_result = plot_kline_with_trade_signals(df, title='交易信号分析图')
    
    
    ##画图
        # 检查是否存在 'volume' 列，如果不存在则添加
    # if 'volume' not in df.columns and 'Volume' not in df.columns:
    #     df['Volume'] = 0 # 创建一个虚拟的Volume列
    # df['open_time'] = pd.to_datetime(df['open_time'])
    # df.set_index('open_time', inplace=True)
    # plot_states_with_fixed_fonts(df, title="我的市场状态分析图")
    # print(df.tail(20))
    
        
    ##计算交易情况
    take_profit_pct=0.02
    stop_loss_pct=0.05
    stats = calculate_trading_profit(df, take_profit_pct, stop_loss_pct)

    print("=== 交易统计报告 ===")
    if take_profit_pct is not None or stop_loss_pct is not None:
        print(f"止盈设置: {take_profit_pct*100 if take_profit_pct else 'None'}%")
        print(f"止损设置: {stop_loss_pct*100 if stop_loss_pct else 'None'}%")
    print(f"总交易次数: {stats['total_trades']}")
    print(f"胜率: {stats['win_rate']:.2f}%")
    print(f"盈利交易: {stats['winning_trades']}")
    print(f"亏损交易: {stats['losing_trades']}")
    print(f"总盈亏: {stats['total_profit_pct']:.6f}")      # ✅ 修正
    print(f"平均盈亏: {stats['avg_profit_pct']:.6f}")      # ✅ 修正
    print(f"最大盈利: {stats['max_profit_pct']:.6f}")      # ✅ 修正
    print(f"最大亏损: {stats['max_loss_pct']:.6f}")        # ✅ 修正
    print(f"止盈交易: {stats['take_profit_trades']}")
    print(f"止损交易: {stats['stop_loss_trades']}")
    print(f"未完成交易: {stats['unfinished_trades']}")

    # 显示退出原因统计
    if stats['exit_reasons']:
        print("\n=== 退出原因统计 ===")
        for reason, count in stats['exit_reasons'].items():
            reason_map = {
                'take_profit': '止盈',
                'stop_loss': '止损',
                'end_of_data': '数据结束未平仓'
            }
            print(f"{reason_map.get(reason, reason)}: {count}次")

    # 固定金额投资
    # result1 = plot_capital_curve(stats, initial_capital=200, investment_amount=50, leverage=10)

    # # 百分比投资（复利效应）
    # result2 = plot_capital_curve(stats, initial_capital=200, investment_pct=0.30, leverage=10)
    ##
    # result_v1 = plot_capital_curve_v1(stats, initial_capital=200, investment_pct=0.30, leverage=10)
    # 2. 保守模式：增长50%时就截留30%
    result_v1 = plot_capital_curve_v1(stats, initial_capital=200, investment_pct=0.30, leverage=10,
                                    reserve_trigger_multiple=1.5, reserve_ratio=0.5)