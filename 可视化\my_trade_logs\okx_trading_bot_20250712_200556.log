2025-07-12 20:05:56,487 - INFO - --- OKX 交易机器人启动 (V-Final, 完整版) ---
2025-07-12 20:05:56,488 - INFO - API_KEY: 91dfa2dc...
2025-07-12 20:05:56,488 - INFO - SECRET_KEY: 017E48AF...
2025-07-12 20:05:56,489 - INFO - PASSPHRASE: Hl12...
2025-07-12 20:06:00,666 - INFO - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/1.1 200 OK"
2025-07-12 20:06:00,667 - INFO - API 连接验证成功
2025-07-12 20:06:00,667 - INFO - 核心参数: 本金=10.0 USDT, 杠杆=5x, 计算名义价值=50.0 USDT
2025-07-12 20:06:00,941 - INFO - HTTP Request: GET https://www.okx.com/api/v5/public/instruments?instType=SWAP&instId=DOGE-USDT-SWAP "HTTP/2 200 OK"
2025-07-12 20:06:00,943 - INFO - 下单量计算(单位:张): 
  - 名义价值(USDT) = 50.00
  - 计算用价格 = 1.3
  - 理论张数 = 0.0385
  - 最终下单张数 = 0.03
2025-07-12 20:06:00,943 - INFO - 设置 DOGE-USDT-SWAP short 方向杠杆为 5x
2025-07-12 20:06:01,717 - INFO - HTTP Request: POST https://www.okx.com/api/v5/account/set-leverage "HTTP/1.1 200 OK"
2025-07-12 20:06:02,718 - INFO - 准备下达计划委托：当价格到达 1.3 时，以 1.3 的价格开 sell 仓...
2025-07-12 20:06:03,558 - INFO - HTTP Request: POST https://www.okx.com/api/v5/trade/order-algo "HTTP/1.1 200 OK"
2025-07-12 20:06:03,560 - INFO - 计划委托已成功提交，策略ID: 2678300140292808704
2025-07-12 20:06:03,561 - INFO - 开始监控仓位...若 1.2 分钟内未开仓，则撤销计划委托。
2025-07-12 20:06:23,562 - INFO - 正在检查 short 方向的仓位...
2025-07-12 20:06:23,867 - INFO - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instType=SWAP&instId=DOGE-USDT-SWAP "HTTP/2 200 OK"
2025-07-12 20:06:43,868 - INFO - 正在检查 short 方向的仓位...
2025-07-12 20:06:44,645 - INFO - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instType=SWAP&instId=DOGE-USDT-SWAP "HTTP/1.1 200 OK"
2025-07-12 20:07:04,646 - INFO - 正在检查 short 方向的仓位...
2025-07-12 20:07:05,431 - INFO - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instType=SWAP&instId=DOGE-USDT-SWAP "HTTP/1.1 200 OK"
2025-07-12 20:07:25,434 - INFO - 正在检查 short 方向的仓位...
2025-07-12 20:07:25,742 - INFO - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instType=SWAP&instId=DOGE-USDT-SWAP "HTTP/2 200 OK"
2025-07-12 20:07:25,745 - WARNING - 计划委托 2678300140292808704 超时未触发或成交，正在撤销...
2025-07-12 20:07:26,542 - INFO - HTTP Request: POST https://www.okx.com/api/v5/trade/cancel-algos "HTTP/1.1 200 OK"
2025-07-12 20:07:26,543 - INFO - --- 交易流程正常结束 ---
