from okx_sdkV1 import get_kline_data, OKXClient
from qushi import TrendDirection,KLineTrendAnalyzer
from MkKu import analyze_numerical_series,get_local_kline_data,analyze_trend_changes,get_trade_decision_v2
from Mklog import TradeLogger
from MkjiaoyiV1 import execute_full_trade_lifecycle, quick_buy_silent

import pandas as pd
import numpy as np
from typing import List, Tuple,Dict, Any
import mplfinance as mpf
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from matplotlib.font_manager import FontProperties
from typing import Dict, List, Any
import warnings
from matplotlib.font_manager import FontProperties, findfont, FontManager
from MkhuiceMea import calculate_ema_optimized,analyze_ema_state
import time
import datetime

def parse_timeframe(timeframe: str) -> int:
    """解析时间框架，返回秒数"""
    timeframe = timeframe.lower().strip()
    
    unit_map = {'s': 1, 'm': 60, 'h': 3600, 'd': 86400}
    
    if timeframe[-1] in unit_map:
        number = int(timeframe[:-1])
        unit = timeframe[-1]
        return number * unit_map[unit]
    else:
        raise ValueError(f"不支持的时间格式: {timeframe}")

def time_loop(symbol,timeframe='15m',kcount=50):
    """按时间框架循环"""
    interval = parse_timeframe(timeframe)
    print("=== K线趋势分析系统 ===\n")
    print("=== OKX交易所SDK测试 ===\n")
    try:
        # 指定.env文件的路径
        dotenv_path = r'C:\Users\<USER>\Desktop\my_python\claudeV1\量化\123.env'
        client = OKXClient(dotenv_path=dotenv_path)
    except Exception as e:
        print(f"初始化失败: {e}")
        
    logger = TradeLogger(log_dir="./my_trade_logs")  ##初始化日志
    
    
    
    k_sta=[]
    periods=[10, 20, 50]
    # price_columns=['high', 'close', 'low']
    price_columns=['close', 'close', 'close']
    ##交易参数
    lastSigle= 0
    nowSigle= 0
    current_position='flat'    ##当前持仓状态 ('flat', 'long', 'short')。
    trade_direction='both'  ###交易方向控制 ('both', 'long_only', 'short_only')。
    tradeS=[]
    
    ###实盘交易
    # trader = SilentOKXTrader(is_sandbox=False, silent_mode=True)
  # 实盘模式
    
    while True:
        try:
            print(f"执行时间: {datetime.datetime.now()}")
            ##获取最新k线
            df= get_kline_data(client, symbol, timeframe=timeframe, count=kcount)
            print(df.tail(4))

            # 交易逻辑
            emaStas= []##每次清空
            buy_price=0
            for i in [0,1,2]:
                    results =calculate_ema_optimized(df, price_columns[i], periods[i], N=3)
                    emaStas.append(results.iloc[-1])
            print(emaStas,'ema值')
            state = analyze_ema_state(emaStas,tolerance_percent=0.004)
            k_sta.append(state)
            lastSigle=nowSigle
            nowSigle=int(state[:1])
            print(state,'过去状态和当前状态')
            trade_signal, reason=get_trade_decision_v2(lastSigle,nowSigle,current_position=current_position,trade_direction=trade_direction)
            if trade_signal=='BUY':
                tradeS.append(1)
                buy_price=df.iloc[-1]['close']
                
                entry_order_id = execute_full_trade_lifecycle(
                    symbol="DOGE-USDT-SWAP", side="buy", trigger_price=buy_price, leverage="5", principal_usdt=10.0,
                    tp_ratio=0.01, timeout_minutes=5.0, callback=lambda status, message, data: print(f"交易结果: {status}")
                )
                if entry_order_id: print(f"后台交易任务多单已启动。ID: {entry_order_id}")
            elif trade_signal=='SHORT':
                tradeS.append(-1)
                buy_price=df.iloc[-1]['close']
                entry_order_id = execute_full_trade_lifecycle(
                    symbol="DOGE-USDT-SWAP", side="sell", trigger_price=buy_price, leverage="5", principal_usdt=10.0,
                    tp_ratio=0.01, timeout_minutes=5.0, callback=lambda status, message, data: print(f"交易结果: {status}")
                )
                if entry_order_id: print(f"后台交易任务空单已启动。ID: {entry_order_id}")
            else:
                tradeS.append(0)
                buy_price=None
            print(f"{df.iloc[-1]['open_time']}：{trade_signal} {reason}  {df.iloc[-1]['close']} ")
            if trade_signal != 'STAND_ASIDE':
                    print(f"第{df.iloc[-1]['open_time']}根：{trade_signal} {reason}  {df.iloc[-1]['close']} ")
            time.sleep(interval)
            
            ###日志记录
            # print(tradeS[-1])
            tradeData={
            "symbol": symbol,
            "timeframe": timeframe,
            "open_time": df.iloc[-1]['open_time'],
            "open_price": df.iloc[-1]['open'],
            "high_price": df.iloc[-1]['high'],
            "low_price": df.iloc[-1]['low'],
            "close_price": df.iloc[-1]['close'],
            "current_state":state,
            "trade_signal": tradeS[-1],
            "buy_price": buy_price,
            "remark": f"{trade_signal}**{reason}"
        }
            logger.log_trade(**tradeData)
            
        except KeyboardInterrupt:
            print("循环停止")
            break

if __name__ == "__main__":
        # 示例数据（实际使用时替换为真实数据）
    
       
##在线数据回测
    symbol = 'DOGE-USDT-SWAP'
    timeframe = '3m'
    kcount=60
    
    time_loop(symbol,timeframe,kcount) 
    
    # print(df)
    
    
    