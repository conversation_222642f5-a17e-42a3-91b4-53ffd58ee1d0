import requests
import json
import pandas as pd
import time
import hmac
import hashlib
import base64
from datetime import datetime
import os
from dotenv import load_dotenv

class OKXClient:
    def __init__(self, api_key=None, secret_key=None, passphrase=None, sandbox=False):
        """
        初始化OKX客户端，优先从环境变量读取API信息
        Args:
            api_key: API密钥
            secret_key: 秘密密钥
            passphrase: API密码短语
            sandbox: 是否使用沙盒环境
        """
        # 加载与脚本同目录的 123.env 文件
        dotenv_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '123.env')
        if os.path.exists(dotenv_path):
            load_dotenv(dotenv_path=dotenv_path)

        self.api_key = api_key or os.getenv("OKX_API_KEY")
        self.secret_key = secret_key or os.getenv("OKX_SECRET_KEY")
        self.passphrase = passphrase or os.getenv("OKX_PASSPHRASE")
        if not all([self.api_key, self.secret_key, self.passphrase]):
            raise ValueError("API KEY/SECRET/PASSPHRASE 不能为空，请设置环境变量OKX_API_KEY, OKX_SECRET_KEY, OKX_PASSPHRASE或在初始化时传入。")
        if sandbox:
            self.base_url = 'https://www.okx.com'  # 沙盒环境可调整
        else:
            self.base_url = 'https://www.okx.com'  # 正式环境

    def _get_open_time(self):
        """获取UTC时间戳"""
        return datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'

    def _sign(self, open_time, method, request_path, body=''):
        """生成签名"""
        message = open_time + method + request_path + body
        mac = hmac.new(
            bytes(self.secret_key, encoding='utf8'),
            bytes(message, encoding='utf8'),
            digestmod=hashlib.sha256
        )
        return base64.b64encode(mac.digest()).decode()

    def _request(self, method, endpoint, params=None):
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        open_time = self._get_open_time()
        if method == 'GET' and params:
            query_string = '&'.join([f"{k}={v}" for k, v in params.items()])
            url += f"?{query_string}"
            request_path = f"{endpoint}?{query_string}"
        else:
            request_path = endpoint
        body = json.dumps(params) if method == 'POST' and params else ''
        signature = self._sign(open_time, method, request_path, body)
        headers = {
            'OK-ACCESS-KEY': self.api_key,
            'OK-ACCESS-SIGN': signature,
            'OK-ACCESS-open_time': open_time,
            'OK-ACCESS-PASSPHRASE': self.passphrase,
            'Content-Type': 'application/json'
        }
        try:
            resp = (requests.get(url, headers=headers)
                    if method == 'GET'
                    else requests.post(url, headers=headers, data=body))
            resp.raise_for_status()
            return resp.json()
        except requests.exceptions.RequestException as e:
            print(f"请求错误: {e}")
            return None

def get_kline_data(client, symbol, timeframe='15m', count=None, dt=None):
    """
    获取K线数据（open_time已转换为北京时间）

    - 不指定dt和count：返回最新一条K线
    - 只指定count：返回最新count条K线（最多支持10000条），以当前时间为截止，向前获取
    - 指定dt：自动分页，返回从dt（含）到现在的所有K线（最多10000条），以当前时间为截止
    Args:
        client: OKX客户端实例 (假设 client 有一个 _request 方法用于发送请求)
        symbol: 交易对, 如 'DOGE-USDT'
        timeframe: K线周期, 如 '15m'
        count: 获取条数, 1~10000
        dt: 起始时间点(北京时间)，如 '2025-01-01 09:00:00'
    Returns:
        pandas.DataFrame
    """

    endpoint = '/api/v5/market/candles'
    all_data = []
    # OKX API 'limit' 参数最大为100
    max_limit_per_request = 100
    max_total_klines = 10000  # 防止拉取过多

    if dt:
        end_timestamp_ms = int(time.time() * 1000) # 当前时间戳
        start_timestamp_ms = int(pd.to_datetime(dt).tz_localize('Asia/Shanghai').tz_convert('UTC').timestamp() * 1000)
        
        current_after = end_timestamp_ms # 初始从当前时间向前查询

        seen_timestamps = set() # 用于去重

        while len(all_data) < max_total_klines:
            params = {'instId': symbol, 'bar': timeframe, 'limit': max_limit_per_request}
            # OKX 的 after 是获取小于或等于此时间戳的数据
            params['after'] = current_after
            
            resp = client._request('GET', endpoint, params)
            if not resp or resp.get('code') != '0' or not resp.get('data'):
                # 没有数据或API调用失败
                break
            
            data = resp['data']
            if not data:
                break # 没有更多数据
            
            unique_data_batch = []
            for item in data:
                timestamp = int(item[0])
                # 如果这个时间戳比我们设定的起始时间还早，则停止拉取
                if timestamp < start_timestamp_ms:
                    # 我们已经拉取到或超过了起始时间 dt
                    break
                if timestamp not in seen_timestamps:
                    seen_timestamps.add(timestamp)
                    unique_data_batch.append(item)
            
            all_data.extend(unique_data_batch)

            # 如果本次返回的数据不足 max_limit_per_request，说明已经没有更多数据了
            # 或者拉取到的数据的时间戳已经早于或等于 start_timestamp_ms
            if len(data) < max_limit_per_request or (data and int(data[-1][0]) < start_timestamp_ms):
                break
            
            # 更新 after 参数为本次获取到的最旧数据的 timestamp - 1，以便下次请求更旧的数据
            current_after = int(data[-1][0]) - 1
            time.sleep(0.2) # 避免频繁请求

        if not all_data:
            return pd.DataFrame()

        # 对所有数据按时间戳排序，并筛选出 dt 及以后的数据
        df = pd.DataFrame(all_data, columns=['open_time', 'open', 'high', 'low', 'close', 'volume', 'volCcy', 'volCcyQuote', 'confirm'])
        
        # 转换为北京时间
        df['open_time'] = pd.to_datetime(pd.to_numeric(df['open_time']), unit='ms', utc=True).dt.tz_convert('Asia/Shanghai')
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        df[numeric_columns] = df[numeric_columns].astype(float)
        
        # 只保留dt及以后的数据
        dt_bj = pd.to_datetime(dt).tz_localize('Asia/Shanghai')
        df = df[df['open_time'] >= dt_bj].copy() # 使用 .copy() 避免 SettingWithCopyWarning
        
        # 确保数据按时间升序排列并去重
        df = df.sort_values('open_time').drop_duplicates(subset=['open_time'], keep='last').reset_index(drop=True)
        return df

    # 只指定count（自动分页，最新count条）
    elif count:
        # 确保count不超过最大限制
        count = min(count, max_total_klines)
        next_after = None # 初始不设置，获取最新数据
        seen_timestamps = set() # 添加去重机制

        while len(all_data) < count:
            # 计算本次需要请求的数量，不超过 API 单次限制
            remaining = count - len(all_data)
            limit = min(max_limit_per_request, remaining)
            
            params = {'instId': symbol, 'bar': timeframe, 'limit': limit}
            if next_after is not None:
                params['after'] = next_after # 请求更旧的数据
            
            resp = client._request('GET', endpoint, params)
            if not resp or resp.get('code') != '0' or not resp.get('data'):
                break # 没有数据或API调用失败
            
            data = resp['data']
            if not data:
                break # 没有更多数据
            
            unique_data_batch = []
            for item in data:
                timestamp = int(item[0])
                if timestamp not in seen_timestamps:
                    seen_timestamps.add(timestamp)
                    unique_data_batch.append(item)
            
            all_data.extend(unique_data_batch)
            
            # 如果返回的数据少于请求的数量，说明没有更多数据了
            if len(data) < limit:
                break
            
            # 更新 next_after 为本次获取到的最旧数据的时间戳减1，以便下次请求更旧的数据
            # data 是按时间戳降序排列的，所以 data[-1][0] 是最旧的时间戳
            next_after = int(data[-1][0]) - 1
            time.sleep(0.2) # 避免频繁请求

        if not all_data:
            return pd.DataFrame()
            
        # 按时间戳升序排序并取最新的 count 条
        # 因为我们是不断向过去拉取，所以 all_data 内部是倒序的（最新在前面），
        # 并且是按批次倒序，整体需要统一排序。
        # 如果我们拉取了比 count 更多的重复数据，去重后可能数量变少，
        # 所以在排序后，取最新的 count 条。
        all_data_sorted = sorted(all_data, key=lambda x: int(x[0]))
        
        # 如果拉取到的数据多于 count，则只取最新的 count 条
        if len(all_data_sorted) > count:
            all_data_final = all_data_sorted[-count:]
        else:
            all_data_final = all_data_sorted

        df = pd.DataFrame(all_data_final, columns=['open_time', 'open', 'high', 'low', 'close', 'volume', 'volCcy', 'volCcyQuote', 'confirm'])
        df['open_time'] = pd.to_datetime(pd.to_numeric(df['open_time']), unit='ms', utc=True).dt.tz_convert('Asia/Shanghai')
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        df[numeric_columns] = df[numeric_columns].astype(float)
        
        # 最终去重保险（尽管上面已经有去重了，这里再做一次以防万一）
        df = df.drop_duplicates(subset=['open_time'], keep='first').reset_index(drop=True)
        return df

    # 不指定dt、不指定count，默认拉最新一条
    else:
        params = {'instId': symbol, 'bar': timeframe, 'limit': 1}
        resp = client._request('GET', endpoint, params)
        if resp and resp.get('code') == '0' and resp.get('data'):
            data = resp['data']
            df = pd.DataFrame(data, columns=['open_time', 'open', 'high', 'low', 'close', 'volume', 'volCcy', 'volCcyQuote', 'confirm'])
            df['open_time'] = pd.to_datetime(pd.to_numeric(df['open_time']), unit='ms', utc=True).dt.tz_convert('Asia/Shanghai')
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            df[numeric_columns] = df[numeric_columns].astype(float)
            df = df.sort_values('open_time').reset_index(drop=True)
            return df
        else:
            return pd.DataFrame()
def read_local_data(file_path):
    """
    读取本地数据文件
    Args:
        file_path: 文件路径
    Returns:
        pandas.DataFrame or dict: 读取的数据
    """
    print(f"正在读取本地文件: {file_path}")
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return None
    try:
        file_extension = os.path.splitext(file_path)[1].lower()
        if file_extension == '.csv':
            data = pd.read_csv(file_path)
            print(f"成功读取CSV文件，共 {len(data)} 行数据")
            return data
        elif file_extension == '.json':
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"成功读取JSON文件")
            return data
        elif file_extension in ['.xlsx', '.xls']:
            data = pd.read_excel(file_path)
            print(f"成功读取Excel文件，共 {len(data)} 行数据")
            return data
        else:
            print(f"不支持的文件格式: {file_extension}")
            return None
    except Exception as e:
        print(f"读取文件失败: {e}")
        return None

def write_data_to_local(data, file_path, file_format='csv'):
    """
    将数据写入本地文件
    Args:
        data: 要保存的数据 (DataFrame 或 dict)
        file_path: 保存路径
        file_format: 文件格式 ('csv', 'json', 'excel')
    Returns:
        bool: 是否保存成功
    """
    print(f"正在保存数据到: {file_path}")
    try:
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        if file_format.lower() == 'csv':
            if isinstance(data, pd.DataFrame):
                data.to_csv(file_path, index=False, encoding='utf-8')
            else:
                pd.DataFrame([data]).to_csv(file_path, index=False, encoding='utf-8')
        elif file_format.lower() == 'json':
            with open(file_path, 'w', encoding='utf-8') as f:
                if isinstance(data, pd.DataFrame):
                    data.to_json(f, orient='records', indent=2, force_ascii=False)
                else:
                    json.dump(data, f, indent=2, ensure_ascii=False, default=str)
        elif file_format.lower() == 'excel':
            if isinstance(data, pd.DataFrame):
                data.to_excel(file_path, index=False)
            else:
                pd.DataFrame([data]).to_excel(file_path, index=False)
        else:
            print(f"不支持的文件格式: {file_format}")
            return False
        print(f"数据保存成功: {file_path}")
        return True
    except Exception as e:
        print(f"保存数据失败: {e}")
        return False

def main():
    print("=== OKX交易所SDK测试 ===\n")
    try:
        client = OKXClient()  # 优先从环境变量读取
    except Exception as e:
        print(f"初始化失败: {e}")
        return

    symbol = 'DOGE-USDT'
    timeframe = '15m'

    print("1. 测试获取指定时间区间K线数据（北京时间）")
    print("-" * 30)
    start_time = '2025-01-01 09:00:00'
    df = get_kline_data(client, symbol, timeframe=timeframe, dt=start_time)
    if not df.empty:
        print(df.head())
        print(f"共获取{len(df)}条数据")
        print(f"数据区间：{df.iloc[0]['open_time']} ~ {df.iloc[-1]['open_time']}")
    else:
        print("未获取到数据。")
        
        
    print("\n2. 测试获取最新100条K线（北京时间）")
    print("-" * 30)
    df2 = get_kline_data(client, symbol, timeframe='1m', count=100)
    if not df2.empty:
        print(df2.tail())



    # print("\n3. 测试获取最新一条K线（北京时间）")
    # print("-" * 30)
    # df3 = get_kline_data(client, symbol, timeframe='1m')
    # if not df3.empty:
    #     print(df3)

    # print("\n4. 测试数据写入本地")
    # print("-" * 30)
    # write_data_to_local(df, f'data/{symbol}_15m_from_{start_time.replace(" ", "_")}.csv', 'csv')
    # write_data_to_local(df2, f'data/{symbol}_1m_latest100.csv', 'csv')
    # write_data_to_local(df3, f'data/{symbol}_1m_latest1.csv', 'csv')

    # print("\n5. 测试读取本地数据")
    # print("-" * 30)
    # loaded_csv = read_local_data(f'data/{symbol}_15m_from_{start_time.replace(" ", "_")}.csv')
    # if loaded_csv is not None:
    #     print(f"从CSV读取的数据: {len(loaded_csv)} 行")

    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()