
import requests
import pandas as pd
import sqlite3
import datetime
from typing import List, Union
import pytz # 用于时区转换
# import pandas as pd
# --- 猴子补丁开始 ---
# 解决 ImportError: cannot import name 'Na<PERSON>' from 'numpy'
# 强制将 numpy.nan 导入为 numpy.NaN
import numpy
if not hasattr(numpy, 'NaN'):
    numpy.NaN = numpy.nan
# --- 猴子补丁结束 ---
import pandas_ta as ta # 导入 pandas_ta 库 (现在可以正常导入了)
# from ku import generate_plot,  generate_dual_axis_plot
from ku import *
from config import  calculate_kline_needed,get_kline_data,add_rsi_to_kline_data


from tradeing import check_trading_single ,judge_signal,calc_price,batch_judge_trade
import warnings
warnings.filterwarnings('ignore', category=UserWarning)

def sub_kline_time(old_date, count, kline_type):
    """
    日期转换函数（向前推算）
    :param old_date: str/datetime，原日期，支持多种字符串日期格式或datetime对象
    :param count: int，步数
    :param kline_type: str，K线类型，如"1m","5m","15m","1h","1d"
    :return: 新的日期（datetime对象）
    """
    # 支持字符串和datetime输入
    if isinstance(old_date, str):
        dt = None
        tried_formats = [
            "%Y-%m-%d %H:%M:%S",
            "%Y-%m-%d",
            "%Y-%m-%d %H:%M",
            "%Y-%m-%d %H",
            "%Y/%m/%d %H:%M:%S",
            "%Y/%m/%d",
            "%Y/%m/%d %H:%M",
            "%Y/%m/%d %H",
        ]
        for fmt in tried_formats:
            try:
                dt = datetime.strptime(old_date, fmt)
                break
            except ValueError:
                continue
        if dt is None:
            raise ValueError(f"无法识别日期格式: {old_date}")
    else:
        dt = old_date

    # K线类型转换为分钟数
    unit_map = {'m': 1, 'h': 60, 'd': 1440}
    import re
    match = re.match(r"(\d+)([mhd])", kline_type)
    if not match:
        raise ValueError(f"不支持的k线类型: {kline_type}")
    num, unit = match.groups()
    minutes = int(num) * unit_map[unit]

    new_dt = dt - timedelta(minutes=minutes * count)
    return new_dt.strftime("%Y-%m-%d %H:%M:%S")

def is_last_segment_turning(arr, param):
    min_len = param.get('min_len', 3)
    interval = param.get('interval', 3)
    single = param.get('single', 1.5)
    n = len(arr)
    if n < min_len + 1:
        print(f'[长度不足] arr实际长度: {n}, min_len+1: {min_len+1}')
        return 0

    last_jump = abs(arr[-1] - arr[-2])
    if last_jump <= single:
        print(f'[最后一跳绝对值不够] last_jump: {last_jump}, single: {single}')
        return 0
    else:
        print(f'[最后一跳通过] last_jump: {last_jump}, single: {single}')

    trend_segment = arr[-(min_len+1):-1]
    print(f'[趋势段] trend_segment: {trend_segment}')

    # 单调递增判定
    is_increasing = True
    for i in range(len(trend_segment) - 1):
        diff_segment = trend_segment[i+1] - trend_segment[i]
        if not (diff_segment > 0 and abs(diff_segment) <= interval):
            print(f'[递增失败] 下标{i}到{i+1}，diff_segment: {diff_segment}, interval: {interval}')
            is_increasing = False
            break
    if is_increasing:
        print('[递增趋势] 判定为单调递增')

    # 宽松递减判定（允许小反弹，只要绝对值≤interval就通过）
    is_decreasing = all(abs(trend_segment[i+1] - trend_segment[i]) <= interval for i in range(len(trend_segment)-1))
    if is_decreasing:
        print('[递减趋势] 判定为宽松递减')
    else:
        for i in range(len(trend_segment)-1):
            diff_segment = trend_segment[i+1] - trend_segment[i]
            if abs(diff_segment) > interval:
                print(f'[递减失败] 下标{i}到{i+1}，diff_segment: {diff_segment}, interval: {interval}')

    diff = arr[-1] - arr[-2]
    print(f'[最后一跳差值] diff: {diff} (arr[-1]: {arr[-1]}, arr[-2]: {arr[-2]})')

    if is_increasing and diff < 0:
        print('[拐点判断] 顶部拐点成立')
        return -1
    elif is_decreasing and diff > 0:
        print('[拐点判断] 底部拐点成立')
        return 1
    else:
        print('[拐点判断] 不成立')
        return 0

def analyze_trendV0(data: List[float], malen: int) -> dict:
    """
    分析列表数据的趋势
    
    参数:
    data: 数值列表
    malen: 检查长度，从后往前的长度
    
    返回:
    包含趋势分析结果的字典
    """
    if len(data) < malen:
        return {
            'error': f'数据长度 {len(data)} 小于检查长度 {malen}'
        }
    
    # 取最后 malen 个数据点
    recent_data = data[-malen:]
    
    # 计算趋势指标
    x = np.arange(malen)
    y = np.array(recent_data)
    
    # 线性回归计算斜率
    slope = np.polyfit(x, y, 1)[0]
    
    # 计算相对变化率
    relative_change = (recent_data[-1] - recent_data[0]) / recent_data[0] * 100
    
    # 计算移动平均（如果长度足够）
    if malen >= 3:
        ma_short = np.mean(recent_data[-3:])  # 最近3个点的平均
        ma_long = np.mean(recent_data[:3])    # 开始3个点的平均
        ma_trend = "上升" if ma_short > ma_long else "下降" if ma_short < ma_long else "平稳"
    else:
        ma_trend = "数据点太少"
    
    # 判断总体趋势
    if abs(slope) < 0.01:  # 斜率接近0
        trend = "平稳"
    elif slope > 0:
        if slope > 0.1:
            trend = "强烈上升"
        else:
            trend = "缓慢上升"
    else:
        if slope < -0.1:
            trend = "强烈下降"
        else:
            trend = "缓慢下降"
    
    # 计算波动性（标准差）
    volatility = np.std(recent_data)
    
    # 计算最大回撤
    peak = np.maximum.accumulate(recent_data)
    drawdown = (recent_data - peak) / peak * 100
    max_drawdown = np.min(drawdown)
    
    return {
        'trend': trend,
        'slope': slope,
        'relative_change_percent': round(relative_change, 2),
        'moving_average_trend': ma_trend,
        'volatility': round(volatility, 4),
        'max_drawdown_percent': round(max_drawdown, 2),
        'start_value': recent_data[0],
        'end_value': recent_data[-1],
        'min_value': min(recent_data),
        'max_value': max(recent_data),
        'data_points': malen
    }
def safe_get(d, keys, default=''):
    """多级安全取值，遇到None或非dict自动返回default"""
    for k in keys:
        if isinstance(d, dict):
            d = d.get(k, None)
        else:
            return default
        if d is None:
            return default
    return d if d is not None else default

def list_to_str(lst, prefix=''):
    """将列表转换为带前缀的字符串，用于DataFrame存储"""
    if not lst:
        return ''
    return '; '.join([f"{prefix}{str(item)}" for item in lst])

def write_analysis_to_kline(df, next_index, analysis_result):
    """
    将分析结果写入kline_df_with_rsi的指定行，包括多字段（如投资建议、投资可信度、投资理由等）。
    如果DataFrame中无对应列则自动补充，如果索引越界则打印警告。
    """
    if not isinstance(analysis_result, dict):
        print(f"警告：analysis_result 为空或不是字典，实际值为：{analysis_result}")
        return

    # 兼容你的advice结构（如投资建议、投资可信度、投资理由）
    advice = analysis_result.get('advice', {})
    fields_to_update = {
        '投资建议': advice.get('投资建议', analysis_result.get('投资建议', '')),
        # '投资可信度': advice.get('投资可信度', analysis_result.get('投资可信度', '')),
        # '投资理由': list_to_str(advice.get('投资理由', analysis_result.get('投资理由', []))),
        # # 你可以继续补充其它分析字段
        # '趋势': analysis_result.get('trend', ''),
        # '斜率': analysis_result.get('slope', ''),
        # '移动均线趋势': analysis_result.get('moving_average_trend', ''),
        # ...（略，其它字段同之前）
    }

    # 确保所有字段都在DataFrame中
    for col in fields_to_update.keys():
        if col not in df.columns:
            df[col] = ""
    if next_index < len(df):
        for field, value in fields_to_update.items():
            df.at[next_index, field] = value
    else:
        print(f"警告：next_index = {next_index} 已超出DataFrame范围，无法写入数据！")
        print(fields_to_update['投资建议'])


if __name__ == "__main__":

    my_parm = {
    "rsi_length": 8,
    "macd_Fast": 6, # 通常指MACD中的慢速EMA周期 (
    "macd_Slow": 12, # 通常指MACD中的快速EMA周期 (
    "macd_Signal": 5, # 通常指MACD中信号线EMA周期 (
    "max_lookback_kline_count": 140,
    "my_sma_length": 5,
    "my_ema_length": 5,
    "my_gaussian_sigma": 1.9,
    "zhiyinPre": 4.0,
    "zhisunPre": 0.2,
    "windowsTime":480,
    "Uname": "doge",
    "Ktime": "15m",
    "strTime": "2025-05-01",
    "endTime": "2025-05-04"
}
   
    ###获取k线
    # 1. 计算所需获取的K线数量
    needed_klines = calculate_kline_needed(my_parm)
    kline_df = None
    kline_df_with_rsi = None
    # print(needed_klines)
    # 2. 获取K线数据
    # 交易对为 DOGEUSDT，时间间隔15分钟
    # kline_df = get_kline_data(symbol="DOGEUSDT", interval="15m", count=needed_klines)
    # kline_df = kline_df.iloc[:, 0:6]
    # print(kline_df.tail(2))

    ####历史数据回测
    strTime=sub_kline_time(my_parm['strTime'], needed_klines,my_parm['Ktime'])

    # print(strTime)
    try:
        kline_df = get_local_kline_data(my_parm['Uname'], my_parm['Ktime'], strTime, my_parm['endTime'])
        print(len(kline_df),kline_df.head())
    except Exception as e:
        print(e)


    tradingSingle=0  ##交易信号
    close_values = kline_df['close'].values  # 预提取，避免重复调用
    total_loops = len(kline_df) - needed_klines + 1
    # print(f"开始处理 {total_loops} 个窗口...")
    countS1=[]
    countS2=[]
    countTop=[ ]
    countDown=[]
    smoothS=[]
    single1 = []  # ✅ 修复: 改为空列表
    single2 = []  # ✅ 修复: 改为空列表
    singleMacd = []
    for count in range(total_loops):
    # 添加异常处理
    
        nowPrice = close_values[count + needed_klines - 1]  # 使用预提取的数据
        window_close = close_values[count : count + needed_klines]  # 使用预提取的数据
        # print(len(window_close),nowPrice,window_close.tolist(),count + needed_klines - 1)
        # window_close=pd.Series(window_close)
        # print(len(window_close),nowPrice,count + needed_klines - 1)
            
        ##计算金叉死叉
        
        
        window_MACD=fast_macd_cross_check(window_close, fast_period=my_parm["macd_Fast"],slow_period=my_parm["macd_Slow"],
            signal_period=my_parm["macd_Signal"])
        if window_MACD['cross_type']=='golden_cross':
            singleMacd.append(1)
        elif window_MACD['cross_type']=='death_cross':
            singleMacd.append(-1)
        else:
            singleMacd.append(0)
        
        # print(f"MACD交叉信号:{window_MACD['cross_type']}")
        # print("-" * 50) 
       
        window_rsi = fast_rsi_calculation(window_close, my_parm["rsi_length"])
        # print(len(window_rsi),window_rsi[-8:])
        
        # print(len(window_rsi),window_rsi[-5:].values)
        # if count >10:
        # break
        # continue
        
        valid_rsi = window_rsi[~np.isnan(window_rsi)]
     
        if len(valid_rsi) > 0:
            smooth_rsi_window = fast_gaussian_smooth(valid_rsi, my_parm["my_gaussian_sigma"])
            smoothS.append(smooth_rsi_window[-1])
            # print(count)
        else:
            
            continue
        # smooth_rsi_window=valid_rsi
        # print(len(window_rsi),window_rsi)
        # print(len(valid_rsi),valid_rsi)
        # print(len(smooth_rsi_window),smooth_rsi_window)
        
        ADX_resualt=calculate_adx_v2(kline_df[count + needed_klines-56 : count + needed_klines])
        # ADX_resualt.iloc[-1]['adx']<25 and ADX_resualt.iloc[-1]['adx']>20 and ADX_resualt.iloc[-1]['+di']<ADX_resualt.iloc[-1]['-di']:
        
        # print(smooth_rsi_window)
        point=count + needed_klines - 1   ##d当前回测点位
        # print(count%3)
        stats = analyze_numerical_series( smooth_rsi_window [-my_parm["max_lookback_kline_count"]:])
        
        ##计算超空做多
        if smooth_rsi_window[-1]<=stats['conf_interval_2sigma'][0] :
            countDown.append(point)
            single1.append(1)
            # print(single1[-3:])        
            
        else:
            single1.append(0)   
                
            
        ##计算超多做空  
        if smooth_rsi_window[-1]>= stats['conf_interval_2sigma'][1]:
            #  print(stats['conf_interval_2sigma'][0],stats['conf_interval_2sigma'][1],smooth_rsi_window[-1])
            countTop.append(point)
            single2.append(1)   
            
        else:
            single2.append(0)
            
        # ✅ 修复: 添加长度检查
        if len(single1) >= 3 and (single1[-3:] == [0, 1, 0] or single1[-3:] == [1, 1, 0]):
            countS1.append(point)  ##超空做多
            kline_df.at[point, 'buyPrice'] = nowPrice
            kline_df.at[point, 'nowSingle'] = 1
            print(stats['conf_interval_2sigma'][0], stats['conf_interval_2sigma'][1], smooth_rsi_window[-1])
            if ADX_resualt is not None and len(ADX_resualt) > 0:  # ✅ 修复: 检查ADX是否有效
                print(f"{point}做多决策: +DI = {ADX_resualt.iloc[-1]['+di']:.2f}, -DI = {ADX_resualt.iloc[-1]['-di']:.2f}***")
                
        elif len(single2) >= 3 and (single2[-3:] == [0, 1, 0] or single2[-3:] == [1, 1, 0]):
            countS2.append(point)   ##超多做空
            kline_df.at[point, 'buyPrice'] = nowPrice
            kline_df.at[point, 'nowSingle'] = -1
            print(stats['conf_interval_2sigma'][0], stats['conf_interval_2sigma'][1], smooth_rsi_window[-1])
            if ADX_resualt is not None and len(ADX_resualt) > 0:  # ✅ 修复: 检查ADX是否有效
                print(f"{point}做空决策: +DI = {ADX_resualt.iloc[-1]['+di']:.2f}, -DI = {ADX_resualt.iloc[-1]['-di']:.2f}***")
        else:
            kline_df.at[point, 'buyPrice'] = 0
            kline_df.at[point, 'nowSingle'] = 0
            
    # print(len(singleMacd),singleMacd)
    kline_df=batch_judge_trade(kline_df,my_parm['zhiyinPre'],my_parm['zhisunPre'],my_parm['windowsTime'],my_parm['Ktime'])
    nums = pd.to_numeric(kline_df['达成百分比'], errors='coerce')
    ##总收益
    total = nums.sum()
    print(total,'多：',(kline_df['nowSingle'] == 1).sum(),'空：',(kline_df['nowSingle'] == -1).sum())
    
    kline_df.to_excel("71逐段V1kline_with_advice.xlsx", index=False)
    print("K线数据已成功保存到 逐段kline_with_advice.xlsx")

                
    # print(countS1,countS2)
    # print(countDown,countTop)
    # plot_with_markersV1(list(range(0,len(kline_df))),close_values,countS2,countS1,'single7-1.png')
    
    
                # --------- 双轴图组合（RSI+价格） ----------
    print(len(smoothS))
    print(len(kline_df['close'][(needed_klines-1):]))
    countS1 = [x - (needed_klines-1) for x in countS1]
    countS2 = [x - (needed_klines-1) for x in countS2]
    # a=pd.Series(smoothS)
    # b=pd.Series(kline_df['close'][(needed_klines-1):])
    # print(needed_klines,countS1,countS2)
    generate_dual_axis_plotV1(
        y1_values=kline_df['close'][(needed_klines-1):].to_list(),
        y2_values=smoothS,
        title=f"DOGEUSDT 15m (RSI & 价格)",
        y1_label=f"RSI (周期: {my_parm['rsi_length']})",
        y2_label="DOGEUSDT 价格",
        x_label="K线索引",
        filename="dogeusdt_rsi_price_dual_axis.png",
        y1_color='tab:blue',
        y2_color='tab:red',
        countS1=countS1,###做多
        countS2=countS2,###做空
        # signal_list=singleMacd
    )
        # plot_with_dynamic_scale(window_close, window_rsi,smooth_rsi_window)
        # break
    
    #     window_close=window_close[-lookback_period:]
    #     window_rsi=window_rsi[-lookback_period:]
    #     smooth_rsi_window=smooth_rsi_window[-lookback_period:]
      
    #     result = detector.detect_pattern(window_close.tolist(), window_rsi.tolist(), smooth_rsi_window.tolist(), Ktime)
     
    #     # print(analysis_result)
    #     # break
    #     # 投资建议写入下一根K线
    #     next_index = count + needed_klines
    #     # 模拟交易
    #     nowSingle = judge_signal(round(result['confidence'], 3),result['pattern'])
        
    #     # print(round(result['confidence'], 3))
    #     # break
    #     tradingSingle, nowSingle = check_trading_single(tradingSingle, nowSingle)
        
    #     # # 写入结果
    #     if next_index < len(kline_df_with_rsi):  # 添加边界检查
    #         kline_df_with_rsi.at[next_index, 'tradingSingle'] = tradingSingle
    #         kline_df_with_rsi.at[next_index, 'nowSingle'] = nowSingle
    #         if nowSingle != 0:
    #             kline_df_with_rsi.at[next_index, 'buyPrice'] = calc_price(nowSingle, nowPrice, 0)
                
    #     kline_df_with_rsi.at[next_index, '识别模式'] = result['pattern']
    #     kline_df_with_rsi.at[next_index, '置信度'] = f"{result['confidence']:.2%}"
    #     kline_df_with_rsi.at[next_index, '建议'] = result['recommendation']
    #     kline_df_with_rsi.at[next_index, '价格趋势'] = result['price_features']['trend_type']
    #     kline_df_with_rsi.at[next_index, '当前RSI'] = f"{result['rsi_features']['current_rsi']:.2f}"

    #     # 进度显示
    #     if count % 50 == 0:
    #         print(f"进度: {count}/{total_loops} ({count/total_loops*100:.1f}%)")

    # # 保存结果
    # kline_df_with_rsi=batch_judge_trade(kline_df_with_rsi,my_parm['zhiyinPre'],my_parm['zhisunPre'],my_parm['windowsTime'],Ktime)
   
    # nums = pd.to_numeric(kline_df_with_rsi['达成百分比'], errors='coerce')
    # ##总收益
    # total = nums.sum()
    # print(total)

    # kline_df_with_rsi.to_excel("6-29BTC逐段V1kline_with_advice.xlsx", index=False)
    # print("K线数据已成功保存到 逐段kline_with_advice.xlsx")



