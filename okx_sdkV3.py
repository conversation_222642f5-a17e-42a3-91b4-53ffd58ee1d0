import requests
import pandas as pd
import time
from datetime import datetime, timezone
import pytz

class BinanceKlineDataFetcher:
    def __init__(self):
        self.base_url = "https://fapi.binance.com"
        self.klines_endpoint = "/fapi/v1/klines"
        
    def datetime_to_timestamp(self, dt_str):
        """
        输入时间字符串（如 '2025-1-1 12:00'），视为北京时间，返回UTC毫秒时间戳
        """
        tz = pytz.timezone('Asia/Shanghai')  # 明确为北京时间
        dt = datetime.strptime(dt_str, "%Y-%m-%d %H:%M")
        dt = tz.localize(dt)
        timestamp = int(dt.astimezone(timezone.utc).timestamp() * 1000)
        return timestamp
    
    def get_klines_batch(self, symbol, interval, start_time, end_time, limit=1500):
        url = self.base_url + self.klines_endpoint
        params = {
            'symbol': symbol,
            'interval': interval,
            'startTime': start_time,
            'endTime': end_time,
            'limit': limit
        }
        try:
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException:
            return None
    
    def get_historical_klines(self, symbol, interval, start_time_str, end_time_str):
        start_timestamp = self.datetime_to_timestamp(start_time_str)
        end_timestamp = self.datetime_to_timestamp(end_time_str)
        all_klines = []
        current_start = start_timestamp
        while current_start < end_timestamp:
            klines_batch = self.get_klines_batch(
                symbol=symbol,
                interval=interval,
                start_time=current_start,
                end_time=end_timestamp,
                limit=1500
            )
            if not klines_batch or len(klines_batch) == 0:
                break
            all_klines.extend(klines_batch)
            current_start = klines_batch[-1][0] + 1
            time.sleep(0.1)
        if not all_klines:
            return None
        return self.convert_to_dataframe(all_klines)
    
    def convert_to_dataframe(self, klines_data):
        columns = [
            'open_time', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_volume', 'count', 'taker_buy_volume',
            'taker_buy_quote_volume', 'ignore'
        ]
        df = pd.DataFrame(klines_data, columns=columns)
        # 转为东八区带时区时间
        df['timestamp'] = pd.to_datetime(df['open_time'], unit='ms', utc=True).dt.tz_convert('Asia/Shanghai')
        for col in ['open', 'high', 'low', 'close']:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        df['confirm'] = 1
        if len(df) > 0:
            df.iloc[0, df.columns.get_loc('confirm')] = 0  # 最新K线（第一条）confirm=0
        result_df = df[['timestamp', 'open', 'high', 'low', 'close', 'confirm']].copy()
        # 最新在前排序
        result_df = result_df.sort_values('timestamp', ascending=False).reset_index(drop=True)
        return result_df

if __name__ == "__main__":
    fetcher = BinanceKlineDataFetcher()
    symbol = "DOGEUSDT"
    interval = "15m"
    start_time = "2025-1-1 12:00"   # 视为北京时间
    end_time = "2025-6-23 22:00"     # 视为北京时间
    df = fetcher.get_historical_klines(
        symbol=symbol,
        interval=interval,
        start_time_str=start_time,
        end_time_str=end_time
    )
    print(df.head())