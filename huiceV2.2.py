import requests
import pandas as pd
import sqlite3
from datetime import datetime, timedelta
from typing import List, Union
import pytz
import numpy as np
import itertools
import traceback
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing
import time
import os

# --- 猴子补丁开始 ---
if not hasattr(np, 'NaN'):
    np.NaN = np.nan
# --- 猴子补丁结束 ---

import pandas_ta as ta

# 假设这些自定义模块和函数在您的环境中是可用的
from ku import *
from config import calculate_kline_needed, get_kline_data, add_rsi_to_kline_data
from tradeing import check_trading_single, judge_signal, calc_price, batch_judge_trade
import warnings
warnings.filterwarnings('ignore', category=UserWarning)
warnings.filterwarnings('ignore', category=RuntimeWarning)


# ==============================================================================
# 辅助函数 (保持您的原始代码不变)
# ==============================================================================

def sub_kline_time(old_date, count, kline_type):
    """
    日期转换函数（向前推算）
    :param old_date: str/datetime，原日期
    :param count: int，步数
    :param kline_type: str，K线类型
    :return: 新的日期字符串
    """
    if isinstance(old_date, str):
        dt = None
        tried_formats = [
            "%Y-%m-%d %H:%M:%S", "%Y-%m-%d", "%Y-%m-%d %H:%M", "%Y-%m-%d %H",
            "%Y/%m/%d %H:%M:%S", "%Y/%m/%d", "%Y/%m/%d %H:%M", "%Y/%m/%d %H",
        ]
        for fmt in tried_formats:
            try:
                dt = datetime.strptime(old_date, fmt)
                break
            except ValueError:
                continue
        if dt is None:
            raise ValueError(f"无法识别日期格式: {old_date}")
    else:
        dt = old_date

    unit_map = {'m': 1, 'h': 60, 'd': 1440}
    import re
    match = re.match(r"(\d+)([mhd])", kline_type)
    if not match:
        raise ValueError(f"不支持的k线类型: {kline_type}")
    num, unit = match.groups()
    minutes = int(num) * unit_map[unit]

    new_dt = dt - timedelta(minutes=minutes * count)
    return new_dt.strftime("%Y-%m-%d %H:%M:%S")


# ==============================================================================
# 15分钟时间框架优化参数网格配置
# ==============================================================================

def get_15m_optimized_param_grid(optimization_level="comprehensive"):
    """
    获取适用于15分钟时间框架的优化参数网格
    
    Args:
        optimization_level: 优化级别
            - "quick": 快速测试 (~100个组合)
            - "moderate": 中等测试 (~500个组合) 
            - "comprehensive": 全面测试 (~2000个组合)
            - "intensive": 深度测试 (~5000个组合)
    
    Returns:
        dict: 参数网格字典
    """
    
    if optimization_level == "quick":
        # 快速测试：基于经验的最优区间
        param_grid = {
            "macd_Fast": [3, 5, 8],                           # 3个值
            "macd_Slow": [18, 24, 30],                        # 3个值
            "macd_Signal": [7, 9, 12],                        # 3个值
            "zhiyinPre": [2.0, 2.5, 3.0],                    # 3个值
            "zhisunPre": [0.2, 0.3],                         # 2个值
            "max_lookback_kline_count": [120, 140],           # 2个值
        }
        # 总组合数: 3×3×3×3×2×2 = 324 (过滤后约100个)
        
    elif optimization_level == "moderate":
        # 中等测试：适度扩展参数范围
        param_grid = {
            "macd_Fast": [2, 3, 4, 5, 6, 8],                 # 6个值
            "macd_Slow": [16, 20, 24, 26, 30, 34],           # 6个值
            "macd_Signal": [6, 7, 9, 12, 15],                # 5个值
            "zhiyinPre": [1.5, 2.0, 2.5, 3.0, 3.5],         # 5个值
            "zhisunPre": [0.15, 0.2, 0.25, 0.3],             # 4个值
            "max_lookback_kline_count": [100, 120, 140, 160], # 4个值
        }
        # 总组合数: 6×6×5×5×4×4 = 7,200 (过滤后约500个)
        
    elif optimization_level == "comprehensive":
        # 全面测试：您当前使用的参数范围优化版
        param_grid = {
            "macd_Fast": [2, 3, 4, 5, 6, 7, 8, 9, 10],       # 9个值
            "macd_Slow": [14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 38], # 12个值
            "macd_Signal": [5, 6, 7, 8, 9, 10, 12, 15, 18],  # 9个值
            "zhiyinPre": [1.2, 1.5, 1.8, 2.0, 2.2, 2.5, 2.8, 3.0, 3.2, 3.5, 4.0], # 11个值
            "zhisunPre": [0.15, 0.2, 0.25, 0.3, 0.35],       # 5个值
            "max_lookback_kline_count": [100, 120, 140, 160, 180], # 5个值
        }
        # 过滤后约2000个有效组合
        
    elif optimization_level == "intensive":
        # 深度测试：精细化参数调优
        param_grid = {
            "macd_Fast": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], # 12个值
            "macd_Slow": [12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 42], # 15个值
            "macd_Signal": [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 18, 21], # 13个值
            "zhiyinPre": [1.0, 1.2, 1.4, 1.5, 1.6, 1.8, 2.0, 2.2, 2.4, 2.5, 2.6, 2.8, 3.0, 3.2, 3.4, 3.5, 3.8, 4.0, 4.5], # 19个值
            "zhisunPre": [0.1, 0.12, 0.15, 0.18, 0.2, 0.22, 0.25, 0.28, 0.3, 0.32, 0.35, 0.4], # 12个值
            "max_lookback_kline_count": [80, 100, 120, 140, 160, 180, 200], # 7个值
        }
        # 过滤后约5000个有效组合
        
    return param_grid


# ==============================================================================
# 并行处理的核心回测函数
# ==============================================================================

def run_backtest_parallel(params_tuple):
    """
    并行处理专用的回测函数，接受元组参数以支持multiprocessing
    
    Args:
        params_tuple: (base_params, combo, index, debug) 的元组
    
    Returns:
        tuple: (index, combo, total_profit, num_long, num_short, success, error_msg)
    """
    base_params, combo, index, debug = params_tuple
    
    try:
        # 合并基础参数和测试参数
        current_params = base_params.copy()
        current_params.update(combo)
        
        # 验证参数合理性
        if combo["macd_Fast"] >= combo["macd_Slow"]:
            return (index, combo, None, 0, 0, False, "macd_Fast >= macd_Slow")
            
        # 执行回测
        total_profit, num_long, num_short = run_backtest_single(current_params, debug)
        
        if total_profit is not None:
            return (index, combo, total_profit, num_long, num_short, True, None)
        else:
            return (index, combo, None, 0, 0, False, "回测执行失败")
            
    except Exception as e:
        error_msg = f"参数组合执行异常: {str(e)}"
        if debug:
            error_msg += f"\n{traceback.format_exc()}"
        return (index, combo, None, 0, 0, False, error_msg)


def run_backtest_single(params: dict, debug: bool = False):
    """
    单次回测函数（从您的原始代码适配）
    """
    try:
        # 计算并获取K线数据
        needed_klines = calculate_kline_needed(params)
        strTime = sub_kline_time(params['strTime'], needed_klines, params['Ktime'])
        
        if debug:
            print(f"[DEBUG] 参数: {params.get('macd_Fast')}-{params.get('macd_Slow')}-{params.get('macd_Signal')}")
        
        # 注意：这里需要使用 get_local_kline_data 而不是 get_kline_data
        kline_df = get_local_kline_data(params['Uname'], params['Ktime'], strTime, params['endTime'])

        if kline_df is None or kline_df.empty:
            return None, 0, 0
        
        total_loops = len(kline_df) - needed_klines + 1
        if total_loops <= 0:
            return None, 0, 0
            
        close_values = kline_df['close'].values
        singleMacd = []
        
        # 初始化交易列
        kline_df['buyPrice'] = 0
        kline_df['nowSingle'] = 0
        
        for count in range(total_loops):
            point = count + needed_klines - 1
            window_close = close_values[count : count + needed_klines]
            
            window_MACD = fast_macd_cross_check(
                window_close, 
                fast_period=params["macd_Fast"], 
                slow_period=params["macd_Slow"], 
                signal_period=params["macd_Signal"]
            )
            
            if window_MACD['cross_type'] == 'golden_cross':
                singleMacd.append(1)
            elif window_MACD['cross_type'] == 'death_cross':
                singleMacd.append(-1)
            else:
                singleMacd.append(0)
            
            nowPrice = window_close[-1]
            if singleMacd[-1] == 1:
                kline_df.at[point, 'buyPrice'] = nowPrice
                kline_df.at[point, 'nowSingle'] = 1
            elif singleMacd[-1] == -1:
                kline_df.at[point, 'buyPrice'] = nowPrice
                kline_df.at[point, 'nowSingle'] = -1

        # 批量交易判断
        kline_df_result = batch_judge_trade(
            kline_df, 
            params['zhiyinPre'], 
            params['zhisunPre'], 
            params['windowsTime'], 
            params['Ktime']
        )
        
        nums = pd.to_numeric(kline_df_result['达成百分比'], errors='coerce').fillna(0)
        total_profit = nums.sum()
        num_long = (kline_df_result['nowSingle'] == 1).sum()
        num_short = (kline_df_result['nowSingle'] == -1).sum()
        
        return total_profit, num_long, num_short

    except Exception as e:
        if debug:
            print(f"[DEBUG] 回测异常: {e}")
        return None, 0, 0


# ==============================================================================
# 并行优化主函数
# ==============================================================================

def run_parallel_optimization(base_params, param_grid, max_workers=6, debug=False, 
                            save_results=True, optimization_level="comprehensive"):
    """
    运行并行参数优化
    
    Args:
        base_params: 基础参数字典
        param_grid: 参数网格字典，如果为None则使用预设网格
        max_workers: 最大并行进程数，默认6
        debug: 是否开启调试模式
        save_results: 是否保存结果到Excel
        optimization_level: 优化级别（当param_grid为None时使用）
    
    Returns:
        DataFrame: 优化结果
    """
    print("=" * 60)
    print("🚀 并行MACD参数优化器启动")
    print("=" * 60)
    
    # 如果没有提供参数网格，使用预设网格
    if param_grid is None:
        param_grid = get_15m_optimized_param_grid(optimization_level)
        print(f"📊 使用预设参数网格，优化级别: {optimization_level}")
    
    # 生成参数组合
    keys, values = zip(*param_grid.items())
    param_combinations = [dict(zip(keys, v)) for v in itertools.product(*values)]
    
    # 过滤无效组合
    valid_combinations = []
    for combo in param_combinations:
        if combo["macd_Fast"] < combo["macd_Slow"]:  # 确保Fast < Slow
            valid_combinations.append(combo)
    
    total_combinations = len(valid_combinations)
    print(f"📈 生成参数组合: {len(param_combinations)} 个")
    print(f"✅ 有效组合（Fast < Slow）: {total_combinations} 个")
    print(f"⚡ 并行进程数: {max_workers}")
    print(f"🖥️  CPU核心数: {multiprocessing.cpu_count()}")
    print()
    
    # 准备并行任务参数
    tasks = [(base_params, combo, i, debug) for i, combo in enumerate(valid_combinations)]
    
    results = []
    start_time = time.time()
    completed_count = 0
    failed_count = 0
    
    print("🔄 开始并行优化...")
    print(f"{'进度':<8} {'参数组合':<35} {'收益率':<12} {'交易次数':<12} {'状态'}")
    print("-" * 80)
    
    # 执行并行计算
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_task = {executor.submit(run_backtest_parallel, task): task for task in tasks}
        
        # 收集结果
        for future in as_completed(future_to_task):
            try:
                index, combo, total_profit, num_long, num_short, success, error_msg = future.result()
                completed_count += 1
                
                # 显示进度
                progress = f"{completed_count}/{total_combinations}"
                param_str = f"F{combo['macd_Fast']}-S{combo['macd_Slow']}-Si{combo['macd_Signal']}"
                
                if success and total_profit is not None:
                    profit_str = f"{total_profit:.2f}%"
                    trades_str = f"多{num_long}/空{num_short}"
                    status = "✅"
                    
                    results.append({
                        **combo, 
                        'total_profit': total_profit, 
                        'num_long': num_long, 
                        'num_short': num_short,
                        'total_trades': num_long + num_short
                    })
                else:
                    profit_str = "N/A"
                    trades_str = "N/A"
                    status = "❌"
                    failed_count += 1
                    if debug and error_msg:
                        print(f"[DEBUG] 失败原因: {error_msg}")
                
                print(f"{progress:<8} {param_str:<35} {profit_str:<12} {trades_str:<12} {status}")
                
            except Exception as e:
                failed_count += 1
                if debug:
                    print(f"[DEBUG] 任务执行异常: {e}")
    
    end_time = time.time()
    elapsed_time = end_time - start_time
    
    # 结果统计
    print("\n" + "=" * 60)
    print("📊 优化完成统计")
    print("=" * 60)
    print(f"⏱️  总耗时: {elapsed_time:.2f} 秒")
    print(f"✅ 成功执行: {len(results)} 个组合")
    print(f"❌ 失败执行: {failed_count} 个组合")
    print(f"⚡ 平均速度: {total_combinations/elapsed_time:.2f} 组合/秒")
    
    if results:
        # 创建结果DataFrame并排序
        results_df = pd.DataFrame(results)
        results_df_sorted = results_df.sort_values(by='total_profit', ascending=False).reset_index(drop=True)
        
        # 显示最佳结果
        print(f"\n🏆 最佳参数组合:")
        best_result = results_df_sorted.iloc[0]
        print(f"   macd_Fast: {int(best_result['macd_Fast'])}")
        print(f"   macd_Slow: {int(best_result['macd_Slow'])}")
        print(f"   macd_Signal: {int(best_result['macd_Signal'])}")
        print(f"   zhiyinPre: {best_result['zhiyinPre']}")
        print(f"   zhisunPre: {best_result['zhisunPre']}")
        print(f"   总收益: {best_result['total_profit']:.4f}%")
        print(f"   交易次数: 做多{int(best_result['num_long'])}次, 做空{int(best_result['num_short'])}次")
        
        # 显示前10名
        print(f"\n📈 前10名结果:")
        top_10 = results_df_sorted.head(10)
        for i, (_, row) in enumerate(top_10.iterrows()):
            print(f"   {i+1:2d}. F{int(row['macd_Fast'])}-S{int(row['macd_Slow'])}-Si{int(row['macd_Signal'])} "
                  f"止盈{row['zhiyinPre']} 止损{row['zhisunPre']} "
                  f"收益{row['total_profit']:.3f}% 交易{int(row['total_trades'])}次")
        
        # 保存结果
        if save_results:
            filename = f"parallel_optimization_results_{optimization_level}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            results_df_sorted.to_excel(filename, index=False)
            print(f"\n💾 结果已保存到: {filename}")
        
        return results_df_sorted
    else:
        print("\n❌ 没有成功的优化结果")
        return pd.DataFrame()


# ==============================================================================
# 主程序入口
# ==============================================================================

if __name__ == "__main__":
    # 基础参数配置
    base_params = {
        "rsi_length": 8, 
        "max_lookback_kline_count": 140, 
        "my_sma_length": 5,
        "my_ema_length": 5, 
        "my_gaussian_sigma": 1.9, 
        "windowsTime": 40,
        "Uname": "doge", 
        "Ktime": "15m", 
        "strTime": "2025-05-01", 
        "endTime": "2025-05-04"
    }
    
    # 选择优化模式
    print("请选择优化模式:")
    print("1. 快速测试 (~100个组合, 1-2分钟)")
    print("2. 中等测试 (~500个组合, 5-10分钟)")
    print("3. 全面测试 (~2000个组合, 20-30分钟)")
    print("4. 深度测试 (~5000个组合, 1小时+)")
    print("5. 自定义参数网格")
    
    try:
        choice = input("请输入选择 (1-5): ").strip()
    except:
        choice = "2"  # 默认选择
    
    if choice == "1":
        optimization_level = "quick"
        param_grid = None
    elif choice == "2":
        optimization_level = "moderate"
        param_grid = None
    elif choice == "3":
        optimization_level = "comprehensive"
        param_grid = None
    elif choice == "4":
        optimization_level = "intensive"
        param_grid = None
    elif choice == "5":
        # 使用您原来的自定义参数网格
        optimization_level = "custom"
        param_grid = {
            "macd_Fast": [3, 5, 7, 9, 12],
            "macd_Slow": [14, 18, 22, 26, 28, 32, 38],
            "macd_Signal": [5,  7,  9, 10,  15],
            "zhiyinPre": [1.2 , 2.0, 3.0],
            "zhisunPre": [0.2],
            "max_lookback_kline_count": [100,  140,  180],
        }
    else:
        print("无效选择，使用中等测试模式")
        optimization_level = "moderate"
        param_grid = None
    
    # 执行并行优化
    results_df = run_parallel_optimization(
        base_params=base_params,
        param_grid=param_grid,
        max_workers=6,  # 您要求的最大并行数
        debug=False,    # 可以改为True启用详细调试
        save_results=True,
        optimization_level=optimization_level
    )
    
    print(f"\n🎉 优化完成！共获得 {len(results_df)} 个有效结果。")