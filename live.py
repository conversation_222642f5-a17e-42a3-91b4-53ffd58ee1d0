# -*- coding: utf-8 -*-

import ccxt
import pandas as pd
import time
from datetime import datetime, timezone, timedelta
import sys
# 假设您的okx_sdkV1文件与此脚本在同一目录下
from ku import *
from config import  calculate_kline_needed,get_kline_data,add_rsi_to_kline_data


from tradeing import check_trading_single ,judge_signal,calc_price,batch_judge_trade

from okx_sdkV1 import get_kline_data, OKXClient 
from emailHl import send_email_with_attachment_enhanced

# ==============================================================================
# 0. 时区配置
# ==============================================================================
# 定义北京时区 (UTC+8)
BEIJING_TZ = timezone(timedelta(hours=8))


# ==============================================================================
# 1. 参数配置 (Separation of Concerns: Configuration)
# ==============================================================================
# 所有策略和程序的配置都集中在这里
my_pram = {
    # --- 技术指标参数 ---
    "rsi_length": 8,
    "macd_length": 26, # 通常指MACD中的慢速EMA周期 (slow)
    "my_sma_length": 5,
    "my_ema_length": 5,
    "my_gaussian_sigma": 1.39,
    
    # --- 策略与回测参数 ---
    "max_lookback_kline_count": 485, # 保证指标计算有足够数据量的最小K线数量
    "zhiyinPre": 2,
    "zhisunPre": 1,
    
    # --- 实时运行参数 ---
    "windowsTime": 12,
    "Uname": "doge", # 交易币种名称
    "Ktime": "1m",  # K线的时间周期 ('1m', '5m', '15m', '30m', '1h', '4h', '1d')
    
    # --- 历史数据参数 (主要用于回测, 实时模式下不直接使用) ---
    "strTime": "2024-03-01",
    "endTime": "2024-04-07"
}


# ==============================================================================
# 2. 核心功能函数 (Separation of Concerns: Business Logic)
# ==============================================================================


def parse_interval_to_seconds(interval_str: str) -> int:
    """
    将交易所标准的时间周期字符串（如 '1m', '30m', '1h'）转换为秒。
    """
    unit = interval_str[-1].lower()
    value = int(interval_str[:-1])
    if unit == 'm':
        return value * 60
    elif unit == 'h':
        return value * 60 * 60
    elif unit == 'd':
        return value * 24 * 60 * 60
    else:
        raise ValueError(f"Unsupported interval format: {interval_str}")


def get_next_trigger_timestamp(interval_str: str) -> int:
    """
    计算下一个K线收盘时刻的Unix时间戳（以秒为单位）。
    """
    interval_seconds = parse_interval_to_seconds(interval_str)
    now_ts = int(time.time())
    current_bucket_start_ts = (now_ts // interval_seconds) * interval_seconds
    next_trigger_ts = current_bucket_start_ts + interval_seconds
    return next_trigger_ts


# ==============================================================================
# 3. 主执行函数 (Main Execution Block)
# ==============================================================================

def main():
    """
    主执行函数，模拟C/C++中的 `int main()`。
    此函数包含一个无限循环，用于实时、周期性地获取K线数据。
    """
    print("=" * 60)
    print("      实时K线数据获取程序 - 初始化...")
    print("=" * 60)
    
    params = my_pram
    symbol = params["Uname"].upper() + "-USDT" # OKX 交易对格式通常是 XXX-USDT
    interval = params["Ktime"]

    print(f"配置信息:")
    print(f"  - 交易对: {symbol}")
    print(f"  - K线周期: {interval}")
    print("-" * 60)
    
    print("=== OKX交易所SDK测试 ===\n")
    try:
        # 注意: 您的 OKXClient 和 get_kline_data 需要能被正常导入
        client = OKXClient()
    except Exception as e:
        print(f"初始化OKX客户端失败: {e}")
        return

    # 进入主循环，实现实时获取
    while True:
        try:
            # 1. 判断并等待下一个K线周期的到达时间
            next_ts = get_next_trigger_timestamp(interval)
            
            # --- 时间显示转换 ---
            # 获取当前的UTC时间，并立即转换为北京时间用于显示
            now_dt_beijing = datetime.now(timezone.utc).astimezone(BEIJING_TZ)
            # 将计算出的下一个触发时间戳转换为北京时间用于显示
            next_dt_beijing = datetime.fromtimestamp(next_ts, tz=timezone.utc).astimezone(BEIJING_TZ)

            # 使用UTC时间计算休眠秒数，以保证精度
            sleep_duration = next_ts - time.time()

            if sleep_duration > 0:
                print(f"当前北京时间: {now_dt_beijing.strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"等待下一个K线周期收盘...")
                print(f"下一个任务将在 {next_dt_beijing.strftime('%Y-%m-%d %H:%M:%S')} (北京时间) 触发. 休眠 {sleep_duration:.2f} 秒.")
                time.sleep(sleep_duration)

  
            time.sleep(3) 
            
            # --- 时间显示转换 ---
            triggered_time_beijing = datetime.now(timezone.utc).astimezone(BEIJING_TZ)
            print(f"\n--- 时间到达: {triggered_time_beijing.strftime('%Y-%m-%d %H:%M:%S')} (北京时间) ---")

            # 2. 计算所需获取的K线数量
            needed_klines = calculate_kline_needed(params)
            print(f"计算得出需要获取的K线数量: {needed_klines}")

            kline_df = get_kline_data(client, symbol, interval, count= needed_klines)


            if kline_df is not None and not kline_df.empty:
                print(f"成功获取 {len(kline_df)} 条 {symbol} 的K线数据。")
                print("最新一条K线数据:")
                print(kline_df.tail(1))
                close_values = kline_df['close'].values  # 预提取，避免重复调用
                nowPrice = close_values[- 1]  # 使用预提取的数据
                window_close = close_values[:-1]  # 使用预提取的数据       
                window_rsi = fast_rsi_calculation(window_close, params["rsi_length"])
                valid_rsi = window_rsi[~np.isnan(window_rsi)]
                if len(valid_rsi) > 0:
                    smooth_rsi_window = fast_gaussian_smooth(valid_rsi, params["my_gaussian_sigma"])
                else:
                    continue
                stats = analyze_numerical_series( smooth_rsi_window [-params["max_lookback_kline_count"]:])
                
                a=pd.Series(close_values)
                b=pd.Series(smooth_rsi_window)
                 ##计算超空做多
                if smooth_rsi_window[-1]<=stats['conf_interval_2sigma'][0]:
                    print('超空做多信号',smooth_rsi_window[-1])
                        #     # --------- 双轴图组合（RSI+价格） ----------
                    
                   
                    generate_dual_axis_plot(
                        y1_values=a,
                        y2_values=b,
                        title=f"DOGEUSDT 15m (close_values& smooth_rsi_window)",
                        y1_label=f"RSI (周期: {params['rsi_length']})",
                        y2_label="DOGEUSDT PRice",
                        x_label="KLive",
                        filename=f"{params['Uname']}-{params['Ktime']}.png",
                        y1_color='tab:blue',
                        y2_color='tab:red'
                    )
                    send_email_with_attachment_enhanced(
                        list_a=window_rsi[-10:],
                        list_b=smooth_rsi_window[-10:],
                        image_path=f"{params['Uname']}-{params['Ktime']}.png",
                        compress_image_flag=False,  # 不压缩
                        use_zip=False,              # 不打包ZIP
                        max_image_size_mb=50.0      # 使用QQ邮箱最大限制
                        )
                    # print(f'{params["rsi_length"]}长度的rsi值和gau值：')
                          
                ##计算超多做空  
                elif smooth_rsi_window[-1]>= stats['conf_interval_2sigma'][1]:
                    print('超多做空信号',smooth_rsi_window[-1])
                    
                   
                    generate_dual_axis_plot(
                        y1_values=a,
                        y2_values=b,
                        title=f"DOGEUSDT 15m (close_values& smooth_rsi_window)",
                        y1_label=f"RSI (周期: {params['rsi_length']})",
                        y2_label="DOGEUSDT 价格",
                        x_label="K线索引",
                        filename=f"{params['Uname']}-{params['Ktime']}.png",
                        y1_color='tab:blue',
                        y2_color='tab:red' )
                    
                    send_email_with_attachment_enhanced(
                            list_a=window_rsi[-10:],
                            list_b=smooth_rsi_window[-10:],
                            image_path=f"{params['Uname']}-{params['Ktime']}.png",
                            compress_image_flag=False,  # 不压缩
                            use_zip=False,              # 不打包ZIP
                            max_image_size_mb=50.0      # 使用QQ邮箱最大限制
                        )
                    # print(f'{params["rsi_length"]}长度的rsi值和gau值：')
                else:
                    print('震荡',smooth_rsi_window[-1],stats['conf_interval_2sigma'])  
                    
                   
                    generate_dual_axis_plot(
                        y1_values=a,
                        y2_values=b,
                        title=f"DOGEUSDT 15m (close_values& smooth_rsi_window)",
                        y1_label=f"RSI (周期: {params['rsi_length']})",
                        y2_label="DOGEUSDT 价格",
                        x_label="K线索引",
                        filename=f"{params['Uname']}-{params['Ktime']}.png",
                        y1_color='tab:blue',
                        y2_color='tab:red' )
                    send_email_with_attachment_enhanced(
                            list_a=window_rsi[-10:],
                            list_b=smooth_rsi_window[-10:],
                            image_path=f"{params['Uname']}-{params['Ktime']}.png",
                            compress_image_flag=False,  # 不压缩
                            use_zip=False,              # 不打包ZIP
                            max_image_size_mb=50.0      # 使用QQ邮箱最大限制
                        )
            
                
            else:
                print("本周期内未能获取K线数据。将在下一个周期重试。")

            print("--- 本轮周期结束, 等待下一轮 ---")
            print("-" * 60 + "\n")

        except KeyboardInterrupt:
            print("\n程序被用户中断 (Ctrl+C)。正在退出...")
            sys.exit(0)
        except Exception as e:
            print(f"\n主循环发生未知错误: {e}", file=sys.stderr)
            print("程序将在10秒后尝试恢复...", file=sys.stderr)
            time.sleep(10)


if __name__ == "__main__":
    main()