import pandas as pd
import numpy as np



def judge_signal(signal_str1,signal_str2):
    mapping = {
        "decline_pullback_continuation": -1,
        "breakout_pullback_continuation": 1,     
    }
    if signal_str1 >= 0.90:
        # print(signal_str1, signal_str2)
        if signal_str2 =='breakout_pullback_continuation':
            return 1
        elif signal_str2 =='decline_pullback_continuation':
            return -1
        else:
            return 0
    return 0

def check_trading_single(tradingSingle, nowSingle):
    """
    判断tradingSingle与nowSingle的关系并返回结果。
    返回：(tra, now)
    
    逻辑：
    1. 如果 tradingSingle == nowSingle 且 nowSingle != 0，,令nowSingle=0 返回 (tradingSingle, nowSingle)
    2. 如果 tradingSingle != nowSingle 且 nowSingle == 0，返回 (tradingSingle, nowSingle)
    3. 如果 tradingSingle != nowSingle 且 nowSingle != 0，令 tradingSingle = nowSingle，返回 (tradingSingle, nowSingle)
    """
    if tradingSingle == nowSingle:
        if nowSingle != 0:
            nowSingle=0
            return tradingSingle, nowSingle
    else:
        if nowSingle == 0:
            return tradingSingle, nowSingle
        else:
            tradingSingle = nowSingle
            return tradingSingle, nowSingle
    # 默认返回
    return tradingSingle, nowSingle

def calc_price(single, price, pre):
    """
    1. 如果single==0，返回False
    2. 如果pre==0，返回price
    3. 否则返回 price*(single*pre+1)
    """
    if single == 0:
        return False
    if pre == 0:
        return price
    return price * (1-single * pre )
def validate_trade_result(row_kf, take_profit=2.0, stop_loss=1.0, window=8, ktime='15m'):
    """
    针对单个起点的窗口判断，row_kf为起点（含）及其后window-1行的DataFrame。
    根据nowSingle字段判断做多还是做空。
    此函数返回 (结果字符串, 达成百分比, 成交价格)。
    """
    buy_price = row_kf.iloc[0]['buyPrice']
    now_single = row_kf.iloc[0]['nowSingle']
    
    # 确保 now_single 是数值，并处理 NaN
    now_single = pd.to_numeric(now_single, errors='coerce')

    # buyPrice非法或nowSingle非法直接返回空
    if pd.isna(buy_price) or buy_price == 0 or pd.isna(now_single) or now_single not in [1, -1]:
        return '', np.nan, np.nan

    # 循环时跳过开仓行（row_kf.iloc[0]），因为止盈止损发生在开仓之后
    # from_idx_in_slice = 1 表示从开仓后的第一根K线开始判断
    # 这里的 `window` 参数在您的 `validate_trade_result` 中没有被直接用于循环范围，
    # 而是 `len(row_kf)`。我保留这种逻辑。
    
    if now_single == 1:  # 做多
        tp_price = buy_price * (1 + take_profit / 100)
        sl_price = buy_price * (1 - stop_loss / 100)
        
        for i in range(1, len(row_kf)): # 从第二根K线开始检查止盈止损
            row = row_kf.iloc[i]
            
            # 确保高低价有效，避免NaN导致判断错误
            if pd.isna(row['high']) or pd.isna(row['low']):
                continue

            if row['high'] >= tp_price:
                return '止盈', take_profit, tp_price
            if row['low'] <= sl_price:
                return '止损', -stop_loss, sl_price
        
        # 如果在窗口期内没有止盈止损，则以窗口最后一根K线的收盘价平仓
        close_price = row_kf.iloc[-1]['close']
        if pd.isna(close_price): # 如果最后一根K线的收盘价无效，则无法平仓
             return '', np.nan, np.nan 
        close_pct = round((close_price - buy_price) / buy_price * 100, 4)
        return '平仓', close_pct, close_price

    if now_single == -1:  # 做空
        tp_price = buy_price * (1 - take_profit / 100) # 空头止盈是价格下跌
        sl_price = buy_price * (1 + stop_loss / 100)   # 空头止损是价格上涨
        
        for i in range(1, len(row_kf)): # 从第二根K线开始检查止盈止损
            row = row_kf.iloc[i]
            
            if pd.isna(row['high']) or pd.isna(row['low']):
                continue

            if row['low'] <= tp_price:
                return '止盈', take_profit, tp_price
            if row['high'] >= sl_price:
                return '止损', -stop_loss, sl_price
        
        # 如果在窗口期内没有止盈止损，则以窗口最后一根K线的收盘价平仓
        close_price = row_kf.iloc[-1]['close']
        if pd.isna(close_price): # 如果最后一根K线的收盘价无效，则无法平仓
            return '', np.nan, np.nan 
        close_pct = round((buy_price - close_price) / buy_price * 100, 4)
        return '平仓', close_pct, close_price

    # 其他情况 (buyPrice非法或nowSingle非法已在函数开头处理)
    return '', np.nan, np.nan

# --- 完善后的 batch_judge_trade 函数 ---
def batch_judge_trade(kf: pd.DataFrame, take_profit: float = 2.0, stop_loss: float = 1.0, window: int = 8, ktime: str = '15m') -> pd.DataFrame:
    """
    对kf每一行的buyPrice和nowSingle做止盈/止损/平仓判断，新增3个中文字段。
    并在回测数据结束时处理未平仓头寸。
    '交易结果' 字段填充为**字符串**（止盈/止损/平仓/强制平仓）。
    """
    kf_copy = kf.copy() # 操作副本，避免修改原始DataFrame
    kf_copy['交易结果'] = '' # 默认值为空字符串
    kf_copy['达成百分比'] = np.nan
    kf_copy['成交价格'] = np.nan

    # 存储最后一次开仓但尚未在窗口期内被平仓的信息：(开仓行的索引, nowSingle, buyPrice)
    # 这用于在循环结束后处理强制平仓
    last_open_trade_info = None

    for idx in range(len(kf_copy)):
        current_row = kf_copy.iloc[idx]
        
        # 确保 `nowSingle` 是数值类型，避免比较错误
        current_now_single = pd.to_numeric(current_row['nowSingle'], errors='coerce')
        if pd.isna(current_now_single): current_now_single = 0 # 将无法转换的视为0

        # 如果当前行有开仓信号 (`nowSingle` 为 1 或 -1)
        if current_now_single in [1, -1]:
            # 这是一个潜在的开仓点。
            # 更新 last_open_trade_info，因为这是最新的开仓信号
            last_open_trade_info = (idx, current_now_single, current_row['buyPrice'])
            
            # 检查是否有足够的未来K线来形成判断窗口
            # 这里的 `validate_trade_result` 内部循环会从 slice 的第 1 个元素开始判断
            # 所以 `slice_for_validation` 至少需要有 2 根K线 (开仓K线 + 至少1根未来K线)
            # 加上 `window` 指的是从当前K线往后看 `window-1` 根。
            # 所以 slice 长度为 `window`。
            if idx + window > len(kf_copy):
                # 剩余K线不足窗口期，这笔开仓将被视为未平仓，在循环结束后处理
                # print(f"DEBUG: Index {idx}, not enough data for window, breaking.")
                break # 退出循环，转到最终处理

            # 获取从当前开仓点到未来窗口的K线数据切片
            slice_for_validation = kf_copy.iloc[idx:idx+window]
            
            # 调用 `validate_trade_result` 判断这笔交易的结果
            result_str, pct, price = validate_trade_result(
                slice_for_validation, take_profit, stop_loss, window, ktime
            )
            
            # 如果 `validate_trade_result` 返回了有效的平仓结果字符串（非空）
            if result_str != '':
                kf_copy.at[idx, '交易结果'] = result_str
                kf_copy.at[idx, '达成百分比'] = pct
                kf_copy.at[idx, '成交价格'] = price
                # 这笔交易已经平仓，清除 last_open_trade_info，因为它不再是未平仓状态
                last_open_trade_info = None
        
    # --- 在主循环结束后处理回测数据末端可能存在的未平仓头寸 ---
    if last_open_trade_info is not None:
        open_idx, open_signal, open_buy_price = last_open_trade_info
        
        # 再次检查开仓信息是否有效
        if pd.isna(open_buy_price) or open_buy_price == 0:
            print(f"警告：无法强制平仓索引 {open_idx} 处的头寸，因为买入价无效。")
        else:
            last_kline_close = kf_copy.iloc[-1]['close'] # 获取最后一根K线的收盘价
            
            if pd.isna(last_kline_close):
                print(f"警告：无法强制平仓索引 {open_idx} 处的头寸，因为最后收盘价无效。")
            else:
                # 计算这笔强制平仓的达成百分比
                final_pct_relative_to_buy_price = np.nan
                if open_buy_price != 0:
                    if open_signal == 1: # 多头
                        final_pct_relative_to_buy_price = (last_kline_close - open_buy_price) / open_buy_price * 100
                    elif open_signal == -1: # 空头
                        final_pct_relative_to_buy_price = (open_buy_price - last_kline_close) / open_buy_price * 100
                
                # 确定结果字符串
                result_str = '强制平仓'
                if pd.notna(final_pct_relative_to_buy_price):
                    if final_pct_relative_to_buy_price > 0: result_str = '强制止盈'
                    elif final_pct_relative_to_buy_price < 0: result_str = '强制止损'

                # 更新该开仓行的 '交易结果', '达成百分比', '成交价格'
                kf_copy.at[open_idx, '交易结果'] = result_str
                kf_copy.at[open_idx, '达成百分比'] = final_pct_relative_to_buy_price
                kf_copy.at[open_idx, '成交价格'] = last_kline_close
                print(f"--- 强制平仓：索引 {open_idx} 处的未平仓头寸已在 {last_kline_close:.2f} 平仓 (结果: {result_str}, 百分比: {final_pct_relative_to_buy_price:.2f}%) ---")
    
    return kf_copy
