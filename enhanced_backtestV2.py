# optimization.py

import pandas as pd
from bayes_opt import BayesianOptimization
# 尝试导入JSONLogger和Events，兼容新旧版本库
try:
    from bayes_opt.logger import JSONLogger
    from bayes_opt.event import Events
    BAYES_OPT_HAS_LOGGER_EVENTS = True
except ImportError:
    BAYES_OPT_HAS_LOGGER_EVENTS = False
import numpy as np
import os
import sys

# 假设 huice.py 在当前目录，我们将其目录添加到 Python 路径中
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from huice import (
    calculate_kline_needed,
    get_local_kline_data,
    sub_kline_time,
    fast_rsi_calculation,
    fast_gaussian_smooth,
    TrendPullbackDetector,
    judge_signal,
    check_trading_single,
    calc_price,
    batch_judge_trade
)

# 强制 numpy.NaN 补丁，确保 pandas_ta 正常工作
if not hasattr(np, 'NaN'):
    np.NaN = np.nan
import pandas_ta as ta

def run_backtest(
    rsi_length,
    my_gaussian_sigma,
    zhiyinPre,
    zhisunPre,
    windowsTime,
    lookback_period,
    rsi_threshold_bull,
    rsi_threshold_bear,
    price_retracement_pct,
    min_trend_strength,
    smoothing_factor
):
    """
    封装 huice.py 中的回测逻辑，并计算总盈亏。
    
    这个函数将作为贝叶斯优化的目标函数。
    
    参数:
        rsi_length (int): RSI 计算周期。
        my_gaussian_sigma (float): RSI 高斯平滑的标准差。
        zhiyinPre (float): 止盈百分比。
        zhisunPre (float): 止损百分比。
        windowsTime (int): 持仓时间窗口（K线数）。
        lookback_period (int): 趋势检测回看周期。
        rsi_threshold_bull (float): 牛市RSI阈值。
        rsi_threshold_bear (float): 熊市RSI阈值。
        price_retracement_pct (float): 价格回撤百分比。
        min_trend_strength (float): 最小趋势强度。
        smoothing_factor (float): 平滑因子。
        
    返回:
        float: 回测的总盈亏，作为优化目标。
    """
    
    # 将浮点参数转换为整数，因为某些参数需要是整数
    rsi_length = int(round(rsi_length))
    windowsTime = int(round(windowsTime))
    lookback_period = int(round(lookback_period))
    rsi_threshold_bull = int(round(rsi_threshold_bull))
    rsi_threshold_bear = int(round(rsi_threshold_bear))
    
    print("-" * 50)
    print(f"--- 正在测试参数组合: ---")
    print(f"rsi_length: {rsi_length}, my_gaussian_sigma: {my_gaussian_sigma:.2f}")
    print(f"zhiyinPre: {zhiyinPre:.2f}, zhisunPre: {zhisunPre:.2f}, windowsTime: {windowsTime}")
    print(f"lookback_period: {lookback_period}, rsi_threshold_bull: {rsi_threshold_bull}, rsi_threshold_bear: {rsi_threshold_bear}")
    print(f"price_retracement_pct: {price_retracement_pct:.3f}, min_trend_strength: {min_trend_strength:.2f}, smoothing_factor: {smoothing_factor:.2f}")
    print("-" * 50)

    # 1. 策略参数字典
    my_pram = {
        "rsi_length": rsi_length,
        "macd_length": 26, # 假设这个参数是固定的
        "max_lookback_kline_count": 100, # 假设这个参数是固定的
        "my_sma_length": 5, # 假设这个参数是固定的
        "my_ema_length": 5, # 假设这个参数是固定的
        "my_gaussian_sigma": my_gaussian_sigma,
        "zhiyinPre": zhiyinPre,
        "zhisunPre": zhisunPre,
        "windowsTime": windowsTime
    }
    
    # 2. 回测数据和时间范围
    Uname = 'doge'
    Ktime = '15m'
    strTime_end = '2024-3-4'
    strTime_start = '2024-2-1'

    # 3. 计算所需K线数量并获取数据
    needed_klines = calculate_kline_needed(my_pram)
    start_time_adjusted = sub_kline_time(strTime_start, needed_klines, Ktime)

    try:
        kline_df = get_local_kline_data(Uname, Ktime, start_time_adjusted, strTime_end)
    except Exception as e:
        print(f"获取本地K线数据失败: {e}. 返回-1以惩罚此参数组合。")
        return -1.0 # 如果数据获取失败，返回一个较差的分数

    if kline_df.empty:
        print("获取的K线数据为空。返回-1。")
        return -1.0

    # 4. 初始化DataFrame
    kline_df_with_rsi = kline_df.copy()
    kline_df_with_rsi['RSI'] = np.nan
    kline_df_with_rsi['RSI_Gaussian_2.0'] = np.nan
    kline_df_with_rsi['投资建议'] = None
    kline_df_with_rsi['tradingSingle'] = 0
    kline_df_with_rsi['nowSingle'] = 0
    kline_df_with_rsi['buyPrice'] = np.nan
    kline_df_with_rsi['识别模式'] = None
    kline_df_with_rsi['置信度'] = None
    kline_df_with_rsi['建议'] = None
    kline_df_with_rsi['价格趋势'] = None
    kline_df_with_rsi['当前RSI'] = np.nan
    kline_df_with_rsi['total_pnl'] = 0.0 # 新增一列来记录盈亏

    # 5. 趋势检测器 V2
    detector = TrendPullbackDetector(
        lookback_period=lookback_period,
        rsi_threshold_bull=rsi_threshold_bull,
        rsi_threshold_bear=rsi_threshold_bear,
        price_retracement_pct=price_retracement_pct,
        min_trend_strength=min_trend_strength,
        smoothing_factor=smoothing_factor
    )

    # 6. 逐段回测
    tradingSingle = 0
    close_values = kline_df_with_rsi['close'].values
    total_loops = len(kline_df_with_rsi) - needed_klines + 1

    for count in range(total_loops):
        nowPrice = close_values[count + needed_klines - 1]
        window_close = close_values[count : count + needed_klines]
        
        # 快速计算 RSI 和高斯平滑
        window_rsi = fast_rsi_calculation(window_close, my_pram["rsi_length"])
        valid_rsi = window_rsi[~np.isnan(window_rsi)]
        
        if len(valid_rsi) > 0:
            smooth_rsi_window = fast_gaussian_smooth(valid_rsi, my_pram["my_gaussian_sigma"])
        else:
            continue
        
        # 截取用于趋势检测器的窗口数据
        window_close_for_detector = window_close[-lookback_period:]
        window_rsi_for_detector = window_rsi[-lookback_period:]
        smooth_rsi_window_for_detector = smooth_rsi_window[-lookback_period:]

        # 检测模式
        result = detector.detect_pattern(
            window_close_for_detector.tolist(), 
            window_rsi_for_detector.tolist(), 
            smooth_rsi_window_for_detector.tolist(), 
            Ktime
        )
        
        # 模拟交易信号
        nowSingle = judge_signal(round(result['confidence'], 2), result['pattern'])
        tradingSingle, nowSingle = check_trading_single(tradingSingle, nowSingle)
        
        # 写入结果到下一根K线
        next_index = count + needed_klines
        if next_index < len(kline_df_with_rsi):
            kline_df_with_rsi.at[next_index, 'tradingSingle'] = tradingSingle
            kline_df_with_rsi.at[next_index, 'nowSingle'] = nowSingle
            if nowSingle != 0:
                kline_df_with_rsi.at[next_index, 'buyPrice'] = calc_price(nowSingle, nowPrice, 0)
                
            # 记录分析结果
            kline_df_with_rsi.at[next_index, '识别模式'] = result['pattern']
            kline_df_with_rsi.at[next_index, '置信度'] = result['confidence']
            kline_df_with_rsi.at[next_index, '建议'] = result['recommendation']
            kline_df_with_rsi.at[next_index, '价格趋势'] = result['price_features']['trend_type']
            kline_df_with_rsi.at[next_index, '当前RSI'] = result['rsi_features']['current_rsi']

    # 7. 批量判断交易结果并计算盈亏
    kline_df_with_pnl = batch_judge_trade(
        kline_df_with_rsi,
        my_pram['zhiyinPre'],
        my_pram['zhisunPre'],
        my_pram['windowsTime'],
        Ktime
    )

    # 8. 改进后的盈亏计算逻辑：优先使用 '达成百分百' 列
    total_pnl = 0.0
    
    # --- 核心改进逻辑开始 ---
    if '达成百分百' in kline_df_with_pnl.columns:
        pnl_series = kline_df_with_pnl['达成百分百']
        # 尝试将该列转换为数字，'coerce' 会将无法转换的值设为 NaN
        numeric_pnl = pd.to_numeric(pnl_series, errors='coerce')
        # 累加所有有效数字
        total_pnl = numeric_pnl.sum()

        if numeric_pnl.isnull().any() and not pnl_series.isnull().all():
            print("警告: '达成百分百'列中存在无法转换为数字的值，这些值已被忽略。")
            
    elif '交易结果' in kline_df_with_pnl.columns:
        # 如果没有 '达成百分百'，则回退到检查 '交易结果' 列
        pnl_series = kline_df_with_pnl['交易结果']
        numeric_pnl = pd.to_numeric(pnl_series, errors='coerce')
        total_pnl = numeric_pnl.sum()
        
        if numeric_pnl.isnull().all() and not pnl_series.isnull().all():
            print("警告: 无法将'交易结果'列转换为数字。总盈亏将计算为0。")
            total_pnl = 0.0
            
    else:
        print("警告: 既找不到'达成百分百'列，也找不到'交易结果'列。总盈亏将计算为0。请检查 batch_judge_trade 函数的实现。")
        total_pnl = 0.0
    # --- 核心改进逻辑结束 ---

    # 格式化输出，现在 total_pnl 保证是数字了
    print(f"回测完成，总盈亏: {total_pnl:.4f}%")
    return total_pnl

# --- (保持 if __name__ == "__main__": 块不变) ---
if __name__ == "__main__":
    # 定义优化参数的搜索范围
    pbounds = {
        # 策略参数
        'rsi_length': (10, 25),            # 整数范围
        'my_gaussian_sigma': (0.5, 3.0),   # 浮点数范围
        'zhiyinPre': (0.5, 5.0),           # 浮点数范围
        'zhisunPre': (0.5, 3.0),           # 浮点数范围
        'windowsTime': (10, 50),           # 整数范围
        # 趋势检测器参数
        'lookback_period': (10, 100),       # 整数范围
        'rsi_threshold_bull': (40, 60),    # 整数范围
        'rsi_threshold_bear': (40, 60),    # 整数范围
        'price_retracement_pct': (0.2, 0.5), # 浮点数范围
        'min_trend_strength': (0.5, 0.9),  # 浮点数范围
        'smoothing_factor': (0.6, 0.95),   # 浮点数范围
    }

    # 初始化贝叶斯优化器
    # f(run_backtest) 是我们的目标函数
    # pbounds 是参数空间
    # random_state 用于确保每次运行结果可复现
    optimizer = BayesianOptimization(
        f=run_backtest,
        pbounds=pbounds,
        random_state=1,
    )
    
    # 设置日志记录器，可以将每次迭代的结果保存到文件
    log_file = "optimization_results.json"
    if os.path.exists(log_file):
        os.remove(log_file) # 每次开始前删除旧日志
    
    # 兼容新旧版本库的日志功能
    if BAYES_OPT_HAS_LOGGER_EVENTS:
        logger = JSONLogger(path=log_file)
        optimizer.subscribe(Events.OPTIMIZATION_STEP, logger)
    else:
        print("警告: 无法导入 bayes_opt.logger 或 bayes_opt.event，可能需要降级 bayesian-optimization 库。日志功能将无法使用。")
        print("请尝试 'pip install bayesian-optimization==2.0.4' 来解决此问题。")


    # 运行优化
    # n_iter: 迭代次数
    # init_points: 初始随机探索的次数
    total_iterations = 200
    initial_points = 20
    print(f"开始贝叶斯优化，总迭代次数: {total_iterations}，初始随机探索点: {initial_points}")
    
    # 检查是否有足够的K线数据来运行至少一次回测
    needed_klines_for_init = calculate_kline_needed({
        "rsi_length": pbounds['rsi_length'][1],
        "macd_length": 26,
        "max_lookback_kline_count": 100,
        "my_sma_length": 5,
        "my_ema_length": 5,
        "my_gaussian_sigma": pbounds['my_gaussian_sigma'][1],
        "zhiyinPre": pbounds['zhiyinPre'][1],
        "zhisunPre": pbounds['zhisunPre'][1],
        "windowsTime": pbounds['windowsTime'][1],
    })
    
    try:
        # 尝试获取一次数据以确保连接和文件存在
        _ = get_local_kline_data('doge', '15m', sub_kline_time('2024-2-1', needed_klines_for_init, '15m'), '2024-3-4')
    except Exception as e:
        print(f"无法获取初始数据，请检查 get_local_kline_data 函数和数据源。错误：{e}")
        exit()

    try:
        optimizer.maximize(
            init_points=initial_points,
            n_iter=total_iterations - initial_points,
        )
    except KeyboardInterrupt:
        print("\n优化过程被用户中断。")

    # 打印最佳参数组合
    print("\n" + "=" * 60)
    print("贝叶斯优化完成。")
    print("最佳参数组合:")
    best_params = optimizer.max['params']
    
    # 将浮点数参数转换回整数
    best_params['rsi_length'] = int(round(best_params['rsi_length']))
    best_params['windowsTime'] = int(round(best_params['windowsTime']))
    best_params['lookback_period'] = int(round(best_params['lookback_period']))
    best_params['rsi_threshold_bull'] = int(round(best_params['rsi_threshold_bull']))
    best_params['rsi_threshold_bear'] = int(round(best_params['rsi_threshold_bear']))

    print(f"最大化总盈亏: {optimizer.max['target']:.4f}%")
    print("参数:")
    for param, value in best_params.items():
        if isinstance(value, float):
            print(f"  {param}: {value:.4f}")
        else:
            print(f"  {param}: {value}")
    print("=" * 60)

    # 运行一次最佳参数组合的回测，并保存结果到 Excel
    print("\n正在使用最佳参数组合进行最后一次回测并保存结果...")
    final_params_dict = {
        "rsi_length": best_params['rsi_length'],
        "my_gaussian_sigma": best_params['my_gaussian_sigma'],
        "zhiyinPre": best_params['zhiyinPre'],
        "zhisunPre": best_params['zhisunPre'],
        "windowsTime": best_params['windowsTime'],
        "macd_length": 26,
        "max_lookback_kline_count": 100,
        "my_sma_length": 5,
        "my_ema_length": 5,
    }
    
    Uname = 'doge'
    Ktime = '15m'
    strTime_end = '2024-3-5'
    strTime_start = '2024-2-1'
    
    needed_klines = calculate_kline_needed(final_params_dict)
    start_time_adjusted = sub_kline_time(strTime_start, needed_klines, Ktime)
    
    try:
        kline_df = get_local_kline_data(Uname, Ktime, start_time_adjusted, strTime_end)
    except Exception as e:
        print(f"获取本地K线数据失败，无法进行最终回测。错误: {e}")
        exit()

    kline_df_with_rsi = kline_df.copy()
    kline_df_with_rsi['RSI'] = np.nan
    kline_df_with_rsi['RSI_Gaussian_2.0'] = np.nan
    kline_df_with_rsi['投资建议'] = None
    kline_df_with_rsi['tradingSingle'] = 0
    kline_df_with_rsi['nowSingle'] = 0
    kline_df_with_rsi['buyPrice'] = np.nan
    kline_df_with_rsi['识别模式'] = None
    kline_df_with_rsi['置信度'] = None
    kline_df_with_rsi['建议'] = None
    kline_df_with_rsi['价格趋势'] = None
    kline_df_with_rsi['当前RSI'] = np.nan
    kline_df_with_rsi['total_pnl'] = 0.0

    detector = TrendPullbackDetector(
        lookback_period=best_params['lookback_period'],
        rsi_threshold_bull=best_params['rsi_threshold_bull'],
        rsi_threshold_bear=best_params['rsi_threshold_bear'],
        price_retracement_pct=best_params['price_retracement_pct'],
        min_trend_strength=best_params['min_trend_strength'],
        smoothing_factor=best_params['smoothing_factor']
    )

    tradingSingle = 0
    close_values = kline_df_with_rsi['close'].values
    total_loops = len(kline_df_with_rsi) - needed_klines + 1

    for count in range(total_loops):
        nowPrice = close_values[count + needed_klines - 1]
        window_close = close_values[count : count + needed_klines]
        
        window_rsi = fast_rsi_calculation(window_close, final_params_dict["rsi_length"])
        valid_rsi = window_rsi[~np.isnan(window_rsi)]
        
        if len(valid_rsi) > 0:
            smooth_rsi_window = fast_gaussian_smooth(valid_rsi, final_params_dict["my_gaussian_sigma"])
        else:
            continue
        
        window_close_for_detector = window_close[-best_params['lookback_period']:]
        window_rsi_for_detector = window_rsi[-best_params['lookback_period']:]
        smooth_rsi_window_for_detector = smooth_rsi_window[-best_params['lookback_period']:]

        result = detector.detect_pattern(
            window_close_for_detector.tolist(), 
            window_rsi_for_detector.tolist(), 
            smooth_rsi_window_for_detector.tolist(), 
            Ktime
        )
        
        nowSingle = judge_signal(round(result['confidence'], 2), result['pattern'])
        tradingSingle, nowSingle = check_trading_single(tradingSingle, nowSingle)
        
        next_index = count + needed_klines
        if next_index < len(kline_df_with_rsi):
            kline_df_with_rsi.at[next_index, 'tradingSingle'] = tradingSingle
            kline_df_with_rsi.at[next_index, 'nowSingle'] = nowSingle
            if nowSingle != 0:
                kline_df_with_rsi.at[next_index, 'buyPrice'] = calc_price(nowSingle, nowPrice, 0)
                
            kline_df_with_rsi.at[next_index, '识别模式'] = result['pattern']
            kline_df_with_rsi.at[next_index, '置信度'] = f"{result['confidence']:.2%}"
            kline_df_with_rsi.at[next_index, '建议'] = result['recommendation']
            kline_df_with_rsi.at[next_index, '价格趋势'] = result['price_features']['trend_type']
            kline_df_with_rsi.at[next_index, '当前RSI'] = f"{result['rsi_features']['current_rsi']:.2f}"

    kline_df_with_pnl_final = batch_judge_trade(
        kline_df_with_rsi,
        final_params_dict['zhiyinPre'],
        final_params_dict['zhisunPre'],
        final_params_dict['windowsTime'],
        Ktime
    )

    kline_df_with_pnl_final.to_excel("best_params_backtest_result.xlsx", index=False)
    print("使用最佳参数的回测结果已保存到 best_params_backtest_result.xlsx")