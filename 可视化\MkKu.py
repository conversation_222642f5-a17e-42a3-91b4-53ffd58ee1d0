import sqlite3
from datetime import datetime, timedelta
import os
import json
import pandas as pd
import pandas_ta as ta
from scipy.ndimage import gaussian_filter1d # 需要安装 scipy
import scipy.stats as spstats
import numpy as np # 用于生成示例数据
import re
from collections import Counter
import matplotlib.pyplot as plt
from typing import List, Dict, Tuple, Optional
from typing import List, Dict, Any, Union

def save_json(history_data: List[List[Any]], filename: str = 'buysellHistory.json'):
    """
    将 buysellHistory 数据转换为指定的 JSON 格式并保存到文件，
    其中列表元素将尽可能地紧凑地显示在单行中。
    """
    script_dir = os.path.dirname(os.path.abspath(__file__))
    file_path = os.path.join(script_dir, filename)

    json_output_data: List[Dict[str, Any]] = []

    if not (isinstance(history_data, list) and 
            len(history_data) == 3 and
            all(isinstance(sublist, list) for sublist in history_data)):
        raise ValueError("history_data 必须是包含3个列表的列表（例如: [[signals], [highs], [lows]]）。")

    min_len = min(len(history_data[0]), len(history_data[1]), len(history_data[2]))

    for i in range(min_len):
        record = {
            "trade_signal": history_data[0][i],
            "high": history_data[1][i],
            "low": history_data[2][i]
        }
        json_output_data.append(record)

    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            # 设置 indent=None 和 separators 来实现最紧凑的输出
            json.dump(json_output_data, f, indent=None, separators=(',', ':'))
        print(f"buysellHistory 已成功以紧凑格式保存到: {file_path}")
    except IOError as e:
        print(f"保存文件失败: {e}")
    except TypeError as e:
        print(f"数据类型错误，无法序列化为 JSON ({type(history_data)}): {e}")
    except Exception as e:
        print(f"保存时发生未知错误: {e}")


def load_json(filename: str = 'buysellHistory.json') -> List[List[Any]]:
    script_dir = os.path.dirname(os.path.abspath(__file__))
    file_path = os.path.join(script_dir, filename)

    # 如果文件不存在，直接返回初始结构
    if not os.path.exists(file_path):
        print(f"文件 {file_path} 不存在，返回空 buysellHistory 结构。")
        return [[], [], []]
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            raw_data: Union[List[List[Any]], List[Dict[str, Any]]] = json.load(f)
            print(f"buysellHistory 已成功从 {file_path} 加载。")
            
            # --- 根据加载数据的格式进行转换 ---
            if isinstance(raw_data, list) and raw_data and isinstance(raw_data[0], dict):
                # 识别为新格式：列表包含字典
                signals: List[Any] = []
                highs: List[Any] = []
                lows: List[Any] = []
                for record in raw_data:
                    signals.append(record.get("trade_signal"))
                    highs.append(record.get("high"))
                    lows.append(record.get("low"))
                return [signals, highs, lows]
            elif isinstance(raw_data, list) and len(raw_data) == 3 and \
                 all(isinstance(sublist, list) for sublist in raw_data):
                # 识别为旧格式：列表包含三个子列表
                print("信息: 加载了旧格式的 buysellHistory 文件，无需转换。")
                return raw_data
            else:
                # 无法识别的格式
                print(f"警告: {file_path} 中的 buysellHistory 格式未知或无效，返回空结构。")
                return [[], [], []]

    except json.JSONDecodeError as e:
        print(f"文件 {file_path} 内容无效，无法解析为 JSON: {e}")
        return [[], [], []]
    except IOError as e:
        print(f"加载文件失败: {e}")
        return [[], [], []]
    except Exception as e: # 捕获其他可能的转换错误
        print(f"buysellHistory 数据转换时发生错误: {e}, 返回空结构。")
        return [[], [], []]

def get_trade_decision_v2(previous_state: int,
                            current_state: int,
                            current_position: str = 'flat',
                            trade_direction: str = 'both') -> Tuple[str, str]:
    """
    根据“跟屁虫”策略，判断当前应采取的交易决策（完整多空版）。

    Args:
        previous_state (int): 前一根K线的状态 (0-9)。
        current_state (int): 当前K线的状态 (0-9)。
        current_position (str): 当前持仓状态 ('flat', 'long', 'short')。
        trade_direction (str): 交易方向控制 ('both', 'long_only', 'short_only')。

    Returns:
        Tuple[str, str]: 一个包含 (决策指令, 决策原因) 的元组。
    """
    # ================================================================
    # 规则一：空仓时，寻找入场机会
    # ================================================================
    if current_position == 'flat':
        # --- 多头入场逻辑 ---
        if trade_direction in ['both', 'long_only']:
            if previous_state in [2, 8, 9] and current_state == 3:
                return ('BUY', f'黄金做多信号 ({previous_state}→3 突破)')
            if previous_state == 7 and current_state == 1:
                return ('BUY', f'次级做多信号 ({previous_state}→1 动能恢复)')
            # 修正：明确回调结束是转向强势状态
            if previous_state == 4 and current_state in [1, 7]:
                return ('BUY', f'回调结束做多信号 ({previous_state}→{current_state})')

        # --- 空头入场逻辑 ---
        if trade_direction in ['both', 'short_only']:
            if previous_state in [4, 7, 9] and current_state == 6:
                return ('SHORT', f'黄金做空信号 ({previous_state}→6 破位)')
            if previous_state == 8 and current_state == 5:
                return ('SHORT', f'次级做空信号 ({previous_state}→5 动能恢复)')
            # 修正：明确反弹结束是转向弱势状态
            if previous_state == 2 and current_state in [5, 8]:
                return ('SHORT', f'反弹结束做空信号 ({previous_state}→{current_state})')
        
        return ('STAND_ASIDE', '无明确入场信号')

    # ================================================================
    # 规则二：持有多仓时，判断持有或离场
    # ================================================================
    elif current_position == 'long':
        if previous_state == 1 and current_state == 7:
            return ('SELL', f'趋势减弱离场信号 (1→7)')
        if current_state in [4, 5, 6, 8, 9]: # 任何进入非强势多头状态的情况都应警惕
            return ('SELL', f'趋势结构破坏离场 ({previous_state}→{current_state})')
        if previous_state == 3 and current_state != 1:
            return ('SELL', f'突破失败离场 ({previous_state}→{current_state})')

        if current_state in [1, 3, 7]: # 在状态7时，虽然减弱，但仍可观察，所以暂时持有
             return ('HOLD_LONG', '趋势健康或减弱，继续持有')
        
        return ('HOLD_LONG', '未触发明确离场信号')
        
    # ================================================================
    # 规则三：持有空仓时，判断持有或离场
    # ================================================================
    elif current_position == 'short':
        if previous_state == 5 and current_state == 8:
            return ('COVER', f'趋势减弱平仓信号 (5→8)')
        if current_state in [1, 2, 3, 7, 9]:
            return ('COVER', f'趋势结构破坏平仓 ({previous_state}→{current_state})')
        if previous_state == 6 and current_state != 5:
            return ('COVER', f'破位失败平仓 ({previous_state}→{current_state})')

        if current_state in [5, 6, 8]: # 在状态8时，虽然减弱，但仍可观察，所以暂时持有
            return ('HOLD_SHORT', '趋势健康或减弱，继续持有')

        return ('HOLD_SHORT', '未触发明确平仓信号')

    return ('STAND_ASIDE', '未知的持仓状态')


def analyze_trend_changes(data: List[str], 
                         verbose: bool = True, 
                         min_duration: int = 1,
                         show_positions: bool = True) -> Dict:
    """
    分析趋势变化情况和持续时间
    
    Args:
        data (List[str]): 包含趋势状态的列表，如['1. 强势多头', '1. 强势多头', '3. 盘整后突破']
        verbose (bool): 是否打印详细信息，默认True
        min_duration (int): 最小持续时间过滤，默认1
        show_positions (bool): 是否显示K线位置信息，默认True
    
    Returns:
        Dict: 包含分析结果的字典
    """
    
    def extract_trend_number_and_name(trend_str):
        """从趋势字符串中提取数字和名称"""
        if pd.isna(trend_str):
            return None, None
        
        trend_str = str(trend_str).strip()
        
        # 如果是纯数字
        if trend_str.isdigit():
            num = int(trend_str)
            return num, f"趋势{num}"
        
        # 提取数字和名称
        match = re.match(r'^(\d+)\.?\s*(.+)', trend_str)
        if match:
            num = int(match.group(1))
            name = match.group(2).strip()
            return num, name
        
        return None, trend_str
    
    # 1. 数据预处理
    processed_data = []
    for item in data:
        num, name = extract_trend_number_and_name(item)
        if num is not None:
            processed_data.append((num, name))
    
    if not processed_data:
        return {"error": "没有有效的趋势数据"}
    
    # 2. 识别趋势段
    trend_segments = []
    current_trend = processed_data[0][0]
    current_name = processed_data[0][1]
    current_count = 1
    start_index = 0
    
    for i in range(1, len(processed_data)):
        trend_num, trend_name = processed_data[i]
        
        if trend_num == current_trend:
            current_count += 1
        else:
            # 记录前一个趋势段
            trend_segments.append({
                'trend_num': current_trend,
                'trend_name': current_name,
                'count': current_count,
                'start_index': start_index,
                'end_index': i - 1
            })
            
            # 开始新的趋势段
            current_trend = trend_num
            current_name = trend_name
            current_count = 1
            start_index = i
    
    # 添加最后一个趋势段
    trend_segments.append({
        'trend_num': current_trend,
        'trend_name': current_name,
        'count': current_count,
        'start_index': start_index,
        'end_index': len(processed_data) - 1
    })
    
    # 3. 过滤最小持续时间
    filtered_segments = [seg for seg in trend_segments if seg['count'] >= min_duration]
    
    # 4. 生成趋势变化序列（带位置信息）
    trend_changes = []
    trend_changes_with_pos = []
    
    for i, segment in enumerate(filtered_segments):
        start_k = segment['start_index'] + 1  # K线编号从1开始
        end_k = segment['end_index'] + 1
        
        if show_positions:
            trend_desc = f"趋势{segment['trend_num']}（{segment['count']}）[K{start_k}-K{end_k}]"
            trend_changes_with_pos.append(trend_desc)
        
        trend_changes.append(f"趋势{segment['trend_num']}（{segment['count']}）")
    
    # 5. 统计信息
    trend_counter = Counter([seg['trend_num'] for seg in filtered_segments])
    duration_counter = Counter([seg['count'] for seg in filtered_segments])
    
    total_segments = len(filtered_segments)
    total_duration = sum([seg['count'] for seg in filtered_segments])
    avg_duration = total_duration / total_segments if total_segments > 0 else 0
    
    # 6. 趋势变化次数
    change_count = len(filtered_segments) - 1 if len(filtered_segments) > 1 else 0
    
    # 7. 构建结果
    result = {
        'trend_segments': filtered_segments,
        'trend_sequence': ' => '.join(trend_changes),
        'trend_sequence_with_positions': ' => '.join(trend_changes_with_pos) if show_positions else None,
        'trend_statistics': {
            'total_segments': total_segments,
            'total_k_lines': total_duration,
            'change_count': change_count,
            'average_duration': round(avg_duration, 2),
            'trend_frequency': dict(trend_counter),
            'duration_distribution': dict(duration_counter)
        },
        'detailed_changes': [],
        'position_details': []
    }
    
    # 8. 详细变化分析
    for i in range(1, len(filtered_segments)):
        prev_seg = filtered_segments[i-1]
        curr_seg = filtered_segments[i]
        
        prev_start = prev_seg['start_index'] + 1
        prev_end = prev_seg['end_index'] + 1
        curr_start = curr_seg['start_index'] + 1
        curr_end = curr_seg['end_index'] + 1
        
        change_info = {
            'from_trend': prev_seg['trend_num'],
            'to_trend': curr_seg['trend_num'],
            'from_name': prev_seg['trend_name'],
            'to_name': curr_seg['trend_name'],
            'from_duration': prev_seg['count'],
            'to_duration': curr_seg['count'],
            'from_position': f"K{prev_start}-K{prev_end}",
            'to_position': f"K{curr_start}-K{curr_end}",
            'change_description': f"趋势{prev_seg['trend_num']}（{prev_seg['count']}）=> 趋势{curr_seg['trend_num']}（{curr_seg['count']}）",
            'change_at_k_line': curr_start
        }
        result['detailed_changes'].append(change_info)
    
    # 9. 位置详情
    for i, segment in enumerate(filtered_segments):
        start_k = segment['start_index'] + 1
        end_k = segment['end_index'] + 1
        
        position_info = {
            'segment_index': i + 1,
            'trend_num': segment['trend_num'],
            'trend_name': segment['trend_name'],
            'start_k_line': start_k,
            'end_k_line': end_k,
            'duration': segment['count'],
            'position_range': f"K{start_k}-K{end_k}"
        }
        result['position_details'].append(position_info)
    
    # 10. 打印详细信息
    if verbose:
        print("=" * 80)
        print("📊 趋势变化分析报告")
        print("=" * 80)
        
        print(f"\n🔄 趋势变化序列:")
        print(f"   {result['trend_sequence']}")
        
        print(f"\n📍 趋势变化序列（含K线位置）:")
        position_sequence = []
        for segment in filtered_segments:
            start_k = segment['start_index'] + 1  # K线编号从1开始
            end_k = segment['end_index'] + 1
            position_sequence.append(f"趋势{segment['trend_num']}（{segment['count']}）[K{start_k}-K{end_k}]")
        print(f"   {' => '.join(position_sequence)}")
        
        print(f"\n📈 统计摘要:")
        stats = result['trend_statistics']
        print(f"   • 总趋势段数: {stats['total_segments']}")
        print(f"   • 总K线数量: {stats['total_k_lines']}")
        print(f"   • 趋势变化次数: {stats['change_count']}")
        print(f"   • 平均持续时间: {stats['average_duration']} K线")
        
        print(f"\n📍 趋势段位置详情:")
        for segment in filtered_segments:
            start_k = segment['start_index'] + 1
            end_k = segment['end_index'] + 1
            trend_name = segment['trend_name'][:15] + "..." if len(segment['trend_name']) > 15 else segment['trend_name']
            print(f"   • 趋势{segment['trend_num']} ({trend_name}) -> K{start_k}-K{end_k} ({segment['count']}个K线)")
        
        print(f"\n📊 趋势频率统计:")
        for trend_num, count in sorted(stats['trend_frequency'].items()):
            trend_name = next((seg['trend_name'] for seg in filtered_segments 
                             if seg['trend_num'] == trend_num), f"趋势{trend_num}")
            print(f"   • 趋势{trend_num} ({trend_name[:20]}...): {count}次")
        
        print(f"\n⏱️ 持续时间分布:")
        for duration, count in sorted(stats['duration_distribution'].items()):
            print(f"   • {duration}个K线: {count}次")
        
        if result['detailed_changes']:
            print(f"\n🔄 详细变化过程:")
            for i, change in enumerate(result['detailed_changes'], 1):
                start_k = change['change_at_k_line']
                print(f"   {i:2d}. 第{start_k:3d}根K线开始: {change['change_description']}")
        
        print("=" * 60)
    
    return result

def analyze_dataframe_trends(df: pd.DataFrame, 
                           state_col: str = 'emaStas', 
                           verbose: bool = True,
                           show_positions: bool = True) -> Dict:
    """
    分析DataFrame中的趋势变化
    
    Args:
        df (pd.DataFrame): 包含趋势数据的DataFrame
        state_col (str): 趋势状态列名
        verbose (bool): 是否打印详细信息
        show_positions (bool): 是否显示K线位置信息
    
    Returns:
        Dict: 分析结果
    """
    if state_col not in df.columns:
        raise ValueError(f"列 '{state_col}' 不存在于DataFrame中")
    
    # 提取非空数据
    trend_data = df[state_col].dropna().tolist()
    
    if not trend_data:
        return {"error": f"列 '{state_col}' 中没有有效数据"}
    
    return analyze_trend_changes(trend_data, verbose=verbose, show_positions=show_positions)

def get_trend_summary(data: List[str]) -> str:
    """
    获取趋势变化的简要摘要
    
    Args:
        data: 趋势数据列表
    
    Returns:
        str: 简要摘要字符串
    """
    result = analyze_trend_changes(data, verbose=False)
    
    if 'error' in result:
        return result['error']
    
    stats = result['trend_statistics']
    return (f"共{stats['total_segments']}个趋势段，"
            f"{stats['change_count']}次变化，"
            f"平均持续{stats['average_duration']}个K线")


def analyze_numerical_series(data_series):
    data = pd.Series(data_series).dropna()
    if data.empty:
        return {}

    rounded = data.round().astype(int)
    rounded_unique_sorted = np.sort(rounded.unique())
    top_5_max = rounded_unique_sorted[::-1][:5]
    bottom_5_min = rounded_unique_sorted[:5]
    top_5_max_counts = [int((rounded == v).sum()) for v in top_5_max]
    bottom_5_min_counts = [int((rounded == v).sum()) for v in bottom_5_min]

    mean = float(data.mean())
    variance = float(data.var(ddof=0))
    sum_squared_diff = float(((data - mean) ** 2).sum())
    mse = float(((data - mean) ** 2).mean())
    std_dev = float(data.std(ddof=0))

    # 用 spstats.mode
    mode_vals = spstats.mode(rounded, keepdims=True)
    mode = [int(x) for x in np.atleast_1d(mode_vals.mode)]

    conf_1sigma = (mean - std_dev, mean + std_dev)
    conf_2sigma = (mean - 2*std_dev, mean + 2*std_dev)
    conf_3sigma = (mean - 3*std_dev, mean + 3*std_dev)

    n_1sigma = int(((data >= conf_1sigma[0]) & (data <= conf_1sigma[1])).sum())
    n_2sigma = int(((data >= conf_2sigma[0]) & (data <= conf_2sigma[1])).sum())
    n_3sigma = int(((data >= conf_3sigma[0]) & (data <= conf_3sigma[1])).sum())

    return {
        "top_5_max": top_5_max.tolist(),
        "top_5_max_counts": top_5_max_counts,
        "bottom_5_min": bottom_5_min.tolist(),
        "bottom_5_min_counts": bottom_5_min_counts,
        "mean": mean,
        "mode": mode,
        "variance": variance,
        "sum_squared_diff": sum_squared_diff,
        "mse": mse,
        "std_dev": std_dev,
        "conf_interval_1sigma": conf_1sigma,
        "n_1sigma": n_1sigma,
        "conf_interval_2sigma": conf_2sigma,
        "n_2sigma": n_2sigma,
        "conf_interval_3sigma": conf_3sigma,
        "n_3sigma": n_3sigma,
    }

def fast_rsi_numpy(prices, period=14):
    """更快的RSI计算"""
    delta = np.diff(prices)
    gain = np.where(delta > 0, delta, 0)
    loss = np.where(delta < 0, -delta, 0)
    
    # 简单移动平均
    if len(gain) >= period:
        avg_gain = pd.Series(gain).rolling(period).mean()
        avg_loss = pd.Series(loss).rolling(period).mean()
        rs = avg_gain / (avg_loss + 1e-8)
        rsi = 100 - (100 / (1 + rs))
        # 补齐长度
        result = np.full(len(prices), np.nan)
        result[period:] = rsi.dropna().values
        return result
    else:
        return np.full(len(prices), np.nan)
    

def get_local_kline_data(symbol, interval, start_time, end_time, db_path='Kdatas/kline.db'):
    """
    从本地SQLite数据库获取K线数据，支持日期和精确到秒的时间格式
    """
    if not os.path.isfile(db_path):
        raise FileNotFoundError(f"数据库文件不存在: {db_path}")
    table_name = f"{symbol.lower()}{interval.lower()}"

    def _parse_time(tstr):
        # 兼容多种格式
        tried_formats = [
            "%Y-%m-%d %H:%M:%S",
            "%Y-%m-%d",
            "%Y/%m/%d %H:%M:%S",
            "%Y/%m/%d"
        ]
        for fmt in tried_formats:
            try:
                return datetime.strptime(tstr, fmt)
            except:
                continue
        raise ValueError(f"时间格式错误: {tstr}")

    # 保留原始字符串，供SQL用
    def _normalize_time_str(tstr):
        # 自动补齐到"YYYY-MM-DD HH:MM:SS"
        dt = _parse_time(tstr)
        return dt.strftime('%Y-%m-%d %H:%M:%S')

    norm_start = _normalize_time_str(start_time)
    norm_end = _normalize_time_str(end_time)
    if norm_start > norm_end:
        raise ValueError("起始时间不能晚于结束时间")

    conn = sqlite3.connect(db_path)
    try:
        # 用完整时间字符串做条件
        query = f"""
        SELECT * FROM {table_name}
        WHERE open_time >= ?
          AND open_time <= ?
        ORDER BY open_time
        """
        params = (norm_start, norm_end)
        df = pd.read_sql(query, conn, params=params)
        if df.empty:
            min_time_q = f"SELECT min(open_time) FROM {table_name}"
            max_time_q = f"SELECT max(open_time) FROM {table_name}"
            min_time = conn.execute(min_time_q).fetchone()[0]
            max_time = conn.execute(max_time_q).fetchone()[0]
            if not min_time or not max_time:
                raise ValueError(f"本地数据库没有该symbol/interval的数据: {table_name}")
            raise ValueError(f"所选时间区间无数据。可用区间: {min_time} ~ {max_time}")
        else:
            return df
    finally:
        conn.close()
        


def calculate_trading_profit(df, take_profit_pct=None, stop_loss_pct=None):
    """
    计算交易收益统计（纯交易逻辑，不涉及资金管理）
    
    参数:
    df: DataFrame，包含以下列：
        - tradeS: 交易信号 (-1开空仓, 0不开仓, 1开多仓)
        - tradePrice: 交易价格 (0表示无交易)
        - close: 收盘价
        - high: 最高价
        - low: 最低价
    take_profit_pct: 止盈比例，例如0.01表示1%止盈
    stop_loss_pct: 止损比例，例如0.05表示5%止损
    
    返回:
    dict: 包含交易记录和统计信息
    """
    
    # 复制数据避免修改原始数据
    data = df.copy().reset_index(drop=True)
    trades = []  # 存储所有交易记录
    
    # 找到所有开仓信号
    entry_signals = data[(data['tradeS'] != 0) & (data['tradePrice'] != 0)].copy()
    
    for _, entry_row in entry_signals.iterrows():
        entry_idx = entry_row.name
        trade_signal = entry_row['tradeS']
        entry_price = entry_row['tradePrice']
        entry_time = entry_row.get('open_time', entry_idx)  # 如果有时间列则使用，否则用索引
        
        # 计算止盈止损价格
        if trade_signal == 1:  # 多仓
            take_profit_price = entry_price * (1 + take_profit_pct) if take_profit_pct is not None else None
            stop_loss_price = entry_price * (1 - stop_loss_pct) if stop_loss_pct is not None else None
        else:  # 空仓 (trade_signal == -1)
            take_profit_price = entry_price * (1 - take_profit_pct) if take_profit_pct is not None else None
            stop_loss_price = entry_price * (1 + stop_loss_pct) if stop_loss_pct is not None else None
        
        # 向前搜索，找到第一个触发止盈或止损的点
        exit_price = None
        exit_reason = None
        exit_time = None
        exit_idx = None
        
        for future_idx in range(entry_idx + 1, len(data)):
            future_row = data.iloc[future_idx]
            
            if trade_signal == 1:  # 多仓
                # 先检查止损，再检查止盈
                if stop_loss_price is not None and future_row['low'] <= stop_loss_price:
                    exit_price = stop_loss_price
                    exit_reason = 'stop_loss'
                    exit_time = future_row.get('open_time', future_idx)
                    exit_idx = future_idx
                    break
                elif take_profit_price is not None and future_row['high'] >= take_profit_price:
                    exit_price = take_profit_price
                    exit_reason = 'take_profit'
                    exit_time = future_row.get('open_time', future_idx)
                    exit_idx = future_idx
                    break
                    
            else:  # 空仓 (trade_signal == -1)
                # 先检查止损，再检查止盈
                if stop_loss_price is not None and future_row['high'] >= stop_loss_price:
                    exit_price = stop_loss_price
                    exit_reason = 'stop_loss'
                    exit_time = future_row.get('open_time', future_idx)
                    exit_idx = future_idx
                    break
                elif take_profit_price is not None and future_row['low'] <= take_profit_price:
                    exit_price = take_profit_price
                    exit_reason = 'take_profit'
                    exit_time = future_row.get('open_time', future_idx)
                    exit_idx = future_idx
                    break
        
        # 如果没有触发止盈止损，用最后的收盘价平仓
        if exit_price is None:
            exit_price = data.iloc[-1]['close']
            exit_reason = 'end_of_data'
            exit_time = data.iloc[-1].get('open_time', len(data)-1)
            exit_idx = len(data) - 1
        
        # 计算盈亏（按百分比）
        if exit_reason == 'take_profit':
            profit = take_profit_pct if take_profit_pct is not None else 0
        elif exit_reason == 'stop_loss':
            profit = -stop_loss_pct if stop_loss_pct is not None else 0
        else:  # end_of_data
            # 按实际价格计算百分比
            if trade_signal == 1:  # 多仓
                profit = (exit_price - entry_price) / entry_price
            else:  # 空仓
                profit = (entry_price - exit_price) / entry_price
        
        # 记录交易
        trades.append({
            'entry_time': entry_time,
            'exit_time': exit_time,
            'entry_idx': entry_idx,
            'exit_idx': exit_idx,
            'entry_price': entry_price,
            'exit_price': exit_price,
            'position_type': 'long' if trade_signal == 1 else 'short',
            'profit_pct': profit,
            'is_win': profit > 0,
            'exit_reason': exit_reason
        })
    
    # 计算统计指标
    if not trades:
        return {
            'total_trades': 0,
            'win_rate': 0,
            'total_profit_pct': 0,
            'avg_profit_pct': 0,
            'max_profit_pct': 0,
            'max_loss_pct': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'take_profit_trades': 0,
            'stop_loss_trades': 0,
            'unfinished_trades': 0,
            'exit_reasons': {},
            'trade_details': []
        }
    
    # 转换为DataFrame便于分析
    trades_df = pd.DataFrame(trades)
    
    # 计算各项指标
    total_trades = len(trades)
    winning_trades = trades_df['is_win'].sum()
    losing_trades = total_trades - winning_trades
    win_rate = winning_trades / total_trades if total_trades > 0 else 0
    
    total_profit_pct = trades_df['profit_pct'].sum()
    avg_profit_pct = trades_df['profit_pct'].mean()
    max_profit_pct = trades_df['profit_pct'].max()
    max_loss_pct = trades_df['profit_pct'].min()
    
    # 统计不同退出原因
    exit_reasons = trades_df['exit_reason'].value_counts().to_dict()
    
    # 统计止盈止损相关指标
    tp_trades = len([t for t in trades if t['exit_reason'] == 'take_profit'])
    sl_trades = len([t for t in trades if t['exit_reason'] == 'stop_loss'])
    unfinished_trades = len([t for t in trades if t['exit_reason'] == 'end_of_data'])
    
    return {
        'total_trades': total_trades,
        'win_rate': round(win_rate * 100, 2),  # 百分比
        'total_profit_pct': round(total_profit_pct, 6),
        'avg_profit_pct': round(avg_profit_pct, 6),
        'max_profit_pct': round(max_profit_pct, 6),
        'max_loss_pct': round(max_loss_pct, 6),
        'winning_trades': winning_trades,
        'losing_trades': losing_trades,
        'take_profit_trades': tp_trades,
        'stop_loss_trades': sl_trades,
        'unfinished_trades': unfinished_trades,
        'exit_reasons': exit_reasons,
        'trade_details': trades
    }

def plot_capital_curve(stats, initial_capital=200, investment_amount=None, investment_pct=None, leverage=1):
    """
    展示考虑资金占用的真实本金曲线
    
    参数:
    stats: calculate_trading_profit函数返回的结果
    initial_capital: 初始本金，默认200
    investment_amount: 每次交易固定投资金额，例如20（与investment_pct二选一）
    investment_pct: 每次投资占可用本金的比例，例如0.1表示10%（与investment_amount二选一）
    leverage: 杠杆倍数，默认1（无杠杆）
    
    返回:
    dict: 包含本金历史、统计信息等
    """
    import matplotlib.pyplot as plt
    import numpy as np
    
    trades = stats['trade_details']
    if not trades:
        print("没有交易记录，无法绘制本金曲线")
        return None
    
    # 检查参数
    if investment_amount is None and investment_pct is None:
        print("错误：必须指定 investment_amount 或 investment_pct 其中一个")
        return None
    
    if investment_amount is not None and investment_pct is not None:
        print("错误：investment_amount 和 investment_pct 只能指定一个")
        return None
    
    # 创建时间轴（基于交易的时间点）
    all_times = []
    for trade in trades:
        all_times.extend([trade['entry_idx'], trade['exit_idx']])
    all_times = sorted(list(set(all_times)))
    
    # 模拟资金变化过程
    total_capital = initial_capital  # 总本金
    occupied_capital = 0  # 被占用的本金
    active_positions = []  # 当前持仓列表
    
    capital_history = []
    available_capital_history = []
    occupied_capital_history = []
    time_points = []
    
    executed_trades = []  # 实际执行的交易
    skipped_trades = []   # 因资金不足跳过的交易
    
    for time_idx in all_times:
        # 处理当前时间点的平仓
        positions_to_close = []
        for i, pos in enumerate(active_positions):
            if pos['exit_idx'] == time_idx:
                positions_to_close.append(i)
        
        # 平仓处理（从后往前删除，避免索引问题）
        for i in reversed(positions_to_close):
            pos = active_positions.pop(i)
            
            # 释放占用资金
            occupied_capital -= pos['investment_amount']
            
            # 计算实际盈亏（考虑杠杆）
            actual_profit = pos['investment_amount'] * pos['profit_pct'] * leverage
            total_capital += actual_profit
            
            # 记录执行的交易
            executed_trades.append({
                **pos,
                'actual_profit': actual_profit,
                'exit_time_idx': time_idx
            })
        
        # 处理当前时间点的开仓
        for trade in trades:
            if trade['entry_idx'] == time_idx:
                available_capital = total_capital - occupied_capital
                
                # 计算投资金额
                if investment_amount is not None:
                    # 固定金额投资
                    required_investment = investment_amount
                else:
                    # 百分比投资
                    required_investment = available_capital * investment_pct
                
                # 检查资金是否足够
                if required_investment <= available_capital and required_investment > 0:
                    # 执行开仓
                    occupied_capital += required_investment
                    
                    active_positions.append({
                        'entry_idx': trade['entry_idx'],
                        'exit_idx': trade['exit_idx'],
                        'investment_amount': required_investment,
                        'profit_pct': trade['profit_pct'],
                        'position_type': trade['position_type'],
                        'exit_reason': trade['exit_reason']
                    })
                else:
                    # 资金不足，跳过交易
                    skipped_trades.append({
                        **trade,
                        'required_investment': required_investment,
                        'available_capital': available_capital,
                        'reason': 'insufficient_capital'
                    })
        
        # 记录当前状态
        available_capital = total_capital - occupied_capital
        
        capital_history.append(total_capital)
        available_capital_history.append(available_capital)
        occupied_capital_history.append(occupied_capital)
        time_points.append(time_idx)
    
    # 绘制图表
    plt.figure(figsize=(12, 6))
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 只显示本金变化曲线
    plt.plot(time_points, capital_history, 'b-', linewidth=2, label='总本金', marker='o', markersize=3)
    plt.plot(time_points, available_capital_history, 'g-', linewidth=2, label='可用本金', alpha=0.7)
    plt.plot(time_points, occupied_capital_history, 'r-', linewidth=2, label='占用本金', alpha=0.7)
    plt.axhline(y=initial_capital, color='gray', linestyle='--', alpha=0.5, label='初始本金线')
    
    plt.title(f'本金变化曲线 (杠杆倍数: {leverage}x)', fontsize=14, fontweight='bold')
    plt.xlabel('时间点')
    plt.ylabel('本金金额')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 计算统计数据
    final_capital = capital_history[-1] if capital_history else initial_capital
    total_return = (final_capital - initial_capital) / initial_capital * 100
    max_capital = max(capital_history) if capital_history else initial_capital
    min_capital = min(capital_history) if capital_history else initial_capital
    max_drawdown = max_capital - min_capital
    max_drawdown_pct = (max_drawdown / max_capital * 100) if max_capital > 0 else 0
    
    # 添加统计信息
    stats_text = f'初始本金: {initial_capital:.2f}\n最终本金: {final_capital:.2f}\n总收益率: {total_return:.2f}%\n最大回撤: {max_drawdown_pct:.2f}%'
    plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, 
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    plt.show()
    
    # 打印详细统计
    print("\n" + "="*60)
    print("资金管理统计报告".center(60))
    print("="*60)
    
    investment_type = f"固定金额 {investment_amount}" if investment_amount else f"可用本金的 {investment_pct*100:.1f}%"
    print(f"投资策略: {investment_type}")
    print(f"杠杆倍数: {leverage}x")
    print(f"初始本金: {initial_capital:.2f}")
    print(f"最终本金: {final_capital:.2f}")
    print(f"绝对收益: {final_capital - initial_capital:.2f}")
    print(f"总收益率: {total_return:.2f}%")
    print(f"最高本金: {max_capital:.2f}")
    print(f"最低本金: {min_capital:.2f}")
    print(f"最大回撤: {max_drawdown:.2f}")
    print(f"最大回撤率: {max_drawdown_pct:.2f}%")
    
    print(f"\n交易执行情况:")
    print(f"总交易信号: {stats['total_trades']}")
    print(f"实际执行交易: {len(executed_trades)}")
    print(f"跳过交易(资金不足): {len(skipped_trades)}")
    print(f"执行率: {len(executed_trades)/stats['total_trades']*100:.2f}%")
    
    if executed_trades:
        actual_profits = [t['actual_profit'] for t in executed_trades]
        print(f"实际盈利交易: {sum(1 for p in actual_profits if p > 0)}")
        print(f"实际亏损交易: {sum(1 for p in actual_profits if p < 0)}")
        print(f"实际胜率: {sum(1 for p in actual_profits if p > 0) / len(actual_profits) * 100:.2f}%")
    
    print("="*60)
    
    return {
        'capital_history': capital_history,
        'available_capital_history': available_capital_history,
        'occupied_capital_history': occupied_capital_history,
        'time_points': time_points,
        'final_capital': final_capital,
        'total_return': total_return,
        'max_capital': max_capital,
        'min_capital': min_capital,
        'max_drawdown': max_drawdown,
        'max_drawdown_pct': max_drawdown_pct,
        'executed_trades': executed_trades,
        'skipped_trades': skipped_trades,
        'execution_rate': len(executed_trades)/stats['total_trades']*100 if stats['total_trades'] > 0 else 0
    }

def plot_capital_curve_v1(stats, initial_capital=200, investment_pct=0.1, leverage=1, reserve_enabled=True, reserve_trigger_multiple=2, reserve_ratio=0.5):
    """
    展示带截留机制的本金曲线V1版本
    
    参数:
    stats: calculate_trading_profit函数返回的结果
    initial_capital: 初始本金，默认200
    investment_pct: 每次投资占可用本金的比例，例如0.1表示10%
    leverage: 杠杆倍数，默认1（无杠杆）
    reserve_enabled: 是否启用截留机制，默认True
    reserve_trigger_multiple: 截留触发倍数，默认2（即翻倍时触发）
    reserve_ratio: 截留比例，默认0.5（即截留一半新增盈利）
    
    截留机制说明:
    - reserve_trigger_multiple=2, reserve_ratio=0.5: 翻倍时截留新增盈利的一半
    - reserve_trigger_multiple=1.5, reserve_ratio=0.3: 增长50%时截留新增盈利的30%
    - reserve_enabled=False: 关闭截留机制
    
    返回:
    dict: 包含本金历史、截留历史、统计信息等
    """
    
    trades = stats['trade_details']
    if not trades:
        print("没有交易记录，无法绘制本金曲线")
        return None
    
    # 创建时间轴（基于交易的时间点）
    all_times = []
    for trade in trades:
        all_times.extend([trade['entry_idx'], trade['exit_idx']])
    all_times = sorted(list(set(all_times)))
    
    # 初始化变量
    total_capital = initial_capital      # 总本金
    reserved_capital = 0                 # 截留的本金
    occupied_capital = 0                 # 被占用的本金
    active_positions = []                # 当前持仓列表
    
    # 截留机制相关变量
    if reserve_enabled:
        next_reserve_threshold = initial_capital * reserve_trigger_multiple  # 下一个截留阈值
        last_profit_base = 0  # 上次截留时的盈利基数
    else:
        next_reserve_threshold = float('inf')  # 永远不触发
        last_profit_base = 0
    
    # 历史记录
    capital_history = []
    available_capital_history = []
    occupied_capital_history = []
    reserved_capital_history = []
    time_points = []
    reserve_events = []  # 记录截留事件
    
    executed_trades = []  # 实际执行的交易
    skipped_trades = []   # 因资金不足跳过的交易
    
    for time_idx in all_times:
        # 处理当前时间点的平仓
        positions_to_close = []
        for i, pos in enumerate(active_positions):
            if pos['exit_idx'] == time_idx:
                positions_to_close.append(i)
        
        # 平仓处理（从后往前删除，避免索引问题）
        for i in reversed(positions_to_close):
            pos = active_positions.pop(i)
            
            # 释放占用资金
            occupied_capital -= pos['investment_amount']
            
            # 计算实际盈亏（考虑杠杆）
            actual_profit = pos['investment_amount'] * pos['profit_pct'] * leverage
            total_capital += actual_profit
            
            # 记录执行的交易
            executed_trades.append({
                **pos,
                'actual_profit': actual_profit,
                'exit_time_idx': time_idx
            })
        
        # 检查截留机制
        if reserve_enabled and total_capital >= next_reserve_threshold:
            # 计算当前总盈利
            current_total_profit = total_capital - initial_capital
            
            # 计算新增盈利（本轮新增的盈利部分）
            new_profit = current_total_profit - last_profit_base
            
            # 只有新增盈利为正时才截留
            if new_profit > 0:
                # 计算截留金额（截留新增盈利的一部分）
                reserve_amount = new_profit * reserve_ratio
                reserved_capital += reserve_amount
                
                # 记录截留事件
                reserve_events.append({
                    'time_idx': time_idx,
                    'total_capital_before': total_capital,
                    'current_total_profit': current_total_profit,
                    'new_profit': new_profit,
                    'reserve_amount': reserve_amount,
                    'threshold': next_reserve_threshold,
                    'reserve_ratio_used': reserve_ratio
                })
                
                print(f"🔒 触发截留机制！总本金{total_capital:.2f}达到阈值{next_reserve_threshold:.2f}")
                print(f"   总盈利: {current_total_profit:.2f}, 新增盈利: {new_profit:.2f}")
                print(f"   截留金额: {reserve_amount:.2f}({reserve_ratio*100:.1f}%新增盈利), 累计截留: {reserved_capital:.2f}")
                
                # 更新盈利基数
                last_profit_base = current_total_profit
                
                # 更新下一个截留阈值
                next_reserve_threshold = total_capital * reserve_trigger_multiple
        
        # 处理当前时间点的开仓
        for trade in trades:
            if trade['entry_idx'] == time_idx:
                # 计算可用本金 = 总本金 - 截留本金 - 占用本金
                available_capital = total_capital - reserved_capital - occupied_capital
                required_investment = available_capital * investment_pct
                
                # 检查资金是否足够
                if required_investment <= available_capital and required_investment > 0:
                    # 执行开仓
                    occupied_capital += required_investment
                    
                    active_positions.append({
                        'entry_idx': trade['entry_idx'],
                        'exit_idx': trade['exit_idx'],
                        'investment_amount': required_investment,
                        'profit_pct': trade['profit_pct'],
                        'position_type': trade['position_type'],
                        'exit_reason': trade['exit_reason']
                    })
                else:
                    # 资金不足，跳过交易
                    skipped_trades.append({
                        **trade,
                        'required_investment': required_investment,
                        'available_capital': available_capital,
                        'reason': 'insufficient_capital'
                    })
        
        # 记录当前状态
        available_capital = total_capital - reserved_capital - occupied_capital
        
        capital_history.append(total_capital)
        available_capital_history.append(available_capital)
        occupied_capital_history.append(occupied_capital)
        reserved_capital_history.append(reserved_capital)
        time_points.append(time_idx)
    
    # 绘制图表
    plt.figure(figsize=(14, 8))
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 上图：本金变化曲线
    plt.subplot(2, 1, 1)
    plt.plot(time_points, capital_history, 'b-', linewidth=3, label='总本金', marker='o', markersize=4)
    plt.plot(time_points, available_capital_history, 'g-', linewidth=2, label='可用本金', alpha=0.8)
    plt.plot(time_points, occupied_capital_history, 'r-', linewidth=2, label='占用本金', alpha=0.8)
    plt.plot(time_points, reserved_capital_history, 'orange', linewidth=2, label='截留本金', alpha=0.8)
    plt.axhline(y=initial_capital, color='gray', linestyle='--', alpha=0.5, label='初始本金线')
    
    # 标记截留事件
    for event in reserve_events:
        plt.axvline(x=event['time_idx'], color='purple', linestyle=':', alpha=0.7)
        plt.text(event['time_idx'], event['total_capital_before'], 
                f'截留\n{event["reserve_amount"]:.0f}\n(新增{event["new_profit"]:.0f})', 
                ha='center', va='bottom', fontsize=8, 
                bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))
    
    plt.title(f'本金曲线V1 - 截留机制 (杠杆: {leverage}x, 截留: {"开启" if reserve_enabled else "关闭"})', fontsize=14, fontweight='bold')
    plt.xlabel('时间点')
    plt.ylabel('本金金额')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 计算统计数据
    final_capital = capital_history[-1] if capital_history else initial_capital
    final_reserved = reserved_capital_history[-1] if reserved_capital_history else 0
    final_available = available_capital_history[-1] if available_capital_history else initial_capital
    
    total_return = (final_capital - initial_capital) / initial_capital * 100
    max_capital = max(capital_history) if capital_history else initial_capital
    min_capital = min(capital_history) if capital_history else initial_capital
    max_drawdown = max_capital - min_capital
    max_drawdown_pct = (max_drawdown / max_capital * 100) if max_capital > 0 else 0
    
    # 添加统计信息
    final_total_profit = final_capital - initial_capital
    stats_text = f'初始本金: {initial_capital:.2f}\n总本金: {final_capital:.2f}\n总盈利: {final_total_profit:.2f}\n截留本金: {final_reserved:.2f}\n可用本金: {final_available:.2f}\n总收益率: {total_return:.2f}%'
    plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, 
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    # 下图：截留机制可视化
    plt.subplot(2, 1, 2)
    # 绘制本金构成堆叠图
    plt.fill_between(time_points, 0, reserved_capital_history, 
                     color='orange', alpha=0.6, label='截留本金')
    plt.fill_between(time_points, reserved_capital_history, 
                     [r + a for r, a in zip(reserved_capital_history, available_capital_history)], 
                     color='green', alpha=0.6, label='可用本金')
    plt.fill_between(time_points, [r + a for r, a in zip(reserved_capital_history, available_capital_history)], 
                     capital_history, color='red', alpha=0.6, label='占用本金')
    
    plt.title('本金构成分析', fontsize=12)
    plt.xlabel('时间点')
    plt.ylabel('本金金额')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # 打印详细统计
    print("\n" + "="*70)
    print("本金曲线V1统计报告（截留机制）".center(70))
    print("="*70)
    
    investment_type = f"可用本金的 {investment_pct*100:.1f}%"
    print(f"投资策略: {investment_type}")
    print(f"杠杆倍数: {leverage}x")
    print(f"截留机制: {'开启' if reserve_enabled else '关闭'}")
    if reserve_enabled:
        print(f"截留触发: {reserve_trigger_multiple}倍时截留新增盈利的{reserve_ratio*100:.1f}%")
    print(f"初始本金: {initial_capital:.2f}")
    print(f"最终总本金: {final_capital:.2f}")
    print(f"最终截留本金: {final_reserved:.2f}")
    print(f"最终可用本金: {final_available:.2f}")
    print(f"总盈利: {final_capital - initial_capital:.2f}")
    print(f"绝对收益: {final_capital - initial_capital:.2f}")
    print(f"总收益率: {total_return:.2f}%")
    print(f"最高本金: {max_capital:.2f}")
    print(f"最低本金: {min_capital:.2f}")
    print(f"最大回撤: {max_drawdown:.2f}")
    print(f"最大回撤率: {max_drawdown_pct:.2f}%")
    
    print(f"\n截留机制统计:")
    print(f"截留事件次数: {len(reserve_events)}")
    print(f"累计截留金额: {final_reserved:.2f}")
    print(f"截留占总盈利比例: {final_reserved/(final_capital - initial_capital)*100:.2f}%" if final_capital > initial_capital else "无盈利")
    print(f"截留占总本金比例: {final_reserved/final_capital*100:.2f}%")
    
    if reserve_events:
        print("\n截留事件详情:")
        for i, event in enumerate(reserve_events):
            print(f"  第{i+1}次截留: 总本金{event['total_capital_before']:.2f} -> 新增盈利{event['new_profit']:.2f} -> 截留{event['reserve_amount']:.2f}({event['reserve_ratio_used']*100:.1f}%)")
    
    print(f"\n交易执行情况:")
    print(f"总交易信号: {stats['total_trades']}")
    print(f"实际执行交易: {len(executed_trades)}")
    print(f"跳过交易(资金不足): {len(skipped_trades)}")
    print(f"执行率: {len(executed_trades)/stats['total_trades']*100:.2f}%")
    
    if executed_trades:
        actual_profits = [t['actual_profit'] for t in executed_trades]
        print(f"实际盈利交易: {sum(1 for p in actual_profits if p > 0)}")
        print(f"实际亏损交易: {sum(1 for p in actual_profits if p < 0)}")
        print(f"实际胜率: {sum(1 for p in actual_profits if p > 0) / len(actual_profits) * 100:.2f}%")
    
    print("="*70)
    
    return {
        'capital_history': capital_history,
        'available_capital_history': available_capital_history,
        'occupied_capital_history': occupied_capital_history,
        'reserved_capital_history': reserved_capital_history,
        'time_points': time_points,
        'final_capital': final_capital,
        'final_reserved': final_reserved,
        'final_available': final_available,
        'final_total_profit': final_capital - initial_capital,
        'last_profit_base': last_profit_base,
        'total_return': total_return,
        'max_capital': max_capital,
        'min_capital': min_capital,
        'max_drawdown': max_drawdown,
        'max_drawdown_pct': max_drawdown_pct,
        'executed_trades': executed_trades,
        'skipped_trades': skipped_trades,
        'reserve_events': reserve_events,
        'reserve_enabled': reserve_enabled,
        'reserve_trigger_multiple': reserve_trigger_multiple,
        'reserve_ratio': reserve_ratio,
        'execution_rate': len(executed_trades)/stats['total_trades']*100 if stats['total_trades'] > 0 else 0
    }

def save_df_to_excel(df: pd.DataFrame, file_name: str):
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # --- 增强文件扩展名识别和处理 ---
    # 获取文件名的基本部分和扩展名
    base_name, ext = os.path.splitext(file_name)

    # 检查扩展名是否为 .xls 或 .xlsx
    if ext.lower() not in ['.xls', '.xlsx']:
        # 如果扩展名不存在或不正确，则强制设置为 .xlsx
        corrected_file_name = f"{base_name}.xlsx"
        print(f"注意：文件名 '{file_name}' 未包含有效扩展名。已自动更正为 '{corrected_file_name}'。")
    else:
        # 如果扩展名已存在且正确，则直接使用原始文件名
        corrected_file_name = file_name
    # --- 增强识别结束 ---

    output_path = os.path.join(script_dir, corrected_file_name)

    try:
        df.to_excel(output_path, index=False)
        print(f"DataFrame 已成功保存到: {output_path}")
    except Exception as e:
        print(f"保存文件时发生错误: {e}")