# final_optimizer.py

import datetime
import re
from typing import List, Dict
import warnings
import os

import numpy as np
import pandas as pd
import pandas_ta as ta
from skopt import gp_minimize
from skopt.space import Real, Integer
from skopt.utils import use_named_args
import openpyxl 

# --- 核心修正1: 移除所有模拟函数，替换为真实的 import ---
# 请确保这些文件与本脚本在同一个文件夹下
from huice import sub_kline_time # 假设 sub_kline_time 在 huice.py 中
from ku import*
from config import calculate_kline_needed
from analyze_trendV1 import TrendPullbackDetector
from tradeing import check_trading_single, judge_signal, calc_price, batch_judge_trade

# --- 全局配置 ---
EXCEL_FILE_PATH = "optimization_results.xlsx"

# --- 猴子补丁与警告抑制 ---
if not hasattr(np, 'NaN'):
    np.NaN = np.nan
warnings.filterwarnings("ignore", category=UserWarning, module='skopt')
warnings.filterwarnings("ignore", category=FutureWarning)


# ==============================================================================
# 模块一: 核心回测函数
# 这个函数现在会调用您从外部导入的真实函数
# ==============================================================================
def run_backtest(params: Dict, backtest_config: Dict) -> Dict[str, float]:
    try:
        needed_klines = calculate_kline_needed(params)
        start_date_for_klines = sub_kline_time(backtest_config['strTime'], needed_klines, backtest_config['Ktime'])
        
        kline_df = get_local_kline_data(backtest_config['Uname'], backtest_config['Ktime'], start_date_for_klines, backtest_config['endTime'])
        
        if kline_df is None or kline_df.empty or len(kline_df) < needed_klines:
            print(f"[FAIL] 数据量不足或为空。需要 {needed_klines} 条，实际获取 {len(kline_df) if kline_df is not None else 0} 条。")
            return {'profit': -99999, 'error': 'Insufficient K-line data'}

        kline_df_with_results = kline_df.copy()
        for col in ['nowSingle', 'tradingSingle', '达成百分比']: kline_df_with_results[col] = 0
        kline_df_with_results['buyPrice'] = np.nan

        detector_params = {
            'lookback_period': params['lookback_period'], 'rsi_threshold_bull': params['rsi_threshold_bull'],
            'rsi_threshold_bear': params['rsi_threshold_bear'], 'price_retracement_pct': params['price_retracement_pct'],
            'min_trend_strength': params['min_trend_strength'], 'smoothing_factor': params['smoothing_factor'],
        }
        detector = TrendPullbackDetector(**detector_params)
        
        tradingSingle = 0
        close_values = kline_df_with_results['close'].values
        total_loops = len(kline_df_with_results) - needed_klines
        
        for i in range(total_loops):
            now_index = i + needed_klines - 1
            next_index = now_index + 1
            nowPrice = close_values[now_index]
            window_close = close_values[i : i + needed_klines]
            window_rsi = fast_rsi_calculation(window_close, params["rsi_length"])
            if np.isnan(window_rsi).all(): continue
            smooth_rsi_window = fast_gaussian_smooth(window_rsi[~np.isnan(window_rsi)], params["my_gaussian_sigma"])
            
            lookback = params['lookback_period']
            final_close = window_close[-lookback:]; final_rsi = window_rsi[-lookback:]; final_smooth_rsi = smooth_rsi_window[-lookback:]
            if len(final_close) < lookback or len(final_rsi) < lookback or len(final_smooth_rsi) < lookback: continue

            result = detector.detect_pattern(final_close.tolist(), final_rsi.tolist(), final_smooth_rsi.tolist(), backtest_config['Ktime'])
            nowSingle = judge_signal(result['confidence'], result['pattern'])
            tradingSingle, actual_trade_signal = check_trading_single(tradingSingle, nowSingle)
            
            if next_index < len(kline_df_with_results):
                kline_df_with_results.at[next_index, 'tradingSingle'] = tradingSingle
                kline_df_with_results.at[next_index, 'nowSingle'] = actual_trade_signal
                if actual_trade_signal != 0: kline_df_with_results.at[next_index, 'buyPrice'] = calc_price(actual_trade_signal, nowPrice, 0)
        
        kline_df_with_results = batch_judge_trade(kline_df_with_results, params['zhiyin'], params['zhisun'], params['windowsTime'], backtest_config['Ktime'])
        
        trade_returns = kline_df_with_results['达成百分比'][kline_df_with_results['nowSingle'] != 0] / 100
        if trade_returns.empty:
            return {'profit': 0, 'max_drawdown': 0, 'sharpe_ratio': 0, 'calmar_ratio': 0, 'num_trades': 0}
        
        profit = trade_returns.sum() * 100
        num_trades = len(trade_returns)
        equity_curve = (1 + trade_returns).cumprod()
        peak = equity_curve.cummax()
        drawdown = (equity_curve - peak) / peak
        max_drawdown = drawdown.min() * 100
        sharpe_ratio = trade_returns.mean() / (trade_returns.std() + 1e-9) * np.sqrt(252)
        annual_return = equity_curve.iloc[-1]**(252/num_trades) - 1 if num_trades > 0 else 0
        calmar_ratio = annual_return / (abs(max_drawdown/100) + 1e-9)

        return {'profit': profit, 'max_drawdown': max_drawdown, 'sharpe_ratio': sharpe_ratio, 'calmar_ratio': calmar_ratio, 'num_trades': num_trades}

    except Exception as e:
        print(f"!!! [FATAL ERROR] 回测中捕获到致命错误: {e} | 参数: {params}")
        # 在真实环境中，如果需要更详细的错误追溯，可以取消下面这行的注释
        # import traceback; traceback.print_exc()
        return {'profit': -99999, 'error': str(e)}

# ==============================================================================
# 模块二: 结果保存函数
# ==============================================================================
def save_results_to_excel(best_result: Dict, worst_result: Dict, backtest_config: Dict):
    file_path = EXCEL_FILE_PATH
    print(f"\n正在将结果保存到 {file_path}...")
    
    def rename_for_excel(result_dict):
        renamed = result_dict.copy()
        if 'lookback_period' in renamed:
            renamed['malen'] = renamed.pop('lookback_period')
        return renamed

    best_row_data = {'记录类型': '最优', **rename_for_excel(best_result)}
    worst_row_data = {'记录类型': '最差', **rename_for_excel(worst_result)}
    df_new = pd.DataFrame([best_row_data, worst_row_data])

    df_new['虚拟币'] = backtest_config['Uname']
    df_new['K线周期'] = backtest_config['Ktime']
    df_new['起始时间'] = backtest_config['strTime']
    df_new['结束时间'] = backtest_config['endTime']
    df_new['记录时间'] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    final_columns = [
        '记录类型', '虚拟币', 'K线周期', '起始时间', '结束时间', '记录时间', 'zhiyin', 'zhisun', 'malen', 
        'rsi_length', 'my_gaussian_sigma', 'windowsTime', 'price_retracement_pct', 
        'min_trend_strength', 'smoothing_factor', 'rsi_threshold_bull', 'rsi_threshold_bear',
        'profit', 'max_drawdown', 'sharpe_ratio', 'calmar_ratio', 'num_trades'
    ]
    df_final = pd.DataFrame(columns=final_columns)
    df_final = pd.concat([df_final, df_new], ignore_index=True)[final_columns]

    if os.path.exists(file_path):
        try:
            df_old = pd.read_excel(file_path)
            df_to_save = pd.concat([df_old, df_final], ignore_index=True)
            print("文件已存在，追加新纪录。")
        except Exception as e:
            print(f"读取旧Excel文件失败: {e}。将覆盖创建新文件。")
            df_to_save = df_final
    else:
        df_to_save = df_final
        print("文件不存在，将创建新文件。")
        
    try:
        df_to_save.to_excel(file_path, index=False, engine='openpyxl')
        print("结果已成功保存！")
    except Exception as e:
        print(f"保存到Excel失败: {e}")

# ==============================================================================
# 模块三: 优化器主程序 (入口)
# ==============================================================================
if __name__ == "__main__":
    
    # 1. 回测运行配置
    backtest_config = {
        'Uname': 'doge',  # <--- 已修正为小写，以匹配您能正确返回数据的函数要求
        'Ktime': '15m',
        'strTime': '2024-2-1',
        'endTime': '2024-2-8'
    }

    # 2. 定义默认参数和搜索空间
    default_params = {
        'zhiyin': 2.0, 'zhisun': 1.0, 'windowsTime': 56, 'rsi_length': 12, 'my_gaussian_sigma': 0.8,
        'lookback_period': 48, 'rsi_threshold_bull': 42, 'rsi_threshold_bear': 60,
        'price_retracement_pct': 0.382, 'min_trend_strength': 0.6, 'smoothing_factor': 0.8
    }
    search_space = [
        Real(0.5, 5.0, name='zhiyin'), Real(0.5, 3.0, name='zhisun'), Integer(24, 96, name='windowsTime'),
        Integer(8, 20, name='rsi_length'), Real(0.3, 2.0, name='my_gaussian_sigma'), Integer(20, 60, name='lookback_period'),
        Integer(30, 50, name='rsi_threshold_bull'), Integer(50, 70, name='rsi_threshold_bear'),
        Real(0.2, 0.8, name='price_retracement_pct'), Real(0.4, 0.9, name='min_trend_strength'), Real(0.5, 1.0, name='smoothing_factor'),
    ]

    all_run_results = []
    
    @use_named_args(search_space)
    def objective(**params_from_optimizer):
        final_params = default_params.copy()
        final_params.update(params_from_optimizer)
        metrics = run_backtest(final_params, backtest_config)
        profit = metrics.get('profit', -99999)
        if profit != -99999:
             print(f"收益: {profit:<10.4f} | lookback_period: {final_params['lookback_period']}")
        full_result = {**final_params, **metrics}
        all_run_results.append(full_result)
        return -profit

    # 3. 运行贝叶斯优化
    print(f"===== 开始优化: {backtest_config['Uname']} ({backtest_config['strTime']} to {backtest_config['endTime']}) =====")
    n_iterations = 50 
    gp_result = gp_minimize(func=objective, dimensions=search_space, n_calls=n_iterations, n_initial_points=10, random_state=42, acq_func="EI")

    # 4. 筛选与保存结果
    if not all_run_results or all(res.get('profit') == -99999 for res in all_run_results):
        print("\n所有回测均未成功，无法保存有效结果。请检查错误日志。")
    else:
        successful_runs = [res for res in all_run_results if res.get('profit') != -99999]
        sorted_results = sorted(successful_runs, key=lambda x: x.get('profit', -99999), reverse=True)
        best_result = sorted_results[0]
        worst_result = sorted_results[-1]
        
        print("\n" + "="*30 + "\n参数优化完成！\n" + "="*30)
        print(f"找到的最大总收益 (Best Profit): {best_result.get('profit'):.4f}")
        print("\n最佳参数组合及结果:")
        print(best_result)
        save_results_to_excel(best_result, worst_result, backtest_config)