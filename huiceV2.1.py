import requests
import pandas as pd
import sqlite3
import datetime
from typing import List, Union, Dict, Tuple
import pytz
import numpy
if not hasattr(numpy, 'NaN'):
    numpy.NaN = numpy.nan
import pandas_ta as ta
from ku import *
from config import calculate_kline_needed, get_kline_data, add_rsi_to_kline_data
from tradeing import check_trading_single, judge_signal, calc_price, batch_judge_trade
import warnings
warnings.filterwarnings('ignore', category=UserWarning)
from multiprocessing import Pool, cpu_count
from itertools import product
import json
from datetime import datetime, timedelta

def sub_kline_time(old_date, count, kline_type):
    """日期转换函数（向前推算）"""
    if isinstance(old_date, str):
        dt = None
        tried_formats = [
            "%Y-%m-%d %H:%M:%S",
            "%Y-%m-%d",
            "%Y-%m-%d %H:%M",
            "%Y-%m-%d %H",
            "%Y/%m/%d %H:%M:%S",
            "%Y/%m/%d",
            "%Y/%m/%d %H:%M",
            "%Y/%m/%d %H",
        ]
        for fmt in tried_formats:
            try:
                dt = datetime.strptime(old_date, fmt)
                break
            except ValueError:
                continue
        if dt is None:
            raise ValueError(f"无法识别日期格式: {old_date}")
    else:
        dt = old_date

    unit_map = {'m': 1, 'h': 60, 'd': 1440}
    import re
    match = re.match(r"(\d+)([mhd])", kline_type)
    if not match:
        raise ValueError(f"不支持的k线类型: {kline_type}")
    num, unit = match.groups()
    minutes = int(num) * unit_map[unit]

    new_dt = dt - timedelta(minutes=minutes * count)
    return new_dt.strftime("%Y-%m-%d %H:%M:%S")

def run_backtest(params_dict):
    """运行单次回测的核心函数"""
    my_pram = params_dict['my_pram']
    good_pram = params_dict['good_pram']
    time_period = params_dict['time_period']
    
    # 合并参数：good_pram优先级高于my_pram
    test_pram = my_pram.copy()
    test_pram.update(good_pram)
    test_pram['strTime'] = time_period['strTime']
    test_pram['endTime'] = time_period['endTime']
    
    try:
        # 计算所需获取的K线数量
        needed_klines = calculate_kline_needed(test_pram)
        
        # 历史数据回测
        strTime = sub_kline_time(test_pram['strTime'], needed_klines, test_pram['Ktime'])
        
        # 获取K线数据
        kline_df = get_local_kline_data(test_pram['Uname'], test_pram['Ktime'], strTime, test_pram['endTime'])
        
        # 初始化变量
        close_values = kline_df['close'].values
        total_loops = len(kline_df) - needed_klines + 1
        countS1 = []
        countS2 = []
        countTop = []
        countDown = []
        smoothS = []
        single1 = [0, 0, 0]
        single2 = [0, 0, 0]
        
        # 主循环处理
        for count in range(total_loops):
            nowPrice = close_values[count + needed_klines - 1]
            window_close = close_values[count : count + needed_klines]
            window_rsi = fast_rsi_calculation(window_close, test_pram["rsi_length"])
            valid_rsi = window_rsi[~np.isnan(window_rsi)]
            
            if len(valid_rsi) > 0:
                smooth_rsi_window = fast_gaussian_smooth(valid_rsi, test_pram["my_gaussian_sigma"])
                smoothS.append(smooth_rsi_window[-1])
            else:
                continue

            point = count + needed_klines - 1
            stats = analyze_numerical_series(smooth_rsi_window[-test_pram["max_lookback_kline_count"]:])
            
            if smooth_rsi_window[-1] <= stats['conf_interval_2sigma'][0]:
                countDown.append(point)
                single1.append(1)
            else:
                single1.append(0)
                
            if smooth_rsi_window[-1] >= stats['conf_interval_2sigma'][1]:
                countTop.append(point)
                single2.append(1)
            else:
                single2.append(0)
                
            if single1[-3:] == [0, 1, 0] or single1[-3:] == [1, 1, 0]:
                countS1.append(point)
                kline_df.at[point, 'buyPrice'] = nowPrice
                kline_df.at[point, 'nowSingle'] = 1
            elif single2[-3:] == [0, 1, 0] or single2[-3:] == [1, 1, 0]:
                countS2.append(point)
                kline_df.at[point, 'buyPrice'] = nowPrice
                kline_df.at[point, 'nowSingle'] = -1
            else:
                kline_df.at[point, 'buyPrice'] = 0
                kline_df.at[point, 'nowSingle'] = 0
        
        # 批量判断交易
        kline_df = batch_judge_trade(kline_df, test_pram['zhiyinPre'], test_pram['zhisunPre'], 
                                   test_pram['windowsTime'], test_pram['Ktime'])
        
        # 计算收益和交易次数
        nums = pd.to_numeric(kline_df['达成百分比'], errors='coerce')
        total = nums.sum()
        buy_count = (kline_df['nowSingle'] == 1).sum()
        sell_count = (kline_df['nowSingle'] == -1).sum()
        jiaoyicishu = buy_count + sell_count
        
        return {
            'params': good_pram,
            'time_period': time_period,
            'total_return': total,
            'trade_count': jiaoyicishu,
            'buy_count': buy_count,
            'sell_count': sell_count,
            'success': True
        }
    
    except Exception as e:
        return {
            'params': good_pram,
            'time_period': time_period,
            'total_return': -999999,
            'trade_count': 0,
            'buy_count': 0,
            'sell_count': 0,
            'success': False,
            'error': str(e)
        }

def generate_param_combinations(param_ranges):
    """生成参数组合"""
    param_names = list(param_ranges.keys())
    param_values = [param_ranges[name] for name in param_names]
    
    combinations = []
    for values in product(*param_values):
        param_dict = dict(zip(param_names, values))
        combinations.append(param_dict)
    
    return combinations

def optimize_parameters(my_pram, good_pram_ranges, time_periods, n_processes=None, top_k=5):
    """
    主优化函数
    
    Parameters:
    -----------
    my_pram : dict
        基础参数字典
    good_pram_ranges : dict
        待优化参数范围，格式如 {'zhiyinPre': [1.2, 1.3, 1.5], ...}
    time_periods : list of dict
        时间段列表，每个元素格式如 {'strTime': '2025-01-01', 'endTime': '2025-02-01'}
    n_processes : int
        并行进程数，None则使用CPU核心数
    top_k : int
        保留最优的k个参数组合
    
    Returns:
    --------
    dict : 优化结果
    """
    if n_processes is None:
        n_processes = cpu_count()
    
    # 生成参数组合
    param_combinations = generate_param_combinations(good_pram_ranges)
    
    results = {}
    
    # 对每个时间段进行优化
    for period_idx, time_period in enumerate(time_periods):
        print(f"\n=== 优化时间段 {period_idx + 1}: {time_period['strTime']} 到 {time_period['endTime']} ===")
        
        # 准备任务列表
        tasks = []
        for param_combo in param_combinations:
            task = {
                'my_pram': my_pram,
                'good_pram': param_combo,
                'time_period': time_period
            }
            tasks.append(task)
        
        # 并行执行回测
        with Pool(processes=n_processes) as pool:
            period_results = pool.map(run_backtest, tasks)
        
        # 按收益排序并保留前top_k个
        period_results.sort(key=lambda x: x['total_return'], reverse=True)
        top_results = period_results[:top_k]
        
        results[f'period_{period_idx + 1}'] = {
            'time_period': time_period,
            'top_results': top_results,
            'all_results': period_results
        }
        
        # 打印该时间段的最优结果
        print(f"\n该时间段最优 {top_k} 个参数组合：")
        for i, result in enumerate(top_results):
            print(f"{i+1}. 参数: {result['params']}")
            print(f"   总收益: {result['total_return']:.2f}%, 交易次数: {result['trade_count']}")
    
    return results

def rolling_optimization(my_pram, good_pram_ranges, train_period, test_periods, n_processes=None, top_k=5):
    """
    滚动优化：使用训练期的最优参数在测试期进行验证
    
    Parameters:
    -----------
    train_period : dict
        训练时间段，格式如 {'strTime': '2025-01-01', 'endTime': '2025-02-01'}
    test_periods : list of dict
        测试时间段列表
    """
    print(f"\n=== 滚动优化：训练期 {train_period['strTime']} 到 {train_period['endTime']} ===")
    
    # 在训练期优化参数
    train_results = optimize_parameters(my_pram, good_pram_ranges, [train_period], n_processes, top_k)
    
    # 获取训练期最优的top_k个参数
    top_params = [r['params'] for r in train_results['period_1']['top_results']]
    
    print(f"\n使用训练期最优的 {top_k} 个参数在测试期进行验证...")
    
    # 在测试期验证这些参数
    test_results = {}
    for period_idx, test_period in enumerate(test_periods):
        print(f"\n--- 测试时间段 {period_idx + 1}: {test_period['strTime']} 到 {test_period['endTime']} ---")
        
        tasks = []
        for param_combo in top_params:
            task = {
                'my_pram': my_pram,
                'good_pram': param_combo,
                'time_period': test_period
            }
            tasks.append(task)
        
        with Pool(processes=n_processes) as pool:
            period_results = pool.map(run_backtest, tasks)
        
        period_results.sort(key=lambda x: x['total_return'], reverse=True)
        
        test_results[f'test_period_{period_idx + 1}'] = {
            'time_period': test_period,
            'results': period_results
        }
        
        # 打印测试结果
        print(f"测试期表现最佳的参数：")
        best_result = period_results[0]
        print(f"参数: {best_result['params']}")
        print(f"总收益: {best_result['total_return']:.2f}%, 交易次数: {best_result['trade_count']}")
    
    return {
        'train_results': train_results,
        'test_results': test_results
    }

if __name__ == "__main__":
    # 基础参数
    my_pram = {
        "rsi_length": 8,
        "macd_length": 26,
        "max_lookback_kline_count": 110,
        "my_sma_length": 5,
        "my_ema_length": 5,
        "my_gaussian_sigma": 2.6,
        "zhiyinPre": 4,
        "zhisunPre": 0.2,
        "windowsTime": 480,
        "Uname": "doge",
        "Ktime": "15m",
        "strTime": "2025-01-01",
        "endTime": "2025-02-1"
    }
    
    
#     # 生成 1 到 10，间隔为 1 的列表
# list_range_1_10 = list(range(1, 11, 1))
# print(f"使用 range(1, 11, 1) 生成的列表: {list_range_1_10}")

# # 或者简化为 range(1, 11) 因为默认间隔就是 1
# list_range_1_10_simplified = list(range(1, 11))
# print(f"使用 range(1, 11) 生成的列表: {list_range_1_10_simplified}")
# list_arange_1_10_step_0_5 = np.arange(1, 10.1, 0.5).tolist() # 将 10.1 作为停止点，确保包含 10
    # 待优化参数范围
    good_pram_ranges = {
        # "rsi_length": [6,8,10,12,16,24],
        "max_lookback_kline_count": list(range(80, 160, 10)),
        # "my_gaussian_sigma": np.arange(0.2, 4, 0.3).tolist(),
        "my_gaussian_sigma":np.arange(0.8, 2, 0.1).tolist(),
        # "windowsTime": [60, 90, 120, 150],
        # "zhiyinPre": np.arange(3, 5, 0.4).tolist(),
        # "zhisunPre": np.arange(0.2, 1.5, 0.3).tolist()
    }
    
    # 设置并行进程数（None表示使用所有CPU核心）
    n_processes = 4  # 可以根据需要调整
    
    # ========== 场景1：单时间段优化 ==========
    print("========== 场景1：单时间段优化 ==========")
    single_period = [
        {'strTime': '2025-04-15', 'endTime': '2025-05-15'}
    ]
    single_results = optimize_parameters(my_pram, good_pram_ranges, single_period, n_processes)
    
    # # ========== 场景2：多时间段独立优化 ==========
    # print("\n\n========== 场景2：多时间段独立优化 ==========")
    # multiple_periods = [
    #     {'strTime': '2025-01-01', 'endTime': '2025-02-01'},
    #     {'strTime': '2025-02-01', 'endTime': '2025-03-01'},
    #     {'strTime': '2025-03-01', 'endTime': '2025-04-01'}
    # ]
    # multi_results = optimize_parameters(my_pram, good_pram_ranges, multiple_periods, n_processes)
    
    # # ========== 场景3：滚动优化 ==========
    # print("\n\n========== 场景3：滚动优化 ==========")
    # train_period = {'strTime': '2025-01-01', 'endTime': '2025-03-01'}
    # test_periods = [
    #     {'strTime': '2025-03-01', 'endTime': '2025-04-01'},
    #     {'strTime': '2025-04-01', 'endTime': '2025-05-01'}
    # ]
    # rolling_results = rolling_optimization(my_pram, good_pram_ranges, train_period, test_periods, n_processes)
    
    # 保存结果到文件
    print("\n\n========== 保存优化结果 ==========")
    
    # 自定义JSON编码器，处理numpy类型
    class NumpyEncoder(json.JSONEncoder):
        def default(self, obj):
            if isinstance(obj, (numpy.integer, numpy.int64)):
                return int(obj)
            elif isinstance(obj, (numpy.floating, numpy.float64)):
                return float(obj)
            elif isinstance(obj, numpy.ndarray):
                return obj.tolist()
            return super(NumpyEncoder, self).default(obj)
    
    with open('optimization_results.json', 'w', encoding='utf-8') as f:
        all_results = {
            'single_period_optimization': single_results,
            # 'multi_period_optimization': multi_results,
            # 'rolling_optimization': rolling_results
        }
        json.dump(all_results, f, ensure_ascii=False, indent=2, cls=NumpyEncoder)
    print("结果已保存到 optimization_results.json")