import requests
import pandas as pd
import time
from datetime import datetime, timezone
import pytz
import sqlite3
from typing import List, Tuple, Optional

class BinanceKlineDataFetcher:
    def __init__(self):
        self.base_url = "https://fapi.binance.com"
        self.klines_endpoint = "/fapi/v1/klines"
        self.db_path =  r'C:\Users\<USER>\Desktop\my_python\claudeV1\data'
        self._init_db()

    def _init_db(self):
        """初始化数据库，如果目录不存在则创建"""
        import os
        
        # 确保数据库文件所在目录存在
        db_dir = os.path.dirname(self.db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir, exist_ok=True)
            print(f"已创建目录: {db_dir}")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS kline_tables (
                    table_name TEXT PRIMARY KEY,
                    symbol TEXT,
                    interval TEXT
                )
            ''')
            conn.commit()
            conn.close()
            print(f"数据库初始化完成: {self.db_path}")
        except Exception as e:
            print(f"数据库初始化失败: {e}")
    
    def _get_table_name(self, symbol: str, interval: str) -> str:
        """生成表名：DOGEUSDT+15m -> doge15m"""
        base = symbol.replace('USDT', '').lower()
        return f"{base}{interval}"

    def datetime_to_open_time(self, dt_str):
        """
        输入时间字符串（如 '2025-1-1 12:00'），视为北京时间，返回UTC毫秒时间戳
        """
        tz = pytz.timezone('Asia/Shanghai')  # 明确为北京时间
        dt = datetime.strptime(dt_str, "%Y-%m-%d %H:%M")
        dt = tz.localize(dt)
        open_time = int(dt.astimezone(timezone.utc).open_time() * 1000)
        return open_time
    
    def get_klines_batch(self, symbol, interval, start_time, end_time, limit=1500):
        url = self.base_url + self.klines_endpoint
        params = {
            'symbol': symbol,
            'interval': interval,
            'startTime': start_time,
            'endTime': end_time,
            'limit': limit
        }
        try:
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException:
            return None
    
    def get_historical_klines(self, symbol, interval, start_time_str, end_time_str):
        start_open_time = self.datetime_to_open_time(start_time_str)
        end_open_time = self.datetime_to_open_time(end_time_str)
        all_klines = []
        current_start = start_open_time
        while current_start < end_open_time:
            klines_batch = self.get_klines_batch(
                symbol=symbol,
                interval=interval,
                start_time=current_start,
                end_time=end_open_time,
                limit=1500
            )
            if not klines_batch or len(klines_batch) == 0:
                break
            all_klines.extend(klines_batch)
            current_start = klines_batch[-1][0] + 1
            time.sleep(0.1)
        if not all_klines:
            return None
        return self.convert_to_dataframe(all_klines)
    
    def convert_to_dataframe(self, klines_data):
        columns = [
            'open_time', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_volume', 'count', 'taker_buy_volume',
            'taker_buy_quote_volume', 'ignore'
        ]
        df = pd.DataFrame(klines_data, columns=columns)
        # 转为东八区带时区时间
        df['open_time'] = pd.to_datetime(df['open_time'], unit='ms', utc=True).dt.tz_convert('Asia/Shanghai')
        for col in ['open', 'high', 'low', 'close']:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        df['confirm'] = 1
        if len(df) > 0:
            df.iloc[0, df.columns.get_loc('confirm')] = 0  # 最新K线（第一条）confirm=0
        result_df = df[['open_time', 'open', 'high', 'low', 'close', 'confirm']].copy()
        # 最新在前排序
        result_df = result_df.sort_values('open_time', ascending=False).reset_index(drop=True)
        return result_df
        
    def save_to_database(self, df, symbol: str, interval: str):
        """
        函数2：将每次获取的数据通过db保存，不同类型差异化
        如doge15m为单独表，eth4h也单独表，支持不连续数据
        """
        if df is None or df.empty:
            return
            
        table_name = self._get_table_name(symbol, interval)
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建表
            cursor.execute(f'''
                CREATE TABLE IF NOT EXISTS {table_name} (
                    open_time TEXT PRIMARY KEY,
                    open REAL,
                    high REAL, 
                    low REAL,
                    close REAL,
                    confirm INTEGER
                )
            ''')
            
            # 记录表信息
            cursor.execute('''
                INSERT OR REPLACE INTO kline_tables (table_name, symbol, interval)
                VALUES (?, ?, ?)
            ''', (table_name, symbol, interval))
            
            # 保存数据
            for _, row in df.iterrows():
                cursor.execute(f'''
                    INSERT OR REPLACE INTO {table_name} 
                    (open_time, open, high, low, close, confirm)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (str(row['open_time']), row['open'], row['high'], 
                    row['low'], row['close'], row['confirm']))
            
            conn.commit()
            conn.close()
            print(f"已保存{len(df)}条数据到表{table_name}")
            
        except Exception as e:
            print(f"保存数据失败: {e}")

    def get_data_with_cache(self, symbol: str, interval: str, start_time_str: str, end_time_str: str):
        """
        函数1：数据获取，先查看本地有没有，没有就获取，差异化获取支持分段
        考虑k线的类型和时间的差异
        """
        table_name = self._get_table_name(symbol, interval)
        
        # 检查本地数据
        local_data = self._load_local_data(table_name, start_time_str, end_time_str)
        
        if local_data is not None and not local_data.empty:
            print(f"本地已有{len(local_data)}条数据")
            # 检查是否需要补充数据
            missing_ranges = self._find_missing_ranges(local_data, start_time_str, end_time_str)
            if not missing_ranges:
                return local_data
        else:
            print("本地无数据")
            missing_ranges = [(start_time_str, end_time_str)]
        
        # 获取缺失数据
        for gap_start, gap_end in missing_ranges:
            print(f"获取缺失时间段: {gap_start} 到 {gap_end}")
            gap_data = self.get_historical_klines(symbol, interval, gap_start, gap_end)
            if gap_data is not None:
                self.save_to_database(gap_data, symbol, interval)
        
        # 重新加载完整数据
        return self._load_local_data(table_name, start_time_str, end_time_str)

    def _load_local_data(self, table_name: str, start_time_str: str, end_time_str: str):
        """从数据库加载数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
            if not cursor.fetchone():
                conn.close()
                return None
            
            # 转换时间格式查询
            start_dt = datetime.strptime(start_time_str, "%Y-%m-%d %H:%M")
            end_dt = datetime.strptime(end_time_str, "%Y-%m-%d %H:%M")
            
            query = f'''
                SELECT open_time, open, high, low, close, confirm
                FROM {table_name}
                WHERE open_time >= ? AND open_time <= ?
                ORDER BY open_time DESC
            '''
            
            df = pd.read_sql_query(query, conn, params=[
                start_dt.strftime("%Y-%m-%d %H:%M:%S+08:00"),
                end_dt.strftime("%Y-%m-%d %H:%M:%S+08:00")
            ])
            
            conn.close()
            
            if not df.empty:
                df['open_time'] = pd.to_datetime(df['open_time'])
                return df
            return None
            
        except Exception as e:
            print(f"加载数据失败: {e}")
            return None

    def _find_missing_ranges(self, local_data, start_time_str: str, end_time_str: str) -> List[Tuple[str, str]]:
        """找出缺失的时间段"""
        target_start = datetime.strptime(start_time_str, "%Y-%m-%d %H:%M")
        target_end = datetime.strptime(end_time_str, "%Y-%m-%d %H:%M")
        
        local_open_times = pd.to_datetime(local_data['open_time']).dt.tz_localize(None)
        local_start = local_open_times.min()
        local_end = local_open_times.max()
        
        missing_ranges = []
        
        # 前端缺失
        if target_start < local_start:
            gap_end = min(local_start, target_end)
            missing_ranges.append((
                start_time_str,
                gap_end.strftime("%Y-%m-%d %H:%M")
            ))
        
        # 后端缺失
        if target_end > local_end:
            gap_start = max(local_end, target_start)
            missing_ranges.append((
                gap_start.strftime("%Y-%m-%d %H:%M"),
                end_time_str
            ))
        
        return missing_ranges

if __name__ == "__main__":
    fetcher = BinanceKlineDataFetcher()
    symbol = "DOGEUSDT"
    interval = "4h"
    start_time = "2025-1-1 12:00"   # 视为北京时间
    end_time = "2025-6-23 22:00"     # 视为北京时间
    df = fetcher.get_historical_klines(
        symbol=symbol,
        interval=interval,
        start_time_str=start_time,
        end_time_str=end_time
    )
    print(df.head())
    
    df = fetcher.get_data_with_cache("DOGEUSDT", "4h", "2025-1-1 12:00", "2025-6-23 22:00")