2025-07-12 21:10:24,128 - INFO - 加载环境变量文件: C:\Users\<USER>\Desktop\my_python\claudeV1\量化\123.env
2025-07-12 21:10:28,398 - INFO - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/1.1 200 OK"
2025-07-12 21:10:28,399 - INFO - API连接验证成功 - 当前环境: 真实环境
2025-07-12 21:10:29,184 - INFO - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/1.1 200 OK"
2025-07-12 21:10:29,185 - INFO - === 实盘交易开始 ===
2025-07-12 21:10:29,185 - INFO - === 场景1: 简单做多单 ===
2025-07-12 21:10:29,185 - INFO - === 开始执行简单下单 (真实环境) ===
2025-07-12 21:10:29,185 - INFO - 交易对: DOGE-USDT-SWAP, 方向: buy, 触发价: 0.31
2025-07-12 21:10:29,185 - INFO - 杠杆: 3x, 本金: 20.0 USDT
2025-07-12 21:10:29,971 - INFO - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/1.1 200 OK"
2025-07-12 21:10:29,971 - INFO - 可用余额: 0.05 USDT, 需要保证金: 20.00 USDT
2025-07-12 21:10:29,971 - ERROR - 余额不足! 可用: 0.05 USDT < 需要: 20.00 USDT
2025-07-12 21:10:29,971 - ERROR - 多单委托失败
2025-07-12 21:10:29,972 - INFO - === 场景2: 带止盈止损的空单 ===
2025-07-12 21:10:29,972 - INFO - === 开始执行带止盈止损下单 ===
2025-07-12 21:10:29,972 - INFO - 止损比例: 3.0%, 止盈比例: 8.0%
2025-07-12 21:10:29,972 - INFO - === 开始执行简单下单 (真实环境) ===
2025-07-12 21:10:29,972 - INFO - 交易对: DOGE-USDT-SWAP, 方向: sell, 触发价: 0.33
2025-07-12 21:10:29,972 - INFO - 杠杆: 5x, 本金: 30.0 USDT
2025-07-12 21:10:30,750 - INFO - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/1.1 200 OK"
2025-07-12 21:10:30,750 - INFO - 可用余额: 0.05 USDT, 需要保证金: 30.00 USDT
2025-07-12 21:10:30,751 - ERROR - 余额不足! 可用: 0.05 USDT < 需要: 30.00 USDT
2025-07-12 21:10:30,751 - ERROR - 带止盈止损的空单失败
2025-07-12 21:10:30,751 - INFO - === 实盘交易示例结束 ===
