# optimizer.py

import datetime
import re
from typing import List, Dict
import warnings

import numpy as np
import pandas as pd
import pandas_ta as ta
from skopt import gp_minimize
from skopt.space import Real, Integer
from skopt.utils import use_named_args

# --- 猴子补丁开始 ---
# 解决老版本 pandas/numpy 中可能存在的 ImportError
if not hasattr(np, 'NaN'):
    np.NaN = np.nan
# --- 猴子补丁结束 ---

# 关闭scikit-optimize优化过程中的警告信息，使输出更整洁
warnings.filterwarnings("ignore", category=UserWarning, module='skopt')


# ==============================================================================
# 模块一: 模拟的外部依赖函数
# ------------------------------------------------------------------------------
# 在原始代码中，这些函数是从 ku, config, analyze_trendV1, tradeing 等模块导入的。
# 为了使脚本能够独立运行，我们在此创建这些函数的模拟版本。
# 在您的实际环境中，您可以替换为真实的模块导入。
# ==============================================================================

def get_local_kline_data(symbol: str, interval: str, start_time: str, end_time: str) -> pd.DataFrame:
    """
    模拟从本地数据库或文件获取K线数据。
    在实际应用中，请替换为您的真实数据加载函数。
    这里我们生成一些随机的、但带有趋势性的数据用于演示。
    """
    print(f"模拟加载数据: {symbol}, {interval}, from {start_time} to {end_time}")
    date_range = pd.to_datetime(pd.date_range(start=start_time, end=end_time, freq='15T'))
    n = len(date_range)
    # 创建一个带有趋势和噪声的价格序列
    price_trend = np.linspace(100, 150, n) + np.random.normal(0, 2, n)
    price_noise = np.random.normal(0, 0.5, n).cumsum()
    close_prices = price_trend + price_noise
    
    data = {
        'open_time': date_range,
        'open': close_prices - np.random.uniform(-1, 1, n),
        'high': close_prices + np.random.uniform(0, 2, n),
        'low': close_prices - np.random.uniform(0, 2, n),
        'close': close_prices,
        'volume': np.random.uniform(1000, 5000, n)
    }
    df = pd.DataFrame(data)
    # 确保 'low' 是最低价, 'high' 是最高价
    df['low'] = df[['open', 'close', 'low']].min(axis=1)
    df['high'] = df[['open', 'close', 'high']].max(axis=1)
    return df

class TrendPullbackDetector:
    """
    模拟 analyze_trendV1.py 中的趋势回撤检测器。
    """
    def __init__(self, lookback_period, rsi_threshold_bull, rsi_threshold_bear, 
                 price_retracement_pct, min_trend_strength, smoothing_factor):
        self.lookback_period = lookback_period
        self.rsi_threshold_bull = rsi_threshold_bull
        self.rsi_threshold_bear = rsi_threshold_bear
        # 保存其它参数，即使在模拟函数中未使用，以保持接口一致性
        self.price_retracement_pct = price_retracement_pct
        self.min_trend_strength = min_trend_strength
        self.smoothing_factor = smoothing_factor

    def detect_pattern(self, prices, rsi, smooth_rsi, kline_type):
        """
        模拟检测逻辑。
        一个非常简化的模拟：如果RSI低于牛市阈值且价格在上涨，则认为是“牛市回踩”。
        反之，则为“熊市反弹”。
        """
        current_price = prices[-1]
        start_price = prices[0]
        current_rsi = rsi[-1]
        
        pattern = 'Neutral'
        confidence = 0.5
        recommendation = 'Hold'
        trend_type = 'Sideways'

        # 模拟趋势判断
        if current_price > start_price * 1.02: # 价格上涨超过2%
            trend_type = 'Uptrend'
            if current_rsi < self.rsi_threshold_bull:
                pattern = 'Bullish Pullback'
                confidence = 0.75
                recommendation = 'Buy'
        elif current_price < start_price * 0.98: # 价格下跌超过2%
            trend_type = 'Downtrend'
            if current_rsi > self.rsi_threshold_bear:
                pattern = 'Bearish Rally'
                confidence = 0.75
                recommendation = 'Sell'
        
        return {
            'pattern': pattern,
            'confidence': confidence,
            'recommendation': recommendation,
            'price_features': {'trend_type': trend_type},
            'rsi_features': {'current_rsi': current_rsi}
        }

def fast_rsi_calculation(close_prices: np.ndarray, length: int) -> np.ndarray:
    """模拟快速RSI计算。实际使用pandas_ta。"""
    series = pd.Series(close_prices)
    rsi = ta.rsi(series, length=length)
    return rsi.to_numpy()

def fast_gaussian_smooth(data: np.ndarray, sigma: float) -> np.ndarray:
    """模拟快速高斯平滑。"""
    series = pd.Series(data)
    # 使用 rolling 窗口和高斯函数进行平滑
    window = int(sigma * 4) + 1
    if len(series) < window:
        return series.to_numpy() # 如果数据不足，返回原数据
    smoothed = series.rolling(window=window, win_type='gaussian', center=True).mean(std=sigma)
    smoothed = smoothed.bfill().ffill()
    return smoothed.to_numpy()
    
def calculate_kline_needed(params: Dict) -> int:
    """模拟计算所需的K线总数。"""
    return params.get("max_lookback_kline_count", 100)

def judge_signal(confidence: float, pattern: str) -> int:
    """模拟判断信号。"""
    if 'Bullish' in pattern and confidence > 0.7:
        return 1  # 买入信号
    if 'Bearish' in pattern and confidence > 0.7:
        return -1 # 卖出信号
    return 0 # 无信号

def check_trading_single(current_signal: int, new_signal: int) ->tuple[int, int]:
    """模拟交易信号检查逻辑。避免在持仓时反向开仓。"""
    if current_signal != 0 and new_signal == -current_signal:
        # 如果当前持有多仓(1)，来了一个卖出信号(-1)，则先平仓
        return 0, new_signal
    if current_signal == 0 and new_signal != 0:
        # 如果当前空仓，来新信号，则开仓
        return new_signal, new_signal
    # 其他情况（如无信号、同向信号）保持不变
    return current_signal, 0 # 返回当前持仓状态 和 本次操作信号

def calc_price(signal: int, now_price: float, offset: float) -> float:
    """模拟计算买入/卖出价。"""
    return now_price

def batch_judge_trade(df: pd.DataFrame, zhiyin: float, zhisun: float, window: int, kline_type: str) -> pd.DataFrame:
    """
    模拟批量判断交易结果并计算'达成百分比'。
    这是整个回测的核心评价函数。
    """
    df['达成百分比'] = 0.0
    for i in df.index:
        if df.at[i, 'nowSingle'] != 0:
            signal = df.at[i, 'nowSingle']
            buy_price = df.at[i, 'buyPrice']
            
            # 向前看 window 根K线
            future_klines = df.loc[i+1 : i+window]
            if future_klines.empty:
                continue

            target_price = buy_price * (1 + zhiyin / 100 * signal)
            stop_loss_price = buy_price * (1 - zhisun / 100 * signal)
            
            achieved_profit = False
            for _, row in future_klines.iterrows():
                # 做多
                if signal == 1:
                    if row['high'] >= target_price:
                        df.at[i, '达成百分比'] = zhiyin
                        achieved_profit = True
                        break
                    if row['low'] <= stop_loss_price:
                        df.at[i, '达成百分比'] = -zhisun
                        achieved_profit = True
                        break
                # 做空
                elif signal == -1:
                    if row['low'] <= target_price:
                        df.at[i, '达成百分比'] = zhiyin
                        achieved_profit = True
                        break
                    if row['high'] >= stop_loss_price:
                        df.at[i, '达成百分比'] = -zhisun
                        achieved_profit = True
                        break
            
            # 如果在窗口期内既未止盈也未止损，则按最后一根K线的收盘价计算
            if not achieved_profit:
                final_price = future_klines.iloc[-1]['close']
                profit_percent = (final_price - buy_price) / buy_price * 100 * signal
                df.at[i, '达成百分比'] = profit_percent

    return df

def sub_kline_time(old_date, count, kline_type):
    """日期转换函数（向前推算）"""
    dt = pd.to_datetime(old_date)
    unit_map = {'m': 'T', 'h': 'H', 'd': 'D'}
    match = re.match(r"(\d+)([mhd])", kline_type)
    num, unit = match.groups()
    delta = pd.Timedelta(value=int(num) * count, unit=unit_map[unit])
    new_dt = dt - delta
    return new_dt.strftime("%Y-%m-%d %H:%M:%S")

# ==============================================================================
# 模块二: 核心回测函数 (关注点分离)
# ------------------------------------------------------------------------------
# 这个函数封装了完整的单次回测逻辑。
# ==============================================================================

def run_backtest(params: Dict) -> float:
    """
    执行单次回测。
    :param params: 包含所有策略参数的字典。
    :return: 回测的总收益（'达成百分比'之和）。
    """
    try:
        # --- 1. 设置和数据准备 ---
        Uname = 'DOGE'
        Ktime = '15m'
        strTime = '2024-2-1'
        endTime = '2024-3-5'
        
        needed_klines = calculate_kline_needed(params)
        start_date_for_klines = sub_kline_time(strTime, needed_klines, Ktime)
        
        kline_df = get_local_kline_data(Uname, Ktime, start_date_for_klines, endTime)
        
        if kline_df.empty or len(kline_df) < needed_klines:
            print("数据不足，跳过此次回测。")
            return -99999.0 # 返回一个极小值表示失败

        kline_df_with_results = kline_df.copy()
        kline_df_with_results['nowSingle'] = 0
        kline_df_with_results['tradingSingle'] = 0
        kline_df_with_results['buyPrice'] = np.nan
        kline_df_with_results['达成百分比'] = 0.0

        # --- 2. 初始化策略组件 ---
        detector = TrendPullbackDetector(
            lookback_period=params['lookback_period'],
            rsi_threshold_bull=params['rsi_threshold_bull'],
            rsi_threshold_bear=params['rsi_threshold_bear'],
            price_retracement_pct=params['price_retracement_pct'],
            min_trend_strength=params['min_trend_strength'],
            smoothing_factor=params['smoothing_factor']
        )
        
        # --- 3. 循环遍历K线进行模拟 ---
        tradingSingle = 0
        close_values = kline_df_with_results['close'].values
        total_loops = len(kline_df_with_results) - needed_klines
        
        for i in range(total_loops):
            now_index = i + needed_klines -1
            next_index = now_index + 1

            nowPrice = close_values[now_index]
            window_close = close_values[i : i + needed_klines]
            
            window_rsi = fast_rsi_calculation(window_close, params["rsi_length"])
            
            valid_rsi_mask = ~np.isnan(window_rsi)
            if not np.any(valid_rsi_mask):
                continue
            
            smooth_rsi_window = fast_gaussian_smooth(window_rsi[valid_rsi_mask], params["my_gaussian_sigma"])
            
            # 截取所需长度
            lookback = params['lookback_period']
            final_close = window_close[-lookback:]
            final_rsi = window_rsi[-lookback:]
            final_smooth_rsi = smooth_rsi_window[-lookback:]

            if len(final_close) < lookback or len(final_rsi) < lookback or len(final_smooth_rsi) < lookback:
                continue

            result = detector.detect_pattern(final_close.tolist(), final_rsi.tolist(), final_smooth_rsi.tolist(), Ktime)
            
            nowSingle = judge_signal(round(result['confidence'], 3), result['pattern'])
            tradingSingle, actual_trade_signal = check_trading_single(tradingSingle, nowSingle)
            
            if next_index < len(kline_df_with_results):
                kline_df_with_results.at[next_index, 'tradingSingle'] = tradingSingle
                kline_df_with_results.at[next_index, 'nowSingle'] = actual_trade_signal
                if actual_trade_signal != 0:
                    kline_df_with_results.at[next_index, 'buyPrice'] = calc_price(actual_trade_signal, nowPrice, 0)
        
        # --- 4. 批量计算交易结果 ---
        kline_df_with_results = batch_judge_trade(
            kline_df_with_results,
            params['zhiyinPre'],
            params['zhisunPre'],
            params['windowsTime'],
            Ktime
        )
        
        # --- 5. 计算总收益并返回 ---
        nums = pd.to_numeric(kline_df_with_results['达成百分比'], errors='coerce').fillna(0)
        total_profit = nums.sum()
        
        return total_profit

    except Exception as e:
        print(f"回测过程中发生错误: {e}")
        return -99999.0 # 返回极小值表示异常

# ==============================================================================
# 模块三: 优化器主程序 (关注点分离)
# ------------------------------------------------------------------------------
# 这部分代码负责定义优化目标、参数空间，并执行优化过程。
# ==============================================================================

if __name__ == "__main__":
    
    # --- 1. 定义默认参数 ---
    # 这些是未被优化的基础参数
    default_params = {
        "max_lookback_kline_count": 100, # 这是获取数据时向前看的长度，不是策略的lookback
    }

    # --- 2. 定义需要优化的参数及其搜索空间 ---
    # 这是面向优化器的“接口”定义
    search_space = [
        # 基础指标参数
        Integer(8, 20, name='rsi_length'),
        Real(0.3, 2.0, name='my_gaussian_sigma'),
        
        # 交易执行参数
        Real(0.5, 5.0, name='zhiyinPre'),     
        Real(0.5, 3.0, name='zhisunPre'),      
        Integer(12, 96, name='windowsTime'), # 例如，15分钟K线，96根约等于1天     
        
        # 趋势检测器参数
        Integer(20, 80, name='lookback_period'), 
        Integer(30, 48, name='rsi_threshold_bull'), 
        Integer(52, 70, name='rsi_threshold_bear'),    
        Real(0.2, 0.8, name='price_retracement_pct'), 
        Real(0.4, 0.9, name='min_trend_strength'), 
        Real(0.5, 1.0, name='smoothing_factor')
    ]

    # --- 3. 定义优化目标函数 ---
    # @use_named_args 是一个方便的装饰器，它将优化器传入的位置参数列表转换为关键字参数字典
    @use_named_args(search_space)
    def objective(**params_to_optimize):
        """
        目标函数，scikit-optimize 会尝试最小化该函数的返回值。
        """
        # 合并默认参数和待优化参数
        all_params = default_params.copy()
        all_params.update(params_to_optimize)
        
        # 调用核心回测逻辑
        total_profit = run_backtest(all_params)
        
        # 打印当前进度
        print(f"参数: {params_to_optimize}")
        print(f"--> 本次回测总收益: {total_profit:.4f}\n")
        
        # 因为 gp_minimize 是最小化函数，所以我们需要返回总收益的负数
        # 最小化 -profit 就等于最大化 profit
        return -total_profit

    # --- 4. 运行贝叶斯优化 ---
    print("="*30)
    print("开始进行参数优化...")
    print("="*30)
    
    # n_calls 是总的迭代次数（评估次数）
    # n_initial_points 是初始随机搜索的点数
    n_iterations = 50
    result = gp_minimize(
        func=objective,
        dimensions=search_space,
        n_calls=n_iterations,
        n_initial_points=10, # 建议初始点为总迭代次数的10-20%
        random_state=42, # 设置随机种子以保证结果可复现
        acq_func="EI" # 使用期望提升(Expected Improvement)作为采集函数
    )

    # --- 5. 输出最终优化结果 ---
    print("\n\n" + "="*30)
    print("参数优化完成！")
    print("="*30)
    
    best_profit = -result.fun
    print(f"找到的最大总收益 (Best Score): {best_profit:.4f}")
    
    print("\n最佳参数组合:")
    best_parameters = {dim.name: val for dim, val in zip(search_space, result.x)}
    for key, value in best_parameters.items():
        if isinstance(value, float):
            print(f"- {key}: {value:.4f}")
        else:
            print(f"- {key}: {value}")