# 文件名: okx_checker.py
import sys
import os
import okx
from dotenv import load_dotenv
import okx.Trade as Trade

print("--- OKX 库与环境诊断工具 ---")
print("="*35)

# 1. 打印当前使用的 Python 解释器路径
print(f"🐍 当前Python解释器路径:\n   {sys.executable}\n")

# 2. 打印 OKX 库的版本号
try:
    print(f"📦 OKX 库版本: {okx.__version__}")
except AttributeError:
    print("📦 OKX 库版本: 未找到 (可能是非常旧的版本)")

# 3. 打印 OKX 库的安装位置
try:
    print(f"📍 OKX 库安装路径:\n   {okx.__file__}\n")
except Exception as e:
    print(f"📍 无法确定OKX库的路径: {e}\n")

# 4. 初始化 TradeAPI 并列出其所有可用的方法
print("--- 初始化TradeAPI并列出其全部可用功能 ---")
print("="*42)
try:
    DOTENV_PATH = r'C:\Users\<USER>\Desktop\my_python\claudeV1\量化\123.env'
    load_dotenv(dotenv_path=DOTENV_PATH)
    api_key = os.getenv('OKX_API_KEY')
    secret_key = os.getenv('OKX_SECRET_KEY')
    passphrase = os.getenv('OKX_PASSPHRASE')

    if not all([api_key, secret_key, passphrase]):
        print("❌ 错误: 无法从 .env 文件加载API凭据。")
    else:
        trade_api = Trade.TradeAPI(api_key, secret_key, passphrase, False, "0")
        print("✅ TradeAPI 初始化成功。")

        print("\n📜 TradeAPI 对象中所有可用的方法/属性列表:")
        method_list = [method for method in dir(trade_api) if not method.startswith('_')]
        for method_name in sorted(method_list):
            print(f"  - {method_name}")

        print("\n--- 关键方法检查 ---")
        print("="*22)
        required_method = 'get_order_algo_history'
        if hasattr(trade_api, required_method):
            print(f"✅ 成功找到所需方法: '{required_method}'")
        else:
            print(f"❌ 未能找到所需方法: '{required_method}'")

except Exception as e:
    print(f"\n❌ 在初始化或检查过程中发生错误: {e}")

print("\n--- 诊断结束 ---")