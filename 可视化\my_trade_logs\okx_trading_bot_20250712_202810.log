2025-07-12 20:28:14,787 - INFO - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/1.1 200 OK"
2025-07-12 20:28:14,789 - INFO - API连接验证成功
2025-07-12 20:28:14,789 - INFO - === 示例1: 简单下单 ===
2025-07-12 20:28:14,790 - INFO - === 开始执行简单下单 ===
2025-07-12 20:28:14,790 - INFO - 交易对: DOGE-USDT-SWAP, 方向: sell, 触发价: 0.19801
2025-07-12 20:28:14,790 - INFO - 杠杆: 5x, 本金: 10.0 USDT
2025-07-12 20:28:15,574 - INFO - HTTP Request: GET https://www.okx.com/api/v5/account/balance "HTTP/1.1 200 OK"
2025-07-12 20:28:15,578 - INFO - 可用余额: 13.02 USDT, 需要保证金: 10.00 USDT
2025-07-12 20:28:16,357 - INFO - HTTP Request: GET https://www.okx.com/api/v5/public/instruments?instType=SWAP&instId=DOGE-USDT-SWAP "HTTP/1.1 200 OK"
2025-07-12 20:28:16,358 - INFO - 合约详情 DOGE-USDT-SWAP: 每张=1000.0个币, 最小下单=0.01张, 价格精度=1e-05
2025-07-12 20:28:16,359 - INFO - 下单量计算详情:
2025-07-12 20:28:16,359 - INFO -   - 目标名义价值: 50.00 USDT
2025-07-12 20:28:16,359 - INFO -   - 计算用价格: 0.19801
2025-07-12 20:28:16,360 - INFO -   - 合约面值: 1000.0 个币/张
2025-07-12 20:28:16,360 - INFO -   - 理论张数: 0.252512
2025-07-12 20:28:16,360 - INFO -   - 最终下单张数: 0.25
2025-07-12 20:28:16,360 - INFO -   - 实际交易币数: 250.0
2025-07-12 20:28:16,360 - INFO -   - 实际名义价值: 49.50 USDT
2025-07-12 20:28:17,147 - INFO - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instType=SWAP&instId=DOGE-USDT-SWAP "HTTP/1.1 200 OK"
2025-07-12 20:28:17,149 - ERROR - 获取待成交委托失败: 'TradeAPI' object has no attribute 'get_algo_order_list'
2025-07-12 20:28:17,149 - INFO - 设置DOGE-USDT-SWAP short方向杠杆为5x
2025-07-12 20:28:17,439 - INFO - HTTP Request: POST https://www.okx.com/api/v5/account/set-leverage "HTTP/2 200 OK"
2025-07-12 20:28:17,441 - INFO - 提交计划委托: 价格达到0.19801时，以0.19801开sell仓
2025-07-12 20:28:18,280 - INFO - HTTP Request: POST https://www.okx.com/api/v5/trade/order-algo "HTTP/1.1 200 OK"
2025-07-12 20:28:18,280 - INFO - 下单成功! 策略ID: 2678344927842668544
2025-07-12 20:28:18,281 - INFO - 开始监控委托单2678344927842668544, 超时时间: 1.0分钟
2025-07-12 20:28:29,106 - INFO - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instType=SWAP&instId=DOGE-USDT-SWAP "HTTP/1.1 200 OK"
2025-07-12 20:28:39,381 - INFO - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instType=SWAP&instId=DOGE-USDT-SWAP "HTTP/2 200 OK"
2025-07-12 20:28:49,688 - INFO - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instType=SWAP&instId=DOGE-USDT-SWAP "HTTP/2 200 OK"
2025-07-12 20:29:00,490 - INFO - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instType=SWAP&instId=DOGE-USDT-SWAP "HTTP/1.1 200 OK"
2025-07-12 20:29:11,269 - INFO - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instType=SWAP&instId=DOGE-USDT-SWAP "HTTP/1.1 200 OK"
2025-07-12 20:29:21,563 - INFO - HTTP Request: GET https://www.okx.com/api/v5/account/positions?instType=SWAP&instId=DOGE-USDT-SWAP "HTTP/2 200 OK"
2025-07-12 20:29:21,565 - WARNING - 委托超时，正在取消: 2678344927842668544
2025-07-12 20:29:22,351 - INFO - HTTP Request: POST https://www.okx.com/api/v5/trade/cancel-algos "HTTP/1.1 200 OK"
2025-07-12 20:29:22,352 - INFO - 委托已取消
2025-07-12 20:29:22,352 - INFO - 简单下单成功: 2678344927842668544
