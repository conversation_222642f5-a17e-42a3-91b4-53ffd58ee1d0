
import requests
import pandas as pd
import sqlite3
import datetime
from typing import List, Union
import pytz # 用于时区转换
# import pandas as pd
# --- 猴子补丁开始 ---
# 解决 ImportError: cannot import name 'NaN' from 'numpy'
# 强制将 numpy.nan 导入为 numpy.NaN
import numpy
if not hasattr(numpy, 'NaN'):
    numpy.NaN = numpy.nan
# --- 猴子补丁结束 ---
import pandas_ta as ta # 导入 pandas_ta 库 (现在可以正常导入了)
# from ku import generate_plot,  generate_dual_axis_plot
from ku import *
from config import  calculate_kline_needed,get_kline_data,add_rsi_to_kline_data
from analyze_trendV1 import TrendPullbackDetector


from tradeing import check_trading_single ,judge_signal,calc_price,batch_judge_trade

def sub_kline_time(old_date, count, kline_type):
    """
    日期转换函数（向前推算）
    :param old_date: str/datetime，原日期，支持多种字符串日期格式或datetime对象
    :param count: int，步数
    :param kline_type: str，K线类型，如"1m","5m","15m","1h","1d"
    :return: 新的日期（datetime对象）
    """
    # 支持字符串和datetime输入
    if isinstance(old_date, str):
        dt = None
        tried_formats = [
            "%Y-%m-%d %H:%M:%S",
            "%Y-%m-%d",
            "%Y-%m-%d %H:%M",
            "%Y-%m-%d %H",
            "%Y/%m/%d %H:%M:%S",
            "%Y/%m/%d",
            "%Y/%m/%d %H:%M",
            "%Y/%m/%d %H",
        ]
        for fmt in tried_formats:
            try:
                dt = datetime.strptime(old_date, fmt)
                break
            except ValueError:
                continue
        if dt is None:
            raise ValueError(f"无法识别日期格式: {old_date}")
    else:
        dt = old_date

    # K线类型转换为分钟数
    unit_map = {'m': 1, 'h': 60, 'd': 1440}
    import re
    match = re.match(r"(\d+)([mhd])", kline_type)
    if not match:
        raise ValueError(f"不支持的k线类型: {kline_type}")
    num, unit = match.groups()
    minutes = int(num) * unit_map[unit]

    new_dt = dt - timedelta(minutes=minutes * count)
    return new_dt.strftime("%Y-%m-%d %H:%M:%S")

def is_last_segment_turning(arr, param):
    min_len = param.get('min_len', 3)
    interval = param.get('interval', 3)
    single = param.get('single', 1.5)
    n = len(arr)
    if n < min_len + 1:
        print(f'[长度不足] arr实际长度: {n}, min_len+1: {min_len+1}')
        return 0

    last_jump = abs(arr[-1] - arr[-2])
    if last_jump <= single:
        print(f'[最后一跳绝对值不够] last_jump: {last_jump}, single: {single}')
        return 0
    else:
        print(f'[最后一跳通过] last_jump: {last_jump}, single: {single}')

    trend_segment = arr[-(min_len+1):-1]
    print(f'[趋势段] trend_segment: {trend_segment}')

    # 单调递增判定
    is_increasing = True
    for i in range(len(trend_segment) - 1):
        diff_segment = trend_segment[i+1] - trend_segment[i]
        if not (diff_segment > 0 and abs(diff_segment) <= interval):
            print(f'[递增失败] 下标{i}到{i+1}，diff_segment: {diff_segment}, interval: {interval}')
            is_increasing = False
            break
    if is_increasing:
        print('[递增趋势] 判定为单调递增')

    # 宽松递减判定（允许小反弹，只要绝对值≤interval就通过）
    is_decreasing = all(abs(trend_segment[i+1] - trend_segment[i]) <= interval for i in range(len(trend_segment)-1))
    if is_decreasing:
        print('[递减趋势] 判定为宽松递减')
    else:
        for i in range(len(trend_segment)-1):
            diff_segment = trend_segment[i+1] - trend_segment[i]
            if abs(diff_segment) > interval:
                print(f'[递减失败] 下标{i}到{i+1}，diff_segment: {diff_segment}, interval: {interval}')

    diff = arr[-1] - arr[-2]
    print(f'[最后一跳差值] diff: {diff} (arr[-1]: {arr[-1]}, arr[-2]: {arr[-2]})')

    if is_increasing and diff < 0:
        print('[拐点判断] 顶部拐点成立')
        return -1
    elif is_decreasing and diff > 0:
        print('[拐点判断] 底部拐点成立')
        return 1
    else:
        print('[拐点判断] 不成立')
        return 0

def analyze_trendV0(data: List[float], malen: int) -> dict:
    """
    分析列表数据的趋势
    
    参数:
    data: 数值列表
    malen: 检查长度，从后往前的长度
    
    返回:
    包含趋势分析结果的字典
    """
    if len(data) < malen:
        return {
            'error': f'数据长度 {len(data)} 小于检查长度 {malen}'
        }
    
    # 取最后 malen 个数据点
    recent_data = data[-malen:]
    
    # 计算趋势指标
    x = np.arange(malen)
    y = np.array(recent_data)
    
    # 线性回归计算斜率
    slope = np.polyfit(x, y, 1)[0]
    
    # 计算相对变化率
    relative_change = (recent_data[-1] - recent_data[0]) / recent_data[0] * 100
    
    # 计算移动平均（如果长度足够）
    if malen >= 3:
        ma_short = np.mean(recent_data[-3:])  # 最近3个点的平均
        ma_long = np.mean(recent_data[:3])    # 开始3个点的平均
        ma_trend = "上升" if ma_short > ma_long else "下降" if ma_short < ma_long else "平稳"
    else:
        ma_trend = "数据点太少"
    
    # 判断总体趋势
    if abs(slope) < 0.01:  # 斜率接近0
        trend = "平稳"
    elif slope > 0:
        if slope > 0.1:
            trend = "强烈上升"
        else:
            trend = "缓慢上升"
    else:
        if slope < -0.1:
            trend = "强烈下降"
        else:
            trend = "缓慢下降"
    
    # 计算波动性（标准差）
    volatility = np.std(recent_data)
    
    # 计算最大回撤
    peak = np.maximum.accumulate(recent_data)
    drawdown = (recent_data - peak) / peak * 100
    max_drawdown = np.min(drawdown)
    
    return {
        'trend': trend,
        'slope': slope,
        'relative_change_percent': round(relative_change, 2),
        'moving_average_trend': ma_trend,
        'volatility': round(volatility, 4),
        'max_drawdown_percent': round(max_drawdown, 2),
        'start_value': recent_data[0],
        'end_value': recent_data[-1],
        'min_value': min(recent_data),
        'max_value': max(recent_data),
        'data_points': malen
    }
def safe_get(d, keys, default=''):
    """多级安全取值，遇到None或非dict自动返回default"""
    for k in keys:
        if isinstance(d, dict):
            d = d.get(k, None)
        else:
            return default
        if d is None:
            return default
    return d if d is not None else default

def list_to_str(lst, prefix=''):
    """将列表转换为带前缀的字符串，用于DataFrame存储"""
    if not lst:
        return ''
    return '; '.join([f"{prefix}{str(item)}" for item in lst])

def write_analysis_to_kline(df, next_index, analysis_result):
    """
    将分析结果写入kline_df_with_rsi的指定行，包括多字段（如投资建议、投资可信度、投资理由等）。
    如果DataFrame中无对应列则自动补充，如果索引越界则打印警告。
    """
    if not isinstance(analysis_result, dict):
        print(f"警告：analysis_result 为空或不是字典，实际值为：{analysis_result}")
        return

    # 兼容你的advice结构（如投资建议、投资可信度、投资理由）
    advice = analysis_result.get('advice', {})
    fields_to_update = {
        '投资建议': advice.get('投资建议', analysis_result.get('投资建议', '')),
        # '投资可信度': advice.get('投资可信度', analysis_result.get('投资可信度', '')),
        # '投资理由': list_to_str(advice.get('投资理由', analysis_result.get('投资理由', []))),
        # # 你可以继续补充其它分析字段
        # '趋势': analysis_result.get('trend', ''),
        # '斜率': analysis_result.get('slope', ''),
        # '移动均线趋势': analysis_result.get('moving_average_trend', ''),
        # ...（略，其它字段同之前）
    }

    # 确保所有字段都在DataFrame中
    for col in fields_to_update.keys():
        if col not in df.columns:
            df[col] = ""
    if next_index < len(df):
        for field, value in fields_to_update.items():
            df.at[next_index, field] = value
    else:
        print(f"警告：next_index = {next_index} 已超出DataFrame范围，无法写入数据！")
        print(fields_to_update['投资建议'])

# 示例
    # arr1 = [1,2,3,4,5,6,7,7.5,5]  # 先增后降
    # arr2 = [7,6,5,4,3,2,1,1.5,3]  # 先降后升
    # print(is_last_segment_turning(arr1, min_len=3, interval=3, single=2.5)) # 输出 -1
    # print(is_last_segment_turning(arr2, min_len=3, interval=3, single=1.5)) # 输出 1

if __name__ == "__main__":


    my_pram = {
    "rsi_length": 20,
    "macd_length": 26,
    "max_lookback_kline_count": 100,
    "my_sma_length": 5,
    "my_ema_length": 5,
    "my_gaussian_sigma": 1.39,
    "zhiyinPre":5,
    "zhisunPre":3,
    "windowsTime":12
}
    Uname='doge'
    Ktime='15m'
    strTime='2024-1-1'
    endTime='2024-3-5'
    ###获取k线
    # 1. 计算所需获取的K线数量
    needed_klines = calculate_kline_needed(my_pram)
    kline_df = None
    kline_df_with_rsi = None
    # print(needed_klines)
    # 2. 获取K线数据
    # 交易对为 DOGEUSDT，时间间隔15分钟
    # kline_df_50 = get_kline_data(symbol="DOGEUSDT", interval="15m", count=needed_klines)


    ####历史数据回测
    strTime=sub_kline_time(strTime, needed_klines,Ktime)
    # print(strTime)
    try:
        # kline_df = get_local_kline_data('doge', '15m', '2024-12-30 10:00:00','2025-2-1')
        kline_df = get_local_kline_data(Uname, Ktime, strTime, endTime)
        print(kline_df.head())
    except Exception as e:
        print(e)



###逐段计算出
    kline_df_with_rsi = kline_df.copy()  # 先复制，后续逐段赋值
    kline_df_with_rsi['RSI'] = None
    kline_df_with_rsi['RSI_Gaussian_2.0'] = None
    kline_df_with_rsi['投资建议'] = None
    output = ""

    
    # verifier = UniqueTrendVerifier()  ###v1

    ##V2 分析器
    lookback_period=80
    detector = TrendPullbackDetector(
        lookback_period,
        rsi_threshold_bull=48,
        rsi_threshold_bear=52,
        price_retracement_pct=0.200,
        min_trend_strength=0.4,
        smoothing_factor=0.75
    )
    
 
    tradingSingle=0  ##交易信号


    close_values = kline_df_with_rsi['close'].values  # 预提取，避免重复调用
    total_loops = len(kline_df_with_rsi) - needed_klines + 1
    print(f"开始处理 {total_loops} 个窗口...")

    for count in range(total_loops):
    # 添加异常处理
        nowPrice = close_values[count + needed_klines - 1]  # 使用预提取的数据
        window_close = close_values[count : count + needed_klines]  # 使用预提取的数据
        # 或者方案B: 如果您想尝试新的快速函数（需要先测试结果是否一致）
        window_rsi = fast_rsi_calculation(window_close, my_pram["rsi_length"])
        valid_rsi = window_rsi[~np.isnan(window_rsi)]
        if len(valid_rsi) > 0:
            smooth_rsi_window = fast_gaussian_smooth(valid_rsi, my_pram["my_gaussian_sigma"])
        else:
            continue
        
        # 检查数据长度
        # print(len(window_close),window_close)
        # print(len(window_rsi),window_rsi)
        # print(len(smooth_rsi_window),smooth_rsi_window)
        window_close=window_close[-lookback_period:]
        window_rsi=window_rsi[-lookback_period:]
        smooth_rsi_window=smooth_rsi_window[-lookback_period:]
        # print(len(window_close),window_close)
        # print(len(window_rsi),window_rsi)
        # print(len(smooth_rsi_window),smooth_rsi_window)
        
        result = detector.detect_pattern(window_close.tolist(), window_rsi.tolist(), smooth_rsi_window.tolist(), Ktime)
        # print(result)
        # print(f"识别模式: {result['pattern']}")
        # print(f"置信度: {result['confidence']:.2%}")
        # print(f"建议: {result['recommendation']}")
        # print(f"价格趋势: {result['price_features']['trend_type']}")
        # print(f"当前RSI: {result['rsi_features']['current_rsi']:.2f}")
        # break    
        # 趋势分析（保持原逻辑）
        
       
        
     
        # print(analysis_result)
        # break
        # 投资建议写入下一根K线
        next_index = count + needed_klines
        
        # 模拟交易
        
        nowSingle = judge_signal(round(result['confidence'], 3),result['pattern'])
        
        tradingSingle, nowSingle = check_trading_single(tradingSingle, nowSingle)
        
        # # 写入结果
        if next_index < len(kline_df_with_rsi):  # 添加边界检查
            kline_df_with_rsi.at[next_index, 'tradingSingle'] = tradingSingle
            kline_df_with_rsi.at[next_index, 'nowSingle'] = nowSingle
            if nowSingle != 0:
                kline_df_with_rsi.at[next_index, 'buyPrice'] = calc_price(nowSingle, nowPrice, 0)
                
       
        # write_analysis_to_kline(kline_df_with_rsi, next_index, analysis_result)
        # kline_df_with_rsi.at[next_index, 'buyPrice'] = window_close[-1]
        kline_df_with_rsi.at[next_index, '识别模式'] = result['pattern']
        kline_df_with_rsi.at[next_index, '置信度'] = f"{result['confidence']:.2%}"
        kline_df_with_rsi.at[next_index, '建议'] = result['recommendation']
        kline_df_with_rsi.at[next_index, '价格趋势'] = result['price_features']['trend_type']
        kline_df_with_rsi.at[next_index, '当前RSI'] = f"{result['rsi_features']['current_rsi']:.2f}"

        # 进度显示
        if count % 50 == 0:
            print(f"进度: {count}/{total_loops} ({count/total_loops*100:.1f}%)")

    # 保存结果
    kline_df_with_rsi=batch_judge_trade(kline_df_with_rsi,my_pram['zhiyinPre'],my_pram['zhisunPre'],my_pram['windowsTime'],Ktime)
   
    nums = pd.to_numeric(kline_df_with_rsi['达成百分比'], errors='coerce')
    ##总收益
    total = nums.sum()
    print(total)
    # 求和（自动忽略 NaN）
 
    # metrics=calculate_trading_metrics_with_signals(kline_df_with_rsi)
    # print("\n--- 交易指标报告 ---")
    # for key, value in metrics.items():
    #     if isinstance(value, float):
    #         print(f"{key}: {value:.2f}")
    #     else:
    #         print(f"{key}: {value}")
            
            
    kline_df_with_rsi.to_excel("6-29BTC逐段V1kline_with_advice.xlsx", index=False)
    print("K线数据已成功保存到 逐段kline_with_advice.xlsx")





# # for count in range(len(kline_df_with_rsi) - needed_klines + 1):
    #     arr = kline_df_with_rsi['RSI_Gaussian_2.0'].values[count : count + needed_klines]
    #     result = is_last_segment_turning(arr, my_pram)
    #     print(result)

          
 
        # print(count,(count + needed_klines))
    # 在这里对arr进行操作
    # arr = kline_df_with_rsi[count : needed_klines]
    # arr= [7,6,5,4,3,2,1,1.5,-4] 
    # result = is_last_segment_turning(arr, my_pram)
    # print(arr )
    # print( kline_df_with_rsi[count : needed_klines])
    # print(kline_df_with_rsi['RSI_Gaussian_2.0'].values[96:248])
    # print(kline_df_with_rsi)




    # print(kline_df)
    # lenk=len(kline_df)
    # print(kline_df.iloc[needed_klines].values)
    # print(kline_df[0:needed_klines])
    # print(kline_df.iloc[lenk-1].values)
    # kline_df_with_rsi = add_rsi_to_kline_data(kline_df, rsi_length=my_pram["rsi_length"])