import requests
import json
import pandas as pd
import time
import hmac
import hashlib
import base64
from datetime import datetime
import os
from dotenv import load_dotenv

class OKXClient:
    def __init__(self, api_key=None, secret_key=None, passphrase=None, sandbox=False):
        """
        初始化OKX客户端，优先从环境变量读取API信息
        Args:
            api_key: API密钥
            secret_key: 秘密密钥
            passphrase: API密码短语
            sandbox: 是否使用沙盒环境
        """
        # 加载与脚本同目录的 123.env 文件
        dotenv_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '123.env')
        if os.path.exists(dotenv_path):
            load_dotenv(dotenv_path=dotenv_path)

        self.api_key = api_key or os.getenv("OKX_API_KEY")
        self.secret_key = secret_key or os.getenv("OKX_SECRET_KEY")
        self.passphrase = passphrase or os.getenv("OKX_PASSPHRASE")
        if not all([self.api_key, self.secret_key, self.passphrase]):
            raise ValueError("API KEY/SECRET/PASSPHRASE 不能为空，请设置环境变量OKX_API_KEY, OKX_SECRET_KEY, OKX_PASSPHRASE或在初始化时传入。")
        if sandbox:
            self.base_url = 'https://www.okx.com'  # 沙盒环境可调整
        else:
            self.base_url = 'https://www.okx.com'  # 正式环境

    def _get_open_time(self):
        """获取UTC时间戳"""
        return datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'

    def _sign(self, open_time, method, request_path, body=''):
        """生成签名"""
        message = open_time + method + request_path + body
        mac = hmac.new(
            bytes(self.secret_key, encoding='utf8'),
            bytes(message, encoding='utf8'),
            digestmod=hashlib.sha256
        )
        return base64.b64encode(mac.digest()).decode()

    def _request(self, method, endpoint, params=None):
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        open_time = self._get_open_time()
        if method == 'GET' and params:
            query_string = '&'.join([f"{k}={v}" for k, v in params.items()])
            url += f"?{query_string}"
            request_path = f"{endpoint}?{query_string}"
        else:
            request_path = endpoint
        body = json.dumps(params) if method == 'POST' and params else ''
        signature = self._sign(open_time, method, request_path, body)
        headers = {
            'OK-ACCESS-KEY': self.api_key,
            'OK-ACCESS-SIGN': signature,
            'OK-ACCESS-open_time': open_time,
            'OK-ACCESS-PASSPHRASE': self.passphrase,
            'Content-Type': 'application/json'
        }
        try:
            resp = (requests.get(url, headers=headers)
                    if method == 'GET'
                    else requests.post(url, headers=headers, data=body))
            resp.raise_for_status()
            return resp.json()
        except requests.exceptions.RequestException as e:
            print(f"请求错误: {e}")
            return None

def download_historical_data(client, symbol, timeframe='1d', start_time=None, end_time=None, limit=300):
    """
    下载指定时间区间的历史K线数据（时间为UTC）
    Args:
        client: OKX客户端实例
        symbol: 交易对（如 'DOGE-USDT'）
        timeframe: '1m', '5m', '15m', '1H', '1D' 等
        start_time: 开始时间，字符串 'YYYY-MM-DD HH:MM'
        end_time: 结束时间，字符串 'YYYY-MM-DD HH:MM'
        limit: 每次最大拉取条数，最大300
    Returns:
        pandas.DataFrame: 历史数据
    """
    import pandas as pd
    from datetime import datetime, timedelta
    import time

    print(f"正在下载 {symbol} {timeframe} K线数据, 时间区间: {start_time} ~ {end_time}")
    endpoint = '/api/v5/market/candles'

    # 时间字符串转为毫秒时间戳（OKX接口要求毫秒）
    def to_ms(dt_str):
        return int(pd.to_datetime(dt_str, utc=True).open_time() * 1000)

    # 默认拉取最近数据
    if not start_time and not end_time:
        params = {
            'instId': symbol,
            'bar': timeframe,
            'limit': limit
        }
        response = client._request('GET', endpoint, params)
        if response and response.get('code') == '0':
            data = response['data']
            df = pd.DataFrame(data, columns=['open_time', 'open', 'high', 'low', 'close', 'volume', 'volCcy', 'volCcyQuote', 'confirm'])
            df['open_time'] = pd.to_datetime(pd.to_numeric(df['open_time']), unit='ms', utc=True)
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            df[numeric_columns] = df[numeric_columns].astype(float)
            return df.sort_values('open_time')
        else:
            print(f"下载历史数据失败: {response}")
            return pd.DataFrame()

    # 有 start_time/end_time，循环分页拉取
    after = to_ms(start_time) if start_time else None
    end_ms = to_ms(end_time) if end_time else int(time.time() * 1000)
    all_data = []
    while True:
        params = {
            'instId': symbol,
            'bar': timeframe,
            'limit': limit
        }
        if after:
            params['after'] = after
        response = client._request('GET', endpoint, params)
        if not response or response.get('code') != '0':
            print(f"下载历史数据失败: {response}")
            break

        data = response['data']
        if not data:
            break

        # 数据是从最新到最旧，需反转
        data = data[::-1]
        for row in data:
            ts = int(row[0])
            if ts > end_ms:
                continue  # 超出结束时间
            if start_time and ts < after:
                continue
            all_data.append(row)
        # 判断是否已经到达结束时间
        last_ts = int(data[-1][0])
        if last_ts >= end_ms or len(data) < limit:
            break
        after = last_ts + 1  # 下一轮从上轮最后一条的下一毫秒拉取
        time.sleep(0.2)  # 防止接口限速

    if not all_data:
        print("无数据")
        return pd.DataFrame()

    df = pd.DataFrame(all_data, columns=['open_time', 'open', 'high', 'low', 'close', 'volume', 'volCcy', 'volCcyQuote', 'confirm'])
    df['open_time'] = pd.to_datetime(pd.to_numeric(df['open_time']), unit='ms', utc=True)
    numeric_columns = ['open', 'high', 'low', 'close', 'volume']
    df[numeric_columns] = df[numeric_columns].astype(float)
    # 只保留在区间内的数据
    if start_time:
        df = df[df['open_time'] >= pd.to_datetime(start_time, utc=True)]
    if end_time:
        df = df[df['open_time'] <= pd.to_datetime(end_time, utc=True)]
    df = df.sort_values('open_time').reset_index(drop=True)
    print(f"成功下载 {len(df)} 条历史数据")
    return df

def get_current_market_data(client, symbol):
    """
    获取当前市场行情数据
    Args:
        client: OKX客户端实例
        symbol: 交易对
    Returns:
        dict: 当前行情数据
    """
    print(f"正在获取 {symbol} 的当前行情...")
    endpoint = '/api/v5/market/ticker'
    params = {'instId': symbol}
    response = client._request('GET', endpoint, params)
    if response and response.get('code') == '0' and response['data']:
        market_data = response['data'][0]
        result = {
            'symbol': market_data['instId'],
            'last_price': float(market_data['last']),
            'bid_price': float(market_data['bidPx']),
            'ask_price': float(market_data['askPx']),
            'volume_24h': float(market_data['vol24h']),
            'change_24h': float(market_data.get('chg24h', 0)),
            'open_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        print(f"当前价格: {result['last_price']}")
        return result
    else:
        print(f"获取行情数据失败: {response}")
        return {}

def read_local_data(file_path):
    """
    读取本地数据文件
    Args:
        file_path: 文件路径
    Returns:
        pandas.DataFrame or dict: 读取的数据
    """
    print(f"正在读取本地文件: {file_path}")
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return None
    try:
        file_extension = os.path.splitext(file_path)[1].lower()
        if file_extension == '.csv':
            data = pd.read_csv(file_path)
            print(f"成功读取CSV文件，共 {len(data)} 行数据")
            return data
        elif file_extension == '.json':
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"成功读取JSON文件")
            return data
        elif file_extension in ['.xlsx', '.xls']:
            data = pd.read_excel(file_path)
            print(f"成功读取Excel文件，共 {len(data)} 行数据")
            return data
        else:
            print(f"不支持的文件格式: {file_extension}")
            return None
    except Exception as e:
        print(f"读取文件失败: {e}")
        return None

def write_data_to_local(data, file_path, file_format='csv'):
    """
    将数据写入本地文件
    Args:
        data: 要保存的数据 (DataFrame 或 dict)
        file_path: 保存路径
        file_format: 文件格式 ('csv', 'json', 'excel')
    Returns:
        bool: 是否保存成功
    """
    print(f"正在保存数据到: {file_path}")
    try:
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        if file_format.lower() == 'csv':
            if isinstance(data, pd.DataFrame):
                data.to_csv(file_path, index=False, encoding='utf-8')
            else:
                pd.DataFrame([data]).to_csv(file_path, index=False, encoding='utf-8')
        elif file_format.lower() == 'json':
            with open(file_path, 'w', encoding='utf-8') as f:
                if isinstance(data, pd.DataFrame):
                    data.to_json(f, orient='records', indent=2, force_ascii=False)
                else:
                    json.dump(data, f, indent=2, ensure_ascii=False, default=str)
        elif file_format.lower() == 'excel':
            if isinstance(data, pd.DataFrame):
                data.to_excel(file_path, index=False)
            else:
                pd.DataFrame([data]).to_excel(file_path, index=False)
        else:
            print(f"不支持的文件格式: {file_format}")
            return False
        print(f"数据保存成功: {file_path}")
        return True
    except Exception as e:
        print(f"保存数据失败: {e}")
        return False

def main():
    """主函数 - 测试所有功能"""
    print("=== OKX交易所SDK测试 ===\n")
    # API_KEY/SECRET/PASSPHRASE建议设置为环境变量，或者直接传参
    try:
        client = OKXClient()  # 优先从环境变量读取
    except Exception as e:
        print(f"初始化失败: {e}")
        return

    symbol = 'BTC-USDT'

    print("1. 测试下载历史数据")
    print("-" * 30)
    historical_data = download_historical_data(client, symbol, '15m', 5000)
    if not historical_data.empty:
        print(f"历史数据预览:\n{historical_data.head()}\n")

    print("2. 测试获取当前行情")
    print("-" * 30)
    current_data = get_current_market_data(client, symbol)
    if current_data:
        print(f"当前行情: {current_data}\n")

    print("3. 测试数据写入本地")
    print("-" * 30)
    if not historical_data.empty:
        write_data_to_local(historical_data, f'data/{symbol}_historical.csv', 'csv')
        write_data_to_local(historical_data, f'data/{symbol}_historical.json', 'json')
    if current_data:
        write_data_to_local(current_data, f'data/{symbol}_current.json', 'json')

    print("4. 测试读取本地数据")
    print("-" * 30)
    loaded_csv = read_local_data(f'data/{symbol}_historical.csv')
    if loaded_csv is not None:
        print(f"从CSV读取的数据: {len(loaded_csv)} 行")
    loaded_json = read_local_data(f'data/{symbol}_current.json')
    if loaded_json is not None:
        print(f"从JSON读取的数据: {loaded_json}")

    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()