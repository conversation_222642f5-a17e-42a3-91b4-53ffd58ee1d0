2025-07-13 00:45:02,169 - INFO - MainThread - V5.2 交易执行器启动...
2025-07-13 00:45:09,627 - INFO - TradeThread-DOGE - 为 DOGE-USDT-SWAP 设置杠杆: 5x, 仓位: long
2025-07-13 00:45:10,545 - INFO - TradeThread-DOGE - HTTP Request: POST https://www.okx.com/api/v5/account/set-leverage "HTTP/1.1 200 OK"
2025-07-13 00:45:11,365 - INFO - TradeThread-DOGE - HTTP Request: GET https://www.okx.com/api/v5/public/instruments?instType=SWAP&instId=DOGE-USDT-SWAP "HTTP/1.1 200 OK"
2025-07-13 00:45:11,367 - INFO - TradeThread-DOGE - 名义价值: 100.00 USDT, 理论张数: 0.5556, 量化后张数: 0.55
2025-07-13 00:45:11,368 - INFO - TradeThread-DOGE - 开始下单: buy long 0.55张 DOGE-USDT-SWAP @ 0.18
2025-07-13 00:45:12,193 - INFO - TradeThread-DOGE - HTTP Request: POST https://www.okx.com/api/v5/trade/order-algo "HTTP/1.1 200 OK"
2025-07-13 00:45:12,193 - INFO - TradeThread-DOGE - 【交易回调】状态: PLACED, 信息: 开仓单已提交, 数据: {'algoId': '2678862112704962560'}
2025-07-13 00:45:12,194 - CRITICAL - TradeThread-DOGE - 关键错误: 'python-okx'库版本过旧。请运行 'pip install --upgrade python-okx'
2025-07-13 00:45:12,194 - INFO - TradeThread-DOGE - 【交易回调】状态: FATAL_ERROR, 信息: 关键错误: 'python-okx'库版本过旧。请运行 'pip install --upgrade python-okx'
2025-07-13 00:58:31,393 - INFO - MainThread - V6 Final 交易执行器启动...
2025-07-13 00:58:38,840 - INFO - TradeThread-DOGE - 为 DOGE-USDT-SWAP 设置杠杆: 5x, 仓位: long
2025-07-13 00:58:39,124 - INFO - TradeThread-DOGE - HTTP Request: POST https://www.okx.com/api/v5/account/set-leverage "HTTP/2 200 OK"
2025-07-13 00:58:39,124 - ERROR - TradeThread-DOGE - 设置杠杆失败: Cancel isolated margin TP/SL, trailing, trigger, and chase orders or stop bots before adjusting your leverage.
2025-07-13 00:58:39,124 - INFO - TradeThread-DOGE - 【交易回调】状态: ERROR, 信息: 设置杠杆失败, 数据: {'symbol': 'DOGE-USDT-SWAP', 'side': 'buy', 'pos_side': 'long', 'trigger_price': 0.18, 'leverage': '5', 'principal_usdt': 20.0, 'tp_ratio': 0.1, 'sl_ratio': 0.05, 'timeout_minutes': 2.0}
2025-07-13 00:59:23,481 - INFO - MainThread - V6 Final 交易执行器启动...
2025-07-13 00:59:30,912 - INFO - TradeThread-DOGE - 为 DOGE-USDT-SWAP 设置杠杆: 5x, 仓位: long
2025-07-13 00:59:31,228 - INFO - TradeThread-DOGE - HTTP Request: POST https://www.okx.com/api/v5/account/set-leverage "HTTP/2 200 OK"
2025-07-13 00:59:31,993 - INFO - TradeThread-DOGE - HTTP Request: GET https://www.okx.com/api/v5/public/instruments?instType=SWAP&instId=DOGE-USDT-SWAP "HTTP/1.1 200 OK"
2025-07-13 00:59:31,994 - INFO - TradeThread-DOGE - 名义价值: 100.00 USDT, 理论张数: 0.5556, 量化后张数: 0.55
2025-07-13 00:59:31,994 - INFO - TradeThread-DOGE - 开始下单: buy long 0.55张 DOGE-USDT-SWAP @ 0.18
2025-07-13 00:59:32,799 - INFO - TradeThread-DOGE - HTTP Request: POST https://www.okx.com/api/v5/trade/order-algo "HTTP/1.1 200 OK"
2025-07-13 00:59:32,800 - INFO - TradeThread-DOGE - 【交易回调】状态: PLACED, 信息: 开仓单已提交, 数据: {'algoId': '2678890989212925952'}
2025-07-13 00:59:33,805 - INFO - TradeThread-DOGE - HTTP Request: GET https://www.okx.com/api/v5/trade/orders-algo-history?ordType=trigger&algoId=2678890989212925952 "HTTP/1.1 200 OK"
2025-07-13 00:59:44,125 - INFO - TradeThread-DOGE - HTTP Request: GET https://www.okx.com/api/v5/trade/orders-algo-history?ordType=trigger&algoId=2678890989212925952 "HTTP/2 200 OK"
2025-07-13 00:59:54,559 - INFO - TradeThread-DOGE - HTTP Request: GET https://www.okx.com/api/v5/trade/orders-algo-history?ordType=trigger&algoId=2678890989212925952 "HTTP/2 200 OK"
2025-07-13 01:00:05,396 - INFO - TradeThread-DOGE - HTTP Request: GET https://www.okx.com/api/v5/trade/orders-algo-history?ordType=trigger&algoId=2678890989212925952 "HTTP/1.1 200 OK"
2025-07-13 01:00:17,038 - INFO - TradeThread-DOGE - HTTP Request: GET https://www.okx.com/api/v5/trade/orders-algo-history?ordType=trigger&algoId=2678890989212925952 "HTTP/2 200 OK"
2025-07-13 01:00:27,319 - INFO - TradeThread-DOGE - HTTP Request: GET https://www.okx.com/api/v5/trade/orders-algo-history?ordType=trigger&algoId=2678890989212925952 "HTTP/2 200 OK"
2025-07-13 01:00:38,258 - INFO - TradeThread-DOGE - HTTP Request: GET https://www.okx.com/api/v5/trade/orders-algo-history?ordType=trigger&algoId=2678890989212925952 "HTTP/1.1 200 OK"
2025-07-13 01:00:48,536 - INFO - TradeThread-DOGE - HTTP Request: GET https://www.okx.com/api/v5/trade/orders-algo-history?ordType=trigger&algoId=2678890989212925952 "HTTP/2 200 OK"
2025-07-13 01:00:59,321 - INFO - TradeThread-DOGE - HTTP Request: GET https://www.okx.com/api/v5/trade/orders-algo-history?ordType=trigger&algoId=2678890989212925952 "HTTP/1.1 200 OK"
2025-07-13 01:01:10,145 - INFO - TradeThread-DOGE - HTTP Request: GET https://www.okx.com/api/v5/trade/orders-algo-history?ordType=trigger&algoId=2678890989212925952 "HTTP/1.1 200 OK"
2025-07-13 01:01:21,221 - INFO - TradeThread-DOGE - HTTP Request: GET https://www.okx.com/api/v5/trade/orders-algo-history?ordType=trigger&algoId=2678890989212925952 "HTTP/1.1 200 OK"
2025-07-13 01:01:33,083 - INFO - TradeThread-DOGE - HTTP Request: GET https://www.okx.com/api/v5/trade/orders-algo-history?ordType=trigger&algoId=2678890989212925952 "HTTP/1.1 200 OK"
2025-07-13 01:01:43,084 - WARNING - TradeThread-DOGE - 订单 2678890989212925952 超时未成交，正在执行撤单...
2025-07-13 01:01:43,997 - INFO - TradeThread-DOGE - HTTP Request: POST https://www.okx.com/api/v5/trade/cancel-algos "HTTP/1.1 200 OK"
2025-07-13 01:01:43,999 - INFO - TradeThread-DOGE - 【交易回调】状态: TIMEOUT_CANCELED, 信息: 订单超时，已自动撤单, 数据: {'algoId': '2678890989212925952'}
2025-07-13 01:04:30,871 - INFO - MainThread - V7 健壮版交易执行器启动...
2025-07-13 01:04:39,029 - INFO - TradeThread-DOGE - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/1.1 200 OK"
2025-07-13 01:04:39,030 - ERROR - TradeThread-DOGE - 本金不足。需要: 10.0, 可用: 6.7906
2025-07-13 01:04:39,031 - INFO - TradeThread-DOGE - 【交易回调】状态: INSUFFICIENT_FUNDS, 信息: 本金不足。需要: 10.0, 可用: 6.7906, 数据: {'symbol': 'DOGE-USDT-SWAP', 'side': 'buy', 'pos_side': 'long', 'trigger_price': 0.18, 'leverage': '5', 'principal_usdt': 10.0, 'tp_ratio': 0.1, 'sl_ratio': 0.05, 'timeout_minutes': 1.0}
2025-07-13 01:04:47,019 - INFO - TradeThread-DOGE - HTTP Request: GET https://www.okx.com/api/v5/account/balance?ccy=USDT "HTTP/1.1 200 OK"
2025-07-13 01:04:47,020 - ERROR - TradeThread-DOGE - 本金不足。需要: 100000.0, 可用: 6.7906
2025-07-13 01:04:47,020 - INFO - TradeThread-DOGE - 【交易回调】状态: INSUFFICIENT_FUNDS, 信息: 本金不足。需要: 100000.0, 可用: 6.7906, 数据: {'symbol': 'DOGE-USDT-SWAP', 'side': 'buy', 'pos_side': 'long', 'trigger_price': 0.18, 'leverage': '5', 'principal_usdt': 100000.0, 'tp_ratio': None, 'sl_ratio': None, 'timeout_minutes': 1.0}
