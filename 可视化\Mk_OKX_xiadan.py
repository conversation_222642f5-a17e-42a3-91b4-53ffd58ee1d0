
import os
import okx.PublicData as PublicData
import okx.MarketData as MarketData
import okx.Account as Account
import okx.Trade as Trade
from dotenv import load_dotenv
import numpy as np
import math
import time
from datetime import datetime # 导入 datetime 模块用于时间戳转换

# --- 猴子补丁开始 ---
if not hasattr(np, 'NaN'):
    np.NaN = np.nan
# --- 猴子补丁结束 ---

# 用于缓存产品信息，以减少重复API调用开销
_instrument_cache = {}

class OKXTradingHelper:
    """
    OKX 交易辅助类，负责获取产品基础信息、管理缓存和计算下单数量。
    它假设杠杆已在 OKX 平台手动设置。
    """
    def __init__(self, api_key: str, secret_key: str, passphrase: str, flag: str = "1"):
        self.api_key = api_key
        self.secret_key = secret_key
        self.passphrase = passphrase
        self.flag = flag
        self.publicDataAPI = PublicData.PublicAPI(flag=flag)
        self.marketDataAPI = MarketData.MarketAPI(flag=flag)
        self.accountAPI = Account.AccountAPI(api_key, secret_key, passphrase, False, flag) # 保留AccountAPI用于其他潜在账户操作
        self.tradeAPI = Trade.TradeAPI(api_key, secret_key, passphrase, False, flag)

    def _get_instruments_data(self, inst_type: str = None) -> list:
        """内部方法：从 OKX API 获取交易产品基础信息。"""
        try:
            result = self.publicDataAPI.get_instruments(instType=inst_type)
            if result and result.get("code") == "0" and result.get("data"):
                return result["data"]
            return []
        except Exception as e:
            print(f"获取产品信息过程中发生错误: {e}")
            return []

    def load_instruments_to_cache(self, inst_types: list = None):
        """加载指定类型的产品信息到缓存。"""
        if inst_types is None or not inst_types:
            inst_types = ["SPOT", "SWAP"] # 默认加载现货和永续合约

        for itype in inst_types:
            data = self._get_instruments_data(itype)
            for item in data:
                _instrument_cache[item["instId"]] = item

    def get_instrument_info(self, inst_id: str) -> dict:
        """从缓存中获取指定产品ID的基础信息。如果缓存中没有，则尝试从API获取。"""
        if inst_id not in _instrument_cache:
            if inst_id.endswith("-USDT") and not inst_id.endswith("-SWAP"):
                self.load_instruments_to_cache(inst_types=["SPOT"])
            elif inst_id.endswith("-USDT-SWAP"):
                self.load_instruments_to_cache(inst_types=["SWAP"])
            elif inst_id.endswith("-USDT-FUTURES"):
                self.load_instruments_to_cache(inst_types=["FUTURES"])
            else:
                self.load_instruments_to_cache(inst_types=["SPOT", "SWAP", "FUTURES", "MARGIN"])

            if inst_id not in _instrument_cache:
                print(f"未能获取 {inst_id} 的产品信息。")
                return None
        return _instrument_cache.get(inst_id)

    def calculate_order_sz(
        self,
        inst_id: str,
        trade_amount_usd: float,
        order_price: float,
        trade_ccy: str = "USDT" # 这里的 trade_ccy 主要是文档说明，实际计算用 trade_amount_usd
    ) -> str:
        """
        根据下单金额、价格和产品信息计算出符合精度要求的下单数量。
        """
        inst_info = self.get_instrument_info(inst_id)
        if not inst_info:
            print(f"无法计算 {inst_id} 的数量，未能获取产品信息。")
            return None

        inst_type = inst_info.get("instType")
        lot_sz = float(inst_info.get("lotSz"))
        min_sz = float(inst_info.get("minSz"))

        if inst_type == "SPOT":
            calculated_sz = trade_amount_usd / order_price
            adjusted_sz = math.floor(calculated_sz / lot_sz) * lot_sz
            
            if adjusted_sz < min_sz:
                print(f"计算数量 {adjusted_sz} 小于最小下单数量 {min_sz}。请增加下单金额。")
                return None
            return f"{adjusted_sz:.{len(str(lot_sz).split('.')[-1])}f}"

        elif inst_type in ["SWAP", "FUTURES"]:
            ct_val = float(inst_info.get("ctVal"))
            if not ct_val:
                print(f"合约 {inst_id} 缺少 ctVal (合约乘数) 信息，无法计算数量。")
                return None
            
            calculated_sz = trade_amount_usd / (order_price * ct_val)
            adjusted_sz = math.floor(calculated_sz / lot_sz) * lot_sz

            if adjusted_sz < min_sz:
                print(f"计算数量 {adjusted_sz} 小于最小下单数量 {min_sz}。请增加下单金额。")
                return None
            
            return f"{adjusted_sz:.{len(str(lot_sz).split('.')[-1])}f}" if '.' in str(lot_sz) else str(int(adjusted_sz))
            
        else:
            print(f"不支持的产品类型 {inst_type}，无法计算数量。")
            return None

    def get_kline_avg_price(self, inst_id: str) -> float:
        """
        获取1分钟K线数据的最高价和最低价平均值

        Args:
            inst_id (str): 产品ID，如 "DOGE-USDT-SWAP"

        Returns:
            float: 最高价和最低价的平均值，失败时返回 None
        """
        try:
            print(f"正在获取 {inst_id} 的1分钟K线数据...")

            # 使用 get_candlesticks 获取普通K线数据（适用于SWAP等合约）
            print("使用 get_candlesticks 方法获取K线数据...")
            kline_result = self.marketDataAPI.get_candlesticks(
                instId=inst_id,
                bar="1m",
                limit="1"
            )

            print(f"API 响应: {kline_result}")

            if not kline_result or kline_result.get("code") != "0" or not kline_result.get("data"):
                print(f"无法获取 {inst_id} 的K线数据")
                return None

            # 解析K线数据：[时间戳, 开盘价, 最高价, 最低价, 收盘价, 成交量]
            kline_data = kline_result["data"][0]
            high_price = float(kline_data[2])  # 最高价
            low_price = float(kline_data[3])   # 最低价

            # 计算最高价和最低价的平均值
            avg_price = (high_price + low_price) / 2

            print(f"{inst_id} 1分钟K线 - 最高价: {high_price}, 最低价: {low_price}")
            print(f"{inst_id} 计算的平均价格: {avg_price}")

            return avg_price

        except Exception as e:
            print(f"获取K线数据时发生错误: {e}")
            return None

    def place_perp_order(
        self,
        inst_id: str,
        side: str, # 新增：传入 side 参数
        trade_amount_usd: float,
        order_price: float = None,  # 修改：价格参数变为可选，None表示市价单
        td_mode: str = "isolated",
        client_order_id: str = None,
    ) -> dict:
        """
        放置一个永续合约订单。
        如果不传入 order_price 参数，则为市价单；传入价格则为限价单。
        该函数会先计算数量，并假设杠杆已在 OKX 平台手动设置。
        """
        # 判断是否为市价单
        if order_price is None:
            # 市价单：获取K线平均价格
            order_price = self.get_kline_avg_price(inst_id)
            if order_price is None:
                print(f"无法获取 {inst_id} 的K线价格，订单取消。")
                return {"code": "-1", "msg": "Failed to get kline price", "data": []}
            order_type = "market"
            print(f"市价单模式：使用K线平均价格 {order_price}")
        else:
            # 限价单：使用传入的价格
            order_type = "limit"
            print(f"限价单模式：使用指定价格 {order_price}")

        sz = self.calculate_order_sz(inst_id, trade_amount_usd, order_price)
        if sz is None:
            print(f"无法为 {inst_id} 计算下单数量，订单取消。")
            return {"code": "-1", "msg": "Failed to calculate order size", "data": []}

        try:
            order_params = {
                "instId": inst_id,
                "tdMode": td_mode,
                "side": side, # 订单方向
                "ordType": order_type, # 动态设置订单类型：market 或 limit
                "sz": sz,
            }

            # 只有限价单才需要设置价格
            if order_type == "limit":
                order_params["px"] = str(order_price)

            if client_order_id:  # ← 这里检查并添加自定义 clOrdId
                order_params["clOrdId"] = client_order_id
            
            if inst_id.endswith("-SWAP") or inst_id.endswith("-FUTURES"):
                if side == "buy":
                    order_params["posSide"] = "long"
                elif side == "sell":
                    order_params["posSide"] = "short"
            
            print(f"正在放置订单: {inst_id} {side} {sz} @ {order_price} (金额: {trade_amount_usd} USDT)")
            result = self.tradeAPI.place_order(**order_params)
            
            # 优化：提取并返回更多订单信息，包括 side
            if result and result.get("code") == "0" and result.get("data"):
                order_data = result["data"][0]
                return {
                    "success": True,
                    "instId": inst_id, # 产品名称
                    "side": side,      # 订单方向
                    "ordId": order_data.get("ordId"), # 订单编号
                    "clOrdId": order_data.get("clOrdId"), # 客户订单ID
                    "order_timestamp_ms": order_data.get("ts"), # 下单时间戳（毫秒）
                    "message": order_data.get("sMsg", "Order placed successfully"),
                    "raw_response": result # 包含完整原始响应，便于调试
                }
            else:
                return {
                    "success": False,
                    "instId": inst_id,
                    "side": side, # 即使失败也返回方向
                    "message": result.get("msg", "Unknown error") + " | " + result["data"][0].get("sMsg", ""), # 结合sMsg
                    "raw_response": result
                }

        except Exception as e:
            print(f"下单过程中发生错误: {e}")
            return {
                "success": False,
                "instId": inst_id,
                "side": side, # 即使失败也返回方向
                "message": f"Exception during order placement: {e}",
                "raw_response": None
            }

# --- 主函数：测试永续合约开多开空 ---

def main():
    """
    主函数：精简测试永续合约的开多和开空。
    """
    dotenv_path = r'C:\Users\<USER>\Desktop\my_python\claudeV1\量化\123.env'
    if not os.path.exists(dotenv_path):
        print(f"错误: .env 文件不存在于路径: {dotenv_path}")
        print("请确保 .env 文件存在，并包含您的 OKX_API_KEY, OKX_SECRET_KEY, OKX_PASSPHRASE。")
        return
    load_dotenv(dotenv_path=dotenv_path)

    apikey = os.getenv("OKX_API_KEY")
    secretkey = os.getenv("OKX_SECRET_KEY")
    passphrase = os.getenv("OKX_PASSPHRASE")

    if not all([apikey, secretkey, passphrase]):
        print("错误: 无法从环境变量中加载 OKX API 凭据。")
        return

    current_flag = "0" # 示例：设置为实盘，请根据你的 API Key 设定！

    print(f"--- 正在测试永续合约下单 (环境: {'模拟盘' if current_flag == '1' else '实盘'}) ---")

    helper = OKXTradingHelper(apikey, secretkey, passphrase, current_flag)
    helper.load_instruments_to_cache(inst_types=["SWAP"])

    # # --- BTC-USDT-SWAP 开多测试 ---
    # inst_id_btc = "DOGE-USDT-SWAP"
    # side_btc = "buy" # 订单方向
    # trade_amount_btc = 10.0  # 你希望下单的总金额（USDT）
    # order_price_btc_buy = 0.254 # 示例价格，请根据实际市场情况调整
    # client_order_id=str(int(time.time() * 1000) + 100) + os.urandom(2).hex()
    # client_order_id='17532638597679226'

    # print(f"\n--- 尝试放置 {inst_id_btc} {side_btc.upper()} 订单 ---")
    # order_result_btc_buy = helper.place_perp_order(
    #     inst_id=inst_id_btc,
    #     side=side_btc, # 传入 side 参数
    #     trade_amount_usd=trade_amount_btc,
    #     order_price=order_price_btc_buy,
    #     td_mode="isolated",
    #     client_order_id=client_order_id
    # )
    
    # if order_result_btc_buy["success"]:
    #     timestamp_ms = int(order_result_btc_buy["order_timestamp_ms"])
    #     order_time_readable = datetime.fromtimestamp(timestamp_ms / 1000).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
    #     print("\n*** 订单成功！***")
    #     print(f"产品名称 (InstID): {order_result_btc_buy['instId']}")
    #     print(f"订单方向 (Side): {order_result_btc_buy['side'].upper()}") # 打印方向
    #     print(f"订单编号 (OrdID): {order_result_btc_buy['ordId']}")
    #     print(f"客户订单ID (clOrdId): {order_result_btc_buy['clOrdId']}")
    #     print(f"下单时间: {order_time_readable}")
    #     print(f"消息: {order_result_btc_buy['message']}")
    # else:
    #     print("\n*** 订单失败！***")
    #     print(f"产品名称 (InstID): {order_result_btc_buy['instId']}")
    #     print(f"订单方向 (Side): {order_result_btc_buy['side'].upper()}") # 打印方向
    #     print(f"失败原因: {order_result_btc_buy['message']}")
    #     print(f"原始响应: {order_result_btc_buy['raw_response']}")


    # # --- ETH-USDT-SWAP 开空测试 ---
    # inst_id_eth = "DOGE-USDT-SWAP"
    # side_eth = "sell" # 订单方向
    # trade_amount_eth = 10.0
    # order_price_eth_sell = 0.302 # 示例价格，请根据实际市场情况调整
    # client_order_id=str(int(time.time() * 1000) + 100) + os.urandom(2).hex()
    # client_order_id='17532638597679c66'

    # print(f"\n--- 尝试放置 {inst_id_eth} {side_eth.upper()} 订单 ---")
    # order_result_eth_sell = helper.place_perp_order(
    #     inst_id=inst_id_eth,
    #     side=side_eth, # 传入 side 参数
    #     trade_amount_usd=trade_amount_eth,
    #     order_price=order_price_eth_sell,
    #     td_mode="isolated",
    #     client_order_id=client_order_id
    # )

    # if order_result_eth_sell["success"]:
    #     timestamp_ms = int(order_result_eth_sell["order_timestamp_ms"])
    #     order_time_readable = datetime.fromtimestamp(timestamp_ms / 1000).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
    #     print("\n*** 订单成功！***")
    #     print(f"产品名称 (InstID): {order_result_eth_sell['instId']}")
    #     print(f"订单方向 (Side): {order_result_eth_sell['side'].upper()}") # 打印方向
    #     print(f"订单编号 (OrdID): {order_result_eth_sell['ordId']}")
    #     print(f"客户订单ID (clOrdId): {order_result_eth_sell['clOrdId']}")
    #     print(f"下单时间: {order_time_readable}")
    #     print(f"消息: {order_result_eth_sell['message']}")
    # else:
    #     print("\n*** 订单失败！***")
    #     print(f"产品名称 (InstID): {order_result_eth_sell['instId']}")
    #     print(f"订单方向 (Side): {order_result_eth_sell['side'].upper()}") # 打印方向
    #     print(f"失败原因: {order_result_eth_sell['message']}")
    #     print(f"原始响应: {order_result_eth_sell['raw_response']}")

    print("\n" + "="*60)
    print("开始测试市价单功能")
    print("="*60)

    # --- DOGE-USDT-SWAP 开空市价单测试 ---
    inst_id_market_sell = "DOGE-USDT-SWAP"
    side_market_sell = "sell" # 订单方向
    trade_amount_market_sell = 10.0
    client_order_id_market_sell = str(int(time.time() * 1000) + 300) + os.urandom(2).hex()

    print(f"\n--- 尝试放置 {inst_id_market_sell} {side_market_sell.upper()} 市价订单 ---")
    order_result_market_sell = helper.place_perp_order(
        inst_id=inst_id_market_sell,
        side=side_market_sell,
        trade_amount_usd=trade_amount_market_sell,
        # order_price=None,  # 不传入价格参数，自动使用市价
        td_mode="isolated",
        client_order_id=client_order_id_market_sell
    )

    if order_result_market_sell.get("success"):
        timestamp_ms = int(order_result_market_sell["order_timestamp_ms"])
        order_time_readable = datetime.fromtimestamp(timestamp_ms / 1000).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        print("\n*** 市价订单成功！***")
        print(f"产品名称 (InstID): {order_result_market_sell['instId']}")
        print(f"订单方向 (Side): {order_result_market_sell['side'].upper()}")
        print(f"订单类型 (OrdType): {order_result_market_sell.get('ordType', 'market').upper()}")
        print(f"订单编号 (OrdID): {order_result_market_sell['ordId']}")
        print(f"客户订单ID (clOrdId): {order_result_market_sell['clOrdId']}")
        print(f"下单时间: {order_time_readable}")
        print(f"消息: {order_result_market_sell['message']}")
    else:
        print("\n*** 市价订单失败！***")
        print(f"产品名称 (InstID): {order_result_market_sell.get('instId', 'N/A')}")
        print(f"订单方向 (Side): {order_result_market_sell.get('side', 'N/A').upper()}")
        print(f"失败原因: {order_result_market_sell.get('message', 'Unknown error')}")
        print(f"原始响应: {order_result_market_sell.get('raw_response', 'N/A')}")
        print(f"完整返回结果: {order_result_market_sell}")  # 添加调试信息

    # --- DOGE-USDT-SWAP 开多市价单测试 ---
    inst_id_market_buy = "DOGE-USDT-SWAP"
    side_market_buy = "buy" # 订单方向
    trade_amount_market_buy = 10.0  # 你希望下单的总金额（USDT）
    client_order_id_market_buy = str(int(time.time() * 1000) + 400) + os.urandom(2).hex()

    print(f"\n--- 尝试放置 {inst_id_market_buy} {side_market_buy.upper()} 市价订单 ---")
    order_result_market_buy = helper.place_perp_order(
        inst_id=inst_id_market_buy,
        side=side_market_buy,
        trade_amount_usd=trade_amount_market_buy,
        # order_price=None,  # 不传入价格参数，自动使用市价
        td_mode="isolated",
        client_order_id=client_order_id_market_buy
    )

    if order_result_market_buy.get("success"):
        timestamp_ms = int(order_result_market_buy["order_timestamp_ms"])
        order_time_readable = datetime.fromtimestamp(timestamp_ms / 1000).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        print("\n*** 市价订单成功！***")
        print(f"产品名称 (InstID): {order_result_market_buy['instId']}")
        print(f"订单方向 (Side): {order_result_market_buy['side'].upper()}")
        print(f"订单类型 (OrdType): {order_result_market_buy.get('ordType', 'market').upper()}")
        print(f"订单编号 (OrdID): {order_result_market_buy['ordId']}")
        print(f"客户订单ID (clOrdId): {order_result_market_buy['clOrdId']}")
        print(f"下单时间: {order_time_readable}")
        print(f"消息: {order_result_market_buy['message']}")
    else:
        print("\n*** 市价订单失败！***")
        print(f"产品名称 (InstID): {order_result_market_buy.get('instId', 'N/A')}")
        print(f"订单方向 (Side): {order_result_market_buy.get('side', 'N/A').upper()}")
        print(f"失败原因: {order_result_market_buy.get('message', 'Unknown error')}")
        print(f"原始响应: {order_result_market_buy.get('raw_response', 'N/A')}")
        print(f"完整返回结果: {order_result_market_buy}")  # 添加调试信息

    print("\n" + "="*60)
    print("所有测试完成")
    print("="*60)


if __name__ == "__main__":
    main()
