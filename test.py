import sqlite3

def check_table_time_range(db_path, table_name):
    with sqlite3.connect(db_path) as conn:
        cursor = conn.cursor()
        sql = f'SELECT MIN(open_time), MAX(open_time) FROM "{table_name}";'
        cursor.execute(sql)
        result = cursor.fetchone()
        print(f"{table_name} 最早时间: {result[0]}")
        print(f"{table_name} 最晚时间: {result[1]}")
def rename_timestamp_to_open_time(db_path):
    """
    将所有表中的字段timestamp重命名为open_time，保留数据和主键属性。
    """
    with sqlite3.connect(db_path) as conn:
        cursor = conn.cursor()
        # 获取所有用户表名（排除sqlite系统表）
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%';")
        tables = [row[0] for row in cursor.fetchall()]
        for table in tables:
            # 检查表结构中是否有timestamp字段
            cursor.execute(f"PRAGMA table_info('{table}')")
            columns = cursor.fetchall()
            col_names = [col[1] for col in columns]
            if "timestamp" not in col_names:
                continue

            # 构建新表结构，将timestamp换成open_time，其他字段不变
            new_cols = []
            for col in columns:
                name, type_, notnull, default, pk = col[1:6]
                if name == "timestamp":
                    name = "open_time"
                null_str = "NOT NULL" if notnull else ""
                default_str = f"DEFAULT {default}" if default is not None else ""
                pk_str = "PRIMARY KEY" if pk else ""
                new_cols.append(f"{name} {type_} {null_str} {default_str} {pk_str}".strip())
            new_cols_sql = ", ".join(new_cols)

            # 创建临时表
            cursor.execute(f"ALTER TABLE '{table}' RENAME TO '{table}_old'")
            cursor.execute(f"CREATE TABLE '{table}' ({new_cols_sql})")
            # 拷贝数据
            sel_cols = [col if col != "timestamp" else "timestamp AS open_time" for col in col_names]
            sel_cols_sql = ", ".join(sel_cols)
            cursor.execute(f"INSERT INTO '{table}' SELECT {sel_cols_sql} FROM '{table}_old'")
            # 删除旧表
            cursor.execute(f"DROP TABLE '{table}_old'")
            print(f"表 {table} 字段 timestamp 已改为 open_time")
        conn.commit()
        
if __name__ == "__main__":
    # # 修改为你的数据库实际路径
    # db_path = r"C:\Users\<USER>\Desktop\my_python\claudeV1\量化\binance_kline_db\kline.db"
    
    # table_name = "doge1m"
    # check_table_time_range(db_path, table_name)
    
    rename_timestamp_to_open_time("binance_kline_db/kline.db")