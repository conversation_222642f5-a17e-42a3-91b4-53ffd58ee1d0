import os
import okx.Account as Account
import okx.Trade as Trade
from dotenv import load_dotenv
import numpy as np # 导入 numpy 模块，以确保 np.NaN 可用

from MK_OKX_sz import OKXInstrumentInfoManager  ##获取下单sz

# --- 猴子补丁开始 ---
if not hasattr(np, 'NaN'):
    np.NaN = np.nan
# --- 猴子补丁结束 ---

def get_usdt_balance(apikey: str, secretkey: str, passphrase: str, flag: str = "1") -> dict:
    try:
        # 初始化账户 API
        # False 表示不需要代理 (use_proxy)
        accountAPI = Account.AccountAPI(apikey, secretkey, passphrase, False, flag)

        # 查看账户余额
        result = accountAPI.get_account_balance()

        if result and result.get("code") == "0" and result.get("data"):
            # 遍历 details 列表，查找 USDT 的余额信息
            for detail in result["data"][0].get("details", []):
                if detail.get("ccy") == "USDT":
                    # 提取 USDT 的可用余额和冻结余额
                    usdt_avail_bal = detail.get("availBal", "0")
                    usdt_frozen_bal = detail.get("frozenBal", "0")
                    return {
                        "availBal": usdt_avail_bal,
                        "frozenBal": usdt_frozen_bal
                    }
            print("在账户余额中未找到 USDT 信息。")
            return {}
        else:
            print(f"查询账户余额失败: {result.get('msg', '未知错误')}")
            return {}
    except Exception as e:
        print(f"查询过程中发生错误: {e}")
        return {}
    

def get_okx_positions(apikey: str, secretkey: str, passphrase: str, flag: str = "1") -> list:
    """
    查询 OKX 账户的所有持仓信息。

    Args:
        apikey (str): 你的 OKX API Key。
        secretkey (str): 你的 OKX Secret Key。
        passphrase (str): 你的 OKX Passphrase。
        flag (str): 交易环境，"0" 为实盘，"1" 为模拟盘。默认为 "1" (模拟盘)。

    Returns:
        list: 包含所有持仓信息的列表，每个元素是一个持仓字典。
              如果查询失败或没有持仓，则返回空列表。
    """
    try:
        # 初始化账户 API
        # False 表示不需要代理 (use_proxy)
        accountAPI = Account.AccountAPI(apikey, secretkey, passphrase, False, flag)

        # 查看持仓信息
        result = accountAPI.get_positions()

        if result and result.get("code") == "0" and result.get("data"):
            # 返回持仓数据列表
            return result["data"]
        else:
            print(f"查询持仓信息失败: {result.get('msg', '未知错误')}")
            return []
    except Exception as e:
        print(f"查询过程中发生错误: {e}")
        return []


def get_okx_positions_by_coin(apikey: str, secretkey: str, passphrase: str, coin_symbol: str, flag: str = "1") -> list:
    """
    查询 OKX 账户中指定虚拟币的持仓信息。

    Args:
        apikey (str): 你的 OKX API Key。
        secretkey (str): 你的 OKX Secret Key。
        passphrase (str): 你的 OKX Passphrase。
        coin_symbol (str): 虚拟币符号，如 "DOGE"、"BTC"、"ETH" 等。
        flag (str): 交易环境，"0" 为实盘，"1" 为模拟盘。默认为 "1" (模拟盘)。

    Returns:
        list: 包含指定币种持仓信息的列表，每个元素是一个持仓字典。
              如果查询失败或没有该币种持仓，则返回空列表。
    """
    try:
        # 获取所有持仓信息
        all_positions = get_okx_positions(apikey, secretkey, passphrase, flag)

        # 过滤出指定币种的持仓
        coin_positions = []
        for position in all_positions:
            inst_id = position.get('instId', '')
            # 检查是否包含指定的币种符号
            if coin_symbol.upper() in inst_id.upper():
                coin_positions.append(position)

        return coin_positions

    except Exception as e:
        print(f"查询指定币种持仓过程中发生错误: {e}")
        return []


###止盈止损委托单（单方向）
def place_tp_sl_order(
    tradeAPI_instance: Trade.TradeAPI,
    instId: str,
    tdMode: str,
    posSide: str,
    sz: str = None,
    closeFraction: str = None,
    tpTriggerPx: str = None,
    tpOrdPx: str = None,
    slTriggerPx: str = None,
    slOrdPx: str = None,
    Orderprint: bool = True
) -> dict:
    if not (sz or closeFraction):
        raise ValueError("必须提供 sz 或 closeFraction 参数之一。")
    if sz and closeFraction:
        raise ValueError("sz 和 closeFraction 参数不能同时提供。")

    if posSide not in ["long", "short"]:
        raise ValueError("posSide 必须是 'long' 或 'short'。")
    side = "sell" if posSide == "long" else "buy"
    params = {
        "instId": instId,
        "tdMode": tdMode,
        "side": side,
        "posSide": posSide,
        "ordType": "conditional",
        "reduceOnly": True,
    }
    if sz:
        params["sz"] = sz
    elif closeFraction:
        params["closeFraction"] = closeFraction
    # 添加止盈参数（如果提供）
    if tpTriggerPx is not None and tpOrdPx is not None:
        params["tpTriggerPx"] = str(tpTriggerPx)
        params["tpTriggerPxType"] = "last"  # 默认为最新价触发
        params["tpOrdPx"] = str(tpOrdPx)
    elif (tpTriggerPx is not None and tpOrdPx is None) or \
         (tpTriggerPx is None and tpOrdPx is not None):
        raise ValueError("止盈参数 tpTriggerPx 和 tpOrdPx 必须同时提供或同时不提供。")
    # 添加止损参数（如果提供）
    if slTriggerPx is not None and slOrdPx is not None:
        params["slTriggerPx"] = str(slTriggerPx)
        params["slTriggerPxType"] = "last"  # 默认为最新价触发
        params["slOrdPx"] = str(slOrdPx)
    elif (slTriggerPx is not None and slOrdPx is None) or \
         (slTriggerPx is None and slOrdPx is not None):
        raise ValueError("止损参数 slTriggerPx 和 slOrdPx 必须同时提供或同时不提供。")
    # 至少要设置止盈或止损其中一个
    if tpTriggerPx is None and slTriggerPx is None:
        raise ValueError("必须至少提供止盈或止损参数中的一个。")
    result = None
    try:
        # 委托下单
        result = tradeAPI_instance.place_algo_order(**params)
        if Orderprint:
            print(f"\n--- {posSide.capitalize()}仓位 {side.capitalize()}方向委托下单结果 ---")
            print(result)
            if result and 'data' in result and result['data']:
                order_response = result['data'][0]
                status_code = result.get('code', 'N/A')
                status_msg = result.get('msg', 'N/A')
                s_code = order_response.get('sCode', 'N/A')
                s_msg = order_response.get('sMsg', 'N/A')
                algo_id = order_response.get('algoId', 'N/A')
                if status_code == '0' and s_code == '0':
                    print(f"**下单成功!**")
                    print(f"策略订单ID (algoId): {algo_id}")
                else:
                    print(f"**下单失败!**")
                    print(f"错误码 (code): {status_code}, 消息 (msg): {status_msg}")
                    print(f"详细错误码 (sCode): {s_code}, 详细消息 (sMsg): {s_msg}")
            else:
                print(f"下单结果解析失败或数据为空: {result}")

    except Exception as e:
        if Orderprint:
            print(f"下单过程中发生错误: {e}")   
    return result

def main():
    """
    主函数，用于加载环境变量并测试 get_usdt_balance 函数。
    """
    # 指定 .env 文件的路径
    # 请根据你的实际文件路径进行修改
    dotenv_path = r'C:\Users\<USER>\Desktop\my_python\claudeV1\量化\123.env'

    # 检查 .env 文件是否存在并加载
    if os.path.exists(dotenv_path):
        load_dotenv(dotenv_path=dotenv_path)
        print(f"已加载 .env 文件: {dotenv_path}")
    else:
        print(f"错误: .env 文件不存在于路径: {dotenv_path}")
        print("请确保 .env 文件存在，并包含您的 OKX_API_KEY, OKX_SECRET_KEY, OKX_PASSPHRASE。")
        return

    # 从环境变量中获取 API 凭据
    # 确保你的 .env 文件中有这些变量
    apikey = os.getenv("OKX_API_KEY")
    secretkey = os.getenv("OKX_SECRET_KEY")
    passphrase = os.getenv("OKX_PASSPHRASE")

    # 检查凭据是否已加载
    if not all([apikey, secretkey, passphrase]):
        print("错误: 无法从环境变量中加载 OKX API 凭据。")
        print("请确保 .env 文件包含 OKX_API_KEY, OKX_SECRET_KEY, OKX_PASSPHRASE。")
        return
    # 设置交易环境： "0" 为实盘, "1" 为模拟盘
    # 根据你的需求修改此标志
    env_flag = "0" # 例如，设置为模拟盘
    
     # 实例化产品信息管理器  
    info_manager = OKXInstrumentInfoManager(apikey, secretkey, passphrase, env_flag)
    

    
    print(f"\n正在查询 OKX 账户 USDT 余额 (环境: {'模拟盘' if env_flag == '1' else '实盘'})...")
    usdt_balances = get_usdt_balance(apikey, secretkey, passphrase, env_flag)
    if usdt_balances:
        print("\n--- USDT 余额信息 ---")
        print(f"可用余额 (availBal): {usdt_balances['availBal']} USDT")
        print(f"冻结余额 (frozenBal): {usdt_balances['frozenBal']} USDT")
    else:
        print("\n未能获取 USDT 余额信息。")
        
        
        ##########返回持仓信息
    positions = get_okx_positions(apikey, secretkey, passphrase, env_flag)

    ##########返回指定币种持仓信息（DOGE测试）
    print("\n" + "="*50)
    print("测试：获取指定币种持仓信息")
    print("="*50)

    # 测试获取 DOGE 相关的持仓
    doge_positions = get_okx_positions_by_coin(apikey, secretkey, passphrase, "DOGE", env_flag)

    if doge_positions:
        print(f"\n--- 找到 {len(doge_positions)} 个 DOGE 相关持仓 ---")
        for i, pos_data in enumerate(doge_positions):
            print(f"--- DOGE 持仓 {i+1} ---")
            print(f"合约/交易对 (instId): {pos_data.get('instId', 'N/A')}")
            print(f"产品类型 (instType): {pos_data.get('instType', 'N/A')}")
            print(f"持仓数量 (pos): {pos_data.get('pos', 'N/A')}")
            print(f"持仓方向 (posSide): {pos_data.get('posSide', 'N/A')}")
            print(f"平均开仓价格 (avgPx): {pos_data.get('avgPx', 'N/A')}")
            print(f"标记价格 (markPx): {pos_data.get('markPx', 'N/A')}")
            print(f"杠杆倍数 (lever): {pos_data.get('lever', 'N/A')}")

            # 订单编号相关信息
            pos_id = pos_data.get('posId', 'N/A')
            trade_id = pos_data.get('tradeId', 'N/A')
            last_trade_id = pos_data.get('last', 'N/A')

            print(f"**持仓ID (posId): {pos_id}**")
            if trade_id != 'N/A':
                print(f"**最新成交ID (tradeId): {trade_id}**")
            if last_trade_id != 'N/A':
                print(f"**最新成交编号 (last): {last_trade_id}**")

            # 财务信息
            margin = pos_data.get('margin', 'N/A')
            upl = pos_data.get('upl', 'N/A')
            upl_ratio = pos_data.get('uplRatio', 'N/A')

            print(f"**保证金 (Margin): {margin}**")
            print(f"**未实现收益额 (UPL): {upl}**")
            print(f"**未实现收益率 (UPL Ratio): {upl_ratio}**")
            print(f"强平价格 (liqPx): {pos_data.get('liqPx', 'N/A')}")
            print("-" * 30)
    else:
        print("\n未找到 DOGE 相关的持仓信息。")

    if positions:
        print("\n--- OKX 账户持仓信息详情 ---")
        for i, pos_data in enumerate(positions):
            print(f"--- 持仓 {i+1} ---")
            print(f"合约/交易对 (instId): {pos_data.get('instId', 'N/A')}")
            print(f"产品类型 (instType): {pos_data.get('instType', 'N/A')}")
            print(f"持仓数量 (pos): {pos_data.get('pos', 'N/A')}")
            print(f"持仓方向 (posSide): {pos_data.get('posSide', 'N/A')}")
            print(f"平均开仓价格 (avgPx): {pos_data.get('avgPx', 'N/A')}")
            print(f"标记价格 (markPx): {pos_data.get('markPx', 'N/A')}")
            print(f"杠杆倍数 (lever): {pos_data.get('lever', 'N/A')}")

            # 添加订单编号相关信息
            pos_id = pos_data.get('posId', 'N/A')  # 持仓ID
            trade_id = pos_data.get('tradeId', 'N/A')  # 最新成交ID
            last_trade_id = pos_data.get('last', 'N/A')  # 最新价格对应的成交ID

            print(f"**持仓ID (posId): {pos_id}**")
            if trade_id != 'N/A':
                print(f"**最新成交ID (tradeId): {trade_id}**")
            if last_trade_id != 'N/A':
                print(f"**最新成交编号 (last): {last_trade_id}**")

            # 提取和打印保证金、收益额、收益率
            # 注意：这些字段可能因不同产品类型而异，需确保存在
            margin = pos_data.get('margin', 'N/A')
            upl = pos_data.get('upl', 'N/A') # 未实现盈亏，即收益额
            upl_ratio = pos_data.get('uplRatio', 'N/A') # 未实现盈亏比，即收益率

            print(f"**保证金 (Margin): {margin}**")
            print(f"**未实现收益额 (UPL): {upl}**")
            print(f"**未实现收益率 (UPL Ratio): {upl_ratio}**")

            print(f"强平价格 (liqPx): {pos_data.get('liqPx', 'N/A')}")
            print("-" * 25) # 分隔线
    else:
        print("\n未能获取持仓信息，或当前账户无持仓。")
        
    ##size演示    
    Uname = "DOGE-USDT-SWAP"
    TradeMone = 50.0
    BuyPrice = 0.450

    print(f"\n--- 首次获取 {Uname} 信息 (可能触发 API 调用) ---")
    sz_sol = info_manager.calculate_order_sz(Uname, TradeMone, BuyPrice)
    if sz_sol:
        print(f"根据 {TradeMone} USDT 的下单金额和 {BuyPrice} 的价格，{Uname} 的计算数量为: {sz_sol}")
    else:
        print(f"未能计算 {Uname} 的下单数量。")
        
        
        
    ###查询持仓信息
    
    tradeAPI = Trade.TradeAPI(apikey, secretkey, passphrase, False, env_flag)
    # 通过 ordId 查询订单（如果已经取消也会显示）
    client_order_id='17532638597679c66'
    result = tradeAPI.get_order(
        instId=Uname,
        clOrdId=client_order_id
    )
    print(result)
    
    ###获取未成交的委托订单
    
    # 查询所有未触发的单向止盈止损策略订单
    result = tradeAPI.order_algos_list(
        instId= "SUI-USDT-SWAP",
        ordType="conditional"
    )
    # print(result)
    # 检查 'data' 字段是否存在且不为空
    if result and 'data' in result and result['data']:
        order_info = result['data'][0] # 获取第一个订单的信息

        print("--- 订单关键信息 ---")
        print(f"产品ID (Inst ID): {order_info.get('instId', 'N/A')}")
        print(f"产品类型 (Inst Type): {order_info.get('instType', 'N/A')}")
        print(f"订单类型 (Ord Type): {order_info.get('ordType', 'N/A')}")
        print(f"订单状态 (State): {order_info.get('state', 'N/A')}")
        print(f"交易方向 (Side): {order_info.get('side', 'N/A')}")
        print(f"持仓方向 (Pos Side): {order_info.get('posSide', 'N/A')}")
        print(f"数量 (Size): {order_info.get('sz', 'N/A')}")
        print(f"止损触发价格 (SL Trigger Px): {order_info.get('slTriggerPx', 'N/A')}")
        print(f"止损触发价格类型 (SL Trigger Px Type): {order_info.get('slTriggerPxType', 'N/A')}")
        print(f"止损委托价格 (SL Ord Px): {order_info.get('slOrdPx', 'N/A')}")
        print(f"是否只减仓 (Reduce Only): {order_info.get('reduceOnly', 'N/A')}")
        print(f"杠杆倍数 (Lever): {order_info.get('lever', 'N/A')}")
        print(f"交易模式 (Td Mode): {order_info.get('tdMode', 'N/A')}")
        print(f"最新价格 (Last): {order_info.get('last', 'N/A')}")
        print(f"算法ID (Algo ID): {order_info.get('algoId', 'N/A')}")
        print(f"创建时间 (cTime): {order_info.get('cTime', 'N/A')} (Unix毫秒时间戳)")
        print(f"更新时间 (uTime): {order_info.get('uTime', 'N/A')} (Unix毫秒时间戳)")
    else:
        print("在 'result' 字典中未找到订单信息或 'data' 字段为空。")
    ###撤销委托订单
    print('撤销委托订单')
    algo_orders = [
    {"instId": "SUI-USDT-SWAP", "algoId": "2719672174859960320"},
    {"instId": "SUI-USDT-SWAP", "algoId": "2724903329431470080"},
    {"instId": "BTC-USDT", "algoId": "590920138287841222"}    ]
    result = tradeAPI.cancel_algo_order(algo_orders)
    print(result)
    
    
    
    # 多空头--单向止盈止损
    # 1. 多头仓位 - 单独限价止盈
    print("\n--- 示例 1: 多头仓位 - 单独限价止盈 ---")
    place_tp_sl_order(
        tradeAPI_instance=tradeAPI,
        instId="SUI-USDT-SWAP",
        tdMode="isolated",
        posSide="long",
        sz="77", # 平仓 100%
        tpTriggerPx="4.40", # 止盈触发价 (tpTriggerPx) 为 "4.40"，表示当最新价格达到 4.40 时，止盈触发。止盈委托价 (tpOrdPx) 为 "4.41"，表示当止盈触发后，系统将提交一个限价委托，委托价为 4.41，来执行止盈。
        tpOrdPx="4.41", # 止盈委托价 (tpOrdPx) 为 "4.41"，表示当止盈触发后，系统将提交一个限价委托，委托价为 4.41，来执行止盈。止损委托价 (slOrdPx) 为 "-1" 时，它确实表示当止损触发后，系统将提交一个市价委托来执行止损。
        Orderprint=True # 打印结果
    )
    # 2. 多头仓位 - 单独限价止损
    print("\n--- 示例 2: 多头仓位 - 单独限价止损 ---")
    place_tp_sl_order(
        tradeAPI_instance=tradeAPI,
        instId="SUI-USDT-SWAP",
        tdMode="isolated",
        posSide="long",
        sz="77", # 指定数量平仓
        slTriggerPx="4.111",
        slOrdPx="4.1211",
        Orderprint=True # 打印结果
    )
    # # 3. 空头仓位 - 单独限价止盈 (不打印详细结果)
    # print("\n--- 示例 3: 空头仓位 - 单独限价止盈 (不打印) ---")
    # result_short_tp = place_tp_sl_order(
    #     tradeAPI_instance=tradeAPI,
    #     instId="SUI-USDT-SWAP",
    #     tdMode="isolated",
    #     posSide="short",
    #     closeFraction="1",
    #     tpTriggerPx="4.01",
    #     tpOrdPx="4.00",
    #     Orderprint=False # 不打印结果，函数会返回结果字典
    # )
    # print("空头单独止盈委托（不打印）的原始返回结果：")
    # print(result_short_tp)
    # # 4. 空头仓位 - 单独限价止损 (同时设置了止盈止损，虽然只使用止损逻辑)
    # print("\n--- 示例 4: 空头仓位 - 同时设置止盈止损（限价） ---")
    # place_tp_sl_order(
    #     tradeAPI_instance=tradeAPI,
    #     instId="SUI-USDT-SWAP",
    #     tdMode="isolated",
    #     posSide="short",
    #     closeFraction="1",
    #     tpTriggerPx="4.01", # 止盈参数
    #     tpOrdPx="4.00",
    #     slTriggerPx="4.24", # 止损参数
    #     slOrdPx="4.25",
    #     Orderprint=True
    # )
    # 查询所有未成交订单
    result = tradeAPI.get_order_list(
        instId=Uname,
        instType="SWAP"      
    )
    print(result)
    
    ##撤单
    result = tradeAPI.cancel_order(
        instId=Uname, clOrdId = client_order_id)
    print(result)


if __name__ == "__main__":
    main()