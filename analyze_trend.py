import numpy as np
from typing import List, Union, Tuple, Dict
from scipy import stats
import warnings
from trend_analysis_processor import TrendReportParser,UniqueTrendVerifier
warnings.filterwarnings('ignore')

def analyze_trend(data: List[float], malen: int) -> dict:
    """
    分析列表数据的趋势，包含极值拐点判断和回撤分析
    
    参数:
    data: 数值列表
    malen: 检查长度，从后往前的长度
    
    返回:
    包含趋势分析结果的字典
    """
    if len(data) < malen:
        return {
            'error': f'数据长度 {len(data)} 小于检查长度 {malen}'
        }
    
    # 取最后 malen 个数据点
    recent_data = data[-malen:]
    
    # 计算基础趋势指标
    x = np.arange(malen)
    y = np.array(recent_data)
    
    # 线性回归计算斜率
    slope = np.polyfit(x, y, 1)[0]
    
    # 计算相对变化率
    relative_change = (recent_data[-1] - recent_data[0]) / recent_data[0] * 100
    
    # 计算移动平均趋势
    if malen >= 3:
        ma_short = np.mean(recent_data[-3:])
        ma_long = np.mean(recent_data[:3])
        ma_trend = "上升" if ma_short > ma_long else "下降" if ma_short < ma_long else "平稳"
    else:
        ma_trend = "数据点太少"
    
    # 判断总体趋势
    if abs(slope) < 0.01:
        trend = "平稳"
    elif slope > 0:
        trend = "强烈上升" if slope > 0.1 else "缓慢上升"
    else:
        trend = "强烈下降" if slope < -0.1 else "缓慢下降"
    
    # 计算波动性
    volatility = np.std(recent_data)
    
    # === 核心新功能 ===
    
    # 1. 极值拐点分析（可信度）
    extrema_analysis = analyze_extrema_turning_points(recent_data, data)
    
    # 2. 回撤类型和强度分析
    drawdown_analysis = analyze_comprehensive_drawdown(recent_data)
    
    # 3. 趋势确认信号
    trend_confirmation = analyze_trend_confirmation(recent_data)
    
    return {
        # 基础指标
        'trend': trend,
        'slope': slope,
        'relative_change_percent': round(relative_change, 2),
        'moving_average_trend': ma_trend,
        'volatility': round(volatility, 4),
        'start_value': recent_data[0],
        'end_value': recent_data[-1],
        'min_value': min(recent_data),
        'max_value': max(recent_data),
        'data_points': malen,
        
        # 核心新功能
        'extrema_analysis': extrema_analysis,
        'drawdown_analysis': drawdown_analysis,
        'trend_confirmation': trend_confirmation
    }


def analyze_extrema_turning_points(recent_data: List[float], full_data: List[float]) -> Dict:
    """
    分析极值拐点和可信度
    
    核心功能：
    1. 识别局部极值点（峰值/谷值）
    2. 计算拐点可信度评分
    3. 判断当前位置的极值特征
    4. 预测拐点形成概率
    """
    recent_array = np.array(recent_data)
    full_array = np.array(full_data)
    current_value = recent_data[-1]
    
    # === 1. 寻找局部极值点 ===
    def find_extrema_points(data, sensitivity=0.5):
        """寻找局部极值点，sensitivity控制敏感度"""
        data = np.array(data)
        peaks = []
        troughs = []
        
        # 动态窗口大小
        window = max(2, int(len(data) * 0.1))
        
        for i in range(window, len(data) - window):
            left_window = data[i-window:i]
            right_window = data[i+1:i+window+1]
            current = data[i]
            
            # 峰值判断：当前点高于两侧窗口的平均值
            if (current > np.mean(left_window) + sensitivity * np.std(left_window) and
                current > np.mean(right_window) + sensitivity * np.std(right_window)):
                peaks.append({
                    'index': i,
                    'value': current,
                    'strength': min(current - np.mean(left_window), current - np.mean(right_window))
                })
            
            # 谷值判断：当前点低于两侧窗口的平均值
            if (current < np.mean(left_window) - sensitivity * np.std(left_window) and
                current < np.mean(right_window) - sensitivity * np.std(right_window)):
                troughs.append({
                    'index': i,
                    'value': current,
                    'strength': min(np.mean(left_window) - current, np.mean(right_window) - current)
                })
        
        return peaks, troughs
    
    # === 2. 计算拐点可信度 ===
    def calculate_turning_point_credibility(data, extrema_point):
        """计算单个拐点的可信度（0-100分）"""
        idx = extrema_point['index']
        value = extrema_point['value']
        strength = extrema_point['strength']
        
        if idx < 3 or idx >= len(data) - 3:
            return 0
        
        # 因子1: 强度评分（30分）
        max_strength = np.std(data) * 2
        strength_score = min(30, (strength / max_strength) * 30)
        
        # 因子2: 趋势反转评分（40分）
        before_trend = np.mean(np.diff(data[max(0, idx-5):idx+1]))
        after_trend = np.mean(np.diff(data[idx:min(len(data), idx+6)]))
        trend_change = abs(before_trend - after_trend)
        max_trend_change = np.std(np.diff(data)) * 3
        trend_score = min(40, (trend_change / max_trend_change) * 40)
        
        # 因子3: 体积评分（20分）- 极值点周围的价格波动
        volatility_around = np.std(data[max(0, idx-3):min(len(data), idx+4)])
        avg_volatility = np.std(data)
        volume_score = min(20, (volatility_around / avg_volatility) * 20)
        
        # 因子4: 位置评分（10分）- 不在边界
        position_score = 10 if 5 <= idx <= len(data) - 5 else 5
        
        total_credibility = strength_score + trend_score + volume_score + position_score
        return min(100, total_credibility)
    
    # === 3. 分析当前位置极值特征 ===
    # 历史百分位计算
    percentile = stats.percentileofscore(full_array, current_value)
    
    # 识别极值区域
    is_extreme_high = percentile >= 95
    is_extreme_low = percentile <= 5
    is_high_zone = percentile >= 85
    is_low_zone = percentile <= 15
    
    # === 4. 执行分析 ===
    peaks, troughs = find_extrema_points(recent_data)
    
    # 计算所有极值点的可信度
    credible_peaks = []
    credible_troughs = []
    
    for peak in peaks:
        credibility = calculate_turning_point_credibility(recent_data, peak)
        if credibility >= 30:  # 可信度阈值
            credible_peaks.append({
                **peak,
                'credibility': round(credibility, 1),
                'distance_from_end': len(recent_data) - 1 - peak['index'],
                'type': '峰值'
            })
    
    for trough in troughs:
        credibility = calculate_turning_point_credibility(recent_data, trough)
        if credibility >= 30:
            credible_troughs.append({
                **trough,
                'credibility': round(credibility, 1),
                'distance_from_end': len(recent_data) - 1 - trough['index'],
                'type': '谷值'
            })
    
    # 合并并排序所有可信拐点
    all_turning_points = credible_peaks + credible_troughs
    all_turning_points.sort(key=lambda x: x['distance_from_end'])
    
    # === 5. 当前位置拐点预测 ===
    def predict_current_turning_point():
        """预测当前位置形成拐点的概率"""
        if len(recent_data) < 10:
            return 0, "数据不足"
        
        # 检查最近的动量变化
        recent_momentum = np.mean(np.diff(recent_data[-5:]))
        previous_momentum = np.mean(np.diff(recent_data[-10:-5]))
        momentum_change = abs(recent_momentum - previous_momentum)
        
        # 检查是否在极值区域
        extrema_factor = 0
        if is_extreme_high or is_extreme_low:
            extrema_factor = 0.4
        elif is_high_zone or is_low_zone:
            extrema_factor = 0.2
        
        # 检查波动率变化
        recent_volatility = np.std(recent_data[-5:])
        historical_volatility = np.std(recent_data)
        volatility_factor = min(0.3, abs(recent_volatility - historical_volatility) / historical_volatility)
        
        # 综合评分
        turning_probability = min(100, (momentum_change * 100 + extrema_factor * 100 + volatility_factor * 100))
        
        # 判断拐点类型倾向
        if is_extreme_high and recent_momentum < 0:
            prediction_type = "可能形成顶部拐点"
        elif is_extreme_low and recent_momentum > 0:
            prediction_type = "可能形成底部拐点"
        elif momentum_change > np.std(np.diff(recent_data)):
            prediction_type = "趋势可能发生变化"
        else:
            prediction_type = "延续当前趋势"
        
        return round(turning_probability, 1), prediction_type
    
    turning_probability, prediction_type = predict_current_turning_point()
    
    # === 6. 最强拐点识别 ===
    strongest_turning_point = None
    if all_turning_points:
        strongest_turning_point = max(all_turning_points, key=lambda x: x['credibility'])
    
    return {
        'current_percentile': round(percentile, 1),
        'is_extreme_high': is_extreme_high,
        'is_extreme_low': is_extreme_low,
        'is_high_zone': is_high_zone,
        'is_low_zone': is_low_zone,
        
        'turning_points_found': len(all_turning_points),
        'credible_turning_points': all_turning_points[:5],  # 显示前5个最近的
        'strongest_turning_point': strongest_turning_point,
        
        'current_turning_probability': turning_probability,
        'prediction_type': prediction_type,
        
        'extrema_summary': {
            'total_peaks': len(credible_peaks),
            'total_troughs': len(credible_troughs),
            'avg_peak_credibility': round(np.mean([p['credibility'] for p in credible_peaks]), 1) if credible_peaks else 0,
            'avg_trough_credibility': round(np.mean([t['credibility'] for t in credible_troughs]), 1) if credible_troughs else 0
        }
    }


def analyze_comprehensive_drawdown(data: List[float]) -> Dict:
    """
    综合回撤分析：类型、强度、恢复能力
    
    核心功能：
    1. 识别回撤模式和类型
    2. 评估回撤强度等级
    3. 分析恢复能力和韧性
    4. 提供风险评估
    """
    data_array = np.array(data)
    
    # === 1. 计算回撤序列 ===
    peak = np.maximum.accumulate(data_array)
    drawdown = (data_array - peak) / peak * 100
    
    # === 2. 识别回撤期间 ===
    def identify_drawdown_periods(dd_series, threshold=-0.5):
        """识别回撤期间"""
        periods = []
        in_drawdown = False
        start_idx = 0
        
        for i, dd in enumerate(dd_series):
            if dd <= threshold and not in_drawdown:
                in_drawdown = True
                start_idx = i
            elif dd > threshold and in_drawdown:
                in_drawdown = False
                periods.append({
                    'start': start_idx,
                    'end': i - 1,
                    'duration': i - start_idx,
                    'max_drawdown': np.min(dd_series[start_idx:i]),
                    'recovery_speed': (dd_series[i-1] - np.min(dd_series[start_idx:i])) / (i - start_idx) if i > start_idx else 0
                })
        
        # 处理未结束的回撤
        if in_drawdown:
            periods.append({
                'start': start_idx,
                'end': len(dd_series) - 1,
                'duration': len(dd_series) - start_idx,
                'max_drawdown': np.min(dd_series[start_idx:]),
                'recovery_speed': None,
                'ongoing': True
            })
        
        return periods
    
    drawdown_periods = identify_drawdown_periods(drawdown)
    
    # === 3. 回撤强度分类 ===
    max_drawdown = np.min(drawdown)
    
    def classify_drawdown_intensity(max_dd):
        """回撤强度分类"""
        if max_dd > -1:
            return "微弱", 1
        elif max_dd > -3:
            return "轻微", 2
        elif max_dd > -5:
            return "中等", 3
        elif max_dd > -10:
            return "严重", 4
        elif max_dd > -20:
            return "重度", 5
        else:
            return "极端", 6
    
    intensity_label, intensity_level = classify_drawdown_intensity(max_drawdown)
    
    # === 4. 回撤类型分析 ===
    def analyze_drawdown_pattern(periods):
        """分析回撤模式"""
        if not periods:
            return "无明显回撤", "稳定型"
        
        total_periods = len(periods)
        avg_duration = np.mean([p['duration'] for p in periods])
        avg_intensity = np.mean([p['max_drawdown'] for p in periods])
        
        # 持续性分析
        ongoing_periods = sum(1 for p in periods if p.get('ongoing', False))
        
        # 频率分析
        if total_periods == 1:
            if ongoing_periods == 1:
                pattern_type = "单次持续回撤"
                risk_profile = "高风险"
            else:
                pattern_type = "单次完整回撤"
                risk_profile = "中等风险"
        elif avg_duration <= 3:
            pattern_type = "频繁快速回撤"
            risk_profile = "高波动风险"
        elif avg_duration <= 7:
            pattern_type = "周期性回撤"
            risk_profile = "中等风险"
        else:
            pattern_type = "长期深度回撤"
            risk_profile = "高风险"
        
        return pattern_type, risk_profile
    
    pattern_type, risk_profile = analyze_drawdown_pattern(drawdown_periods)
    
    # === 5. 恢复能力分析 ===
    def analyze_recovery_capability(periods):
        """分析恢复能力"""
        if not periods:
            return {"capability": "无需恢复", "score": 100}
        
        completed_periods = [p for p in periods if not p.get('ongoing', False)]
        
        if not completed_periods:
            return {"capability": "恢复中", "score": 0, "note": "当前仍在回撤期"}
        
        recovery_speeds = [p['recovery_speed'] for p in completed_periods if p['recovery_speed'] is not None]
        avg_recovery_speed = np.mean(recovery_speeds) if recovery_speeds else 0
        
        # 恢复能力评分
        if avg_recovery_speed > 1:
            capability = "强恢复能力"
            score = 90
        elif avg_recovery_speed > 0.5:
            capability = "良好恢复能力"
            score = 75
        elif avg_recovery_speed > 0.2:
            capability = "中等恢复能力"
            score = 60
        elif avg_recovery_speed > 0:
            capability = "较弱恢复能力"
            score = 40
        else:
            capability = "恢复困难"
            score = 20
        
        return {
            "capability": capability,
            "score": score,
            "avg_recovery_speed": round(avg_recovery_speed, 3),
            "recovery_consistency": round(np.std(recovery_speeds), 3) if len(recovery_speeds) > 1 else 0
        }
    
    recovery_analysis = analyze_recovery_capability(drawdown_periods)
    
    # === 6. 当前状态评估 ===
    current_drawdown = drawdown[-1]
    is_in_drawdown = current_drawdown < -0.5
    
    # 计算从最近峰值的持续时间
    last_peak_idx = np.where(peak == peak[-1])[0][-1] if len(peak) > 0 else 0
    time_from_peak = len(data) - 1 - last_peak_idx
    
    return {
        'max_drawdown_percent': round(max_drawdown, 2),
        'intensity_label': intensity_label,
        'intensity_level': intensity_level,
        'pattern_type': pattern_type,
        'risk_profile': risk_profile,
        
        'drawdown_periods_count': len(drawdown_periods),
        'drawdown_periods': drawdown_periods,
        
        'recovery_analysis': recovery_analysis,
        
        'current_status': {
            'current_drawdown': round(current_drawdown, 2),
            'is_in_drawdown': is_in_drawdown,
            'time_from_peak': time_from_peak,
            'peak_value': round(peak[-1], 4)
        },
        
        'risk_metrics': {
            'avg_drawdown_duration': round(np.mean([p['duration'] for p in drawdown_periods]), 1) if drawdown_periods else 0,
            'max_drawdown_duration': max([p['duration'] for p in drawdown_periods]) if drawdown_periods else 0,
            'drawdown_frequency': len(drawdown_periods) / len(data) * 100  # 每100个数据点的回撤次数
        }
    }


def analyze_trend_confirmation(data: List[float]) -> Dict:
    """
    趋势确认信号分析
    
    提供多重确认信号来验证趋势的可靠性
    """
    data_array = np.array(data)
    
    # 多时间框架趋势
    short_trend = np.polyfit(range(5), data_array[-5:], 1)[0] if len(data) >= 5 else 0
    medium_trend = np.polyfit(range(10), data_array[-10:], 1)[0] if len(data) >= 10 else 0
    long_trend = np.polyfit(range(len(data)), data_array, 1)[0]
    
    # 趋势一致性评分
    trends = [short_trend, medium_trend, long_trend]
    trend_consistency = sum(1 for t in trends if t * trends[0] > 0) / len(trends) * 100
    
    # 强度评估
    trend_strength = abs(long_trend) / np.std(data_array) * 100
    
    if trend_strength > 5:
        strength_label = "强劲"
    elif trend_strength > 2:
        strength_label = "中等"
    elif trend_strength > 0.5:
        strength_label = "微弱"
    else:
        strength_label = "无明显趋势"
    
    return {
        'trend_consistency_percent': round(trend_consistency, 1),
        'trend_strength': round(trend_strength, 2),
        'strength_label': strength_label,
        'short_term_slope': round(short_trend, 6),
        'medium_term_slope': round(medium_trend, 6),
        'long_term_slope': round(long_trend, 6)
    }


# 示例用法和测试
if __name__ == "__main__":
    # 你提供的数据
    data = [43.86471923, 44.34390555, 44.30328535, 43.85598118, 43.26073257, 42.75761627,
            42.44077626, 42.22406642, 41.92585099, 41.47573658, 41.07244234, 41.05270332,
            41.57118338, 42.47926685, 43.51646228, 44.51375901, 45.3960352, 46.12907484,
            46.74625297, 47.36825055, 48.1745325, 49.42791484, 51.47851438, 54.51704496,
            58.19815958, 61.65869556, 64.11233404, 65.41259986, 65.96449153, 66.21388489,
            66.36681149, 66.49201622, 66.66104525, 66.91583291, 67.20980307, 67.48537964,
            67.78563602, 68.22558369, 68.83960989, 69.46720191, 69.80725865, 69.66944367,
            69.23227993, 68.95985092, 69.17042442, 69.77639822, 70.50779653, 71.21790081,
            71.85604307, 72.31299523, 72.46429556, 72.30204384, 71.90271444, 71.26028423,
            70.20050512, 68.49526288, 66.08005395, 63.1245077, 59.81476834, 56.11387966,
            51.9094854, 47.44443859, 43.4103203, 40.45465016, 38.68225058, 37.73865815,
            37.24894188, 37.03937777, 37.04328799, 37.1864856, 37.38842763, 37.57853652,
            37.68034585, 37.63679188, 37.50016774, 37.49101935, 37.90268867, 38.85961256,
            40.13403406, 41.24007226, 41.76758368, 41.6585128, 41.19347965, 40.75269397,
            40.61715221, 40.9966071, 42.14341879, 44.26030116, 47.21849836, 50.43427807,
            53.10235478, 54.61245019, 54.85504685, 54.21207047, 53.25980126, 52.45861972,
            52.04150213, 52.02636805, 52.23744253, 52.38009222, 52.19720269, 51.5733376,
            50.49645061, 49.00169543, 47.23438667, 45.55001011, 44.44099427, 44.23435613,
            44.80625051, 45.64723046, 46.27488517, 46.58183127, 46.769859, 46.99808704,
            47.15755033, 46.97768202, 46.28860373, 45.13608906, 43.66702356, 41.96779658,
            40.04661216, 37.93604547, 35.77268651, 33.81750473, 32.45256709, 32.10184409,
            33.00708917, 34.994216, 37.50011016, 39.92390279, 42.01746714, 43.96658596,
            46.10731632, 48.58045186, 51.24916462, 53.84265663, 56.06186071, 57.6053739,
            58.29139903, 58.25380561, 57.9505693, 57.86877101, 58.14600214, 58.47445566,
            58.43200007, 57.94042573, 57.3802525, 57.28452116, 57.96827435, 59.34961893,
            60.93862843, 62.0111259]
    
    all_analysis_results = [] # 创建一个空列表来存储所有时间周期的结果
    # 测试不同的malen值
    for malen in [15, 25, 40]:
        print(f"\n{'='*60}")
        print(f"分析最近 {malen} 个数据点的综合趋势")
        print(f"{'='*60}")
        
        result = analyze_trend(data, malen)
        print(result)
        all_analysis_results.append( result ) 

    verifier = UniqueTrendVerifier()
    result = verifier.verify_and_advise(all_analysis_results)
    print("===== 综合趋势检验结果与投资建议 =====")
    print(f"状态: {result['状态']}")
    print(f"综合趋势判断: {result['综合趋势判断']}")
    print(f"建议操作: {result['建议操作']}")
    # print("风险提示:")
    # for risk in result['风险提示']:
    #     print(f"  - {risk}")
    # print("潜在机会:")
    # for opp in result['潜在机会']:
    #     print(f"  - {opp}")
    # print("\n===== 原始解析数据预览 (前3条) =====")
    # for i, data in enumerate(result['原始解析数据'][:3]):
    #     print(f"--- 报告 {i+1} (周期: {data['数据点周期']}) ---")
    #     for k, v in data.items():
    #         print(f"  {k}: {v}")
    #     print("-" * 20)
    # if len(result['原始解析数据']) > 3:
    #     print(f"... 还有 {len(result['原始解析数据']) - 3} 条报告未显示 ...")









        # if 'error' in result:
        #     print(result['error'])
        #     continue
        
        # # 基础趋势信息
        # print(f"📈 基础趋势: {result['trend']} (斜率: {result['slope']:.6f})")
        # print(f"📊 相对变化: {result['relative_change_percent']:.2f}%")
        # print(f"📈 移动平均趋势: {result['moving_average_trend']}")
        
        # # 极值拐点分析
        # ext = result['extrema_analysis']
        # print(f"\n🎯 极值拐点分析:")
        # print(f"   当前百分位: {ext['current_percentile']:.1f}%")
        # print(f"   当前拐点概率: {ext['current_turning_probability']:.1f}%")
        # print(f"   预测类型: {ext['prediction_type']}")
        # print(f"   发现拐点数: {ext['turning_points_found']} 个")
        
        # if ext['strongest_turning_point']:
        #     stp = ext['strongest_turning_point']
        #     print(f"   最强拐点: {stp['type']} (可信度: {stp['credibility']:.1f}%, 距离: {stp['distance_from_end']}点)")
        
        # # 回撤分析
        # dd = result['drawdown_analysis']
        # print(f"\n📉 回撤分析:")
        # print(f"   回撤强度: {dd['intensity_label']} (等级: {dd['intensity_level']}/6)")
        # print(f"   回撤模式: {dd['pattern_type']}")
        # print(f"   风险档案: {dd['risk_profile']}")
        # print(f"   当前回撤: {dd['current_status']['current_drawdown']:.2f}%")
        # print(f"   正在回撤: {'是' if dd['current_status']['is_in_drawdown'] else '否'}")
        
        # recovery = dd['recovery_analysis']
        # print(f"   恢复能力: {recovery['capability']} (评分: {recovery['score']}/100)")
        
        # # 趋势确认
        # tc = result['trend_confirmation']
        # print(f"\n✅ 趋势确认:")
        # print(f"   趋势一致性: {tc['trend_consistency_percent']:.1f}%")
        # print(f"   趋势强度: {tc['strength_label']} ({tc['trend_strength']:.2f})")
        
        # print(f"\n💡 投资建议:")
        # if ext['current_turning_probability'] > 70:
        #     print(f"   ⚠️  高概率拐点区域，建议谨慎操作")
        # if dd['intensity_level'] >= 4:
        #     print(f"   ⚠️  回撤较为严重，注意风险控制")
        # if tc['trend_consistency_percent'] > 80:
        #     print(f"   ✅ 趋势确认度高，可考虑顺势操作")