import os
import okx.PublicData as PublicData
import okx.Account as Account # 虽然这里主要用PublicData，但为了保持与原代码一致性，保留导入
import okx.Trade as Trade # 同样，为了保持一致性，保留导入
from dotenv import load_dotenv
import numpy as np # 尽管这里不直接用，但你的环境中有猴子补丁，所以保留导入
import math
import time # 这里不直接用，但你的环境中有猴子补丁，所以保留导入
from datetime import datetime # 这里不直接用，但你的环境中有猴子补丁，所以保留导入

# --- 猴子补丁开始 ---
# 确保 numpy.NaN 可用，如果你的 numpy 版本没有，则进行兼容性处理
if not hasattr(np, 'NaN'):
    np.NaN = np.nan
# --- 猴子补丁结束 ---

# 用于缓存产品信息，以减少重复API调用开销。
# 使用字典存储，key 为 instId，value 为产品详情字典。
_instrument_cache = {}

class OKXInstrumentInfoManager:
    """
    OKX 产品信息管理类。
    负责从 OKX API 获取和缓存交易产品（如永续合约、现货）的基础信息。
    提供获取产品信息和计算下单数量的功能。
    """
    def __init__(self, api_key: str, secret_key: str, passphrase: str, flag: str = "1"):
        """
        初始化 OKXInstrumentInfoManager。
        参数:
            api_key (str): OKX API Key.
            secret_key (str): OKX Secret Key.
            passphrase (str): OKX Passphrase.
            flag (str): 交易环境标识。"0" 代表实盘，"1" 代表模拟盘。
        """
        # 即使这里主要使用 PublicDataAPI，我们也需要完整的 API 凭证来初始化 PublicAPI，
        # 因为某些 PublicAPI 调用可能需要认证，并且保持与原始 OKX SDK 接口一致。
        self.publicDataAPI = PublicData.PublicAPI(flag=flag)
        self.api_key = api_key # 存储这些是为了可能的未来扩展或调试
        self.secret_key = secret_key
        self.passphrase = passphrase
        self.flag = flag

    def _fetch_instruments_from_api(self, inst_type: str = None) -> list:
        """
        内部方法：从 OKX API 获取指定类型的产品基础信息。
        参数:
            inst_type (str, optional): 产品类型，例如 "SPOT", "SWAP", "FUTURES"。
                                       如果为 None，则返回所有类型。
        返回:
            list: 产品信息列表，每个元素是一个字典。如果失败则返回空列表。
        """
        try:
            print(f"正在从 OKX API 获取 {inst_type if inst_type else '所有'} 产品信息...")
            # 确保 PublicAPI 初始化时携带了必要的认证信息，
            # 尽管对于 get_instruments 而言通常不是强制的，但考虑到鲁棒性，以及OKX SDK的惯例。
            # 这里我们使用 OKX SDK 推荐的 PublicAPI 实例化方式。
            # 注意：某些公共接口不需要 API key，但为了统一性，我们依然初始化。
            result = PublicData.PublicAPI(flag=self.flag).get_instruments(instType=inst_type)
            
            if result and result.get("code") == "0" and result.get("data"):
                return result["data"]
            
            print(f"未能从 API 获取 {inst_type if inst_type else '所有'} 产品信息。响应: {result.get('msg', '无消息')}")
            return []
        except Exception as e:
            print(f"获取产品信息过程中发生错误: {e}")
            return []

    def load_instruments_to_cache(self, inst_types: list = None):
        """
        加载指定类型的产品信息到内存缓存。
        这个方法会清除现有缓存中对应类型的数据，并重新从 API 获取。
        参数:
            inst_types (list, optional): 要加载的产品类型列表，例如 ["SPOT", "SWAP"]。
                                         如果为 None 或空列表，默认加载 "SPOT" 和 "SWAP"。
        """
        if inst_types is None or not inst_types:
            inst_types = ["SPOT", "SWAP"] # 默认加载现货和永续合约

        print(f"正在加载以下产品类型到缓存: {', '.join(inst_types)}")
        for itype in inst_types:
            # 清除该类型在缓存中的旧数据，防止重复或过期
            # 这里简单粗暴地重新加载，更精细的控制可以只更新或删除特定项
            # For simplicity, we just fetch all and update.
            data = self._fetch_instruments_from_api(itype)
            for item in data:
                _instrument_cache[item["instId"]] = item
        print(f"产品信息缓存更新完成。当前缓存中包含 {len(_instrument_cache)} 个产品。")


    def get_instrument_info(self, inst_id: str) -> dict:
        """
        从缓存中获取指定产品ID的基础信息。如果缓存中没有，则尝试从API获取并更新缓存。
        参数:
            inst_id (str): 产品ID，例如 "BTC-USDT-SWAP"。
        返回:
            dict: 产品信息字典。如果未能获取，则返回 None。
        """
        if inst_id in _instrument_cache:
            return _instrument_cache.get(inst_id)
        
        # 如果缓存中没有，尝试根据 inst_id 的后缀猜测类型并加载到缓存
        print(f"缓存中未找到 {inst_id}，尝试从 API 获取并更新缓存。")
        inst_type_to_fetch = None
        if inst_id.endswith("-USDT") and not inst_id.endswith("-SWAP") and not inst_id.endswith("-FUTURES"):
            inst_type_to_fetch = "SPOT"
        elif inst_id.endswith("-USDT-SWAP"):
            inst_type_to_fetch = "SWAP"
        elif inst_id.endswith("-USDT-FUTURES"):
            inst_type_to_fetch = "FUTURES"
        # 对于其他类型（如MARGIN或不知道的类型），我们尝试加载所有常用类型
        else:
            inst_type_to_fetch = ["SPOT", "SWAP", "FUTURES", "MARGIN"]

        if isinstance(inst_type_to_fetch, list):
            self.load_instruments_to_cache(inst_type_to_fetch)
        else:
            self.load_instruments_to_cache([inst_type_to_fetch])

        if inst_id not in _instrument_cache:
            print(f"未能获取 {inst_id} 的产品信息，即使尝试从 API 更新后。")
            return None
        
        return _instrument_cache.get(inst_id)

    def calculate_order_sz(
        self,
        inst_id: str,
        trade_amount_usd: float,
        order_price: float,
    ) -> str:
        """
        根据下单金额、价格和产品信息计算出符合精度要求的下单数量。
        参数:
            inst_id (str): 产品ID，例如 "BTC-USDT-SWAP"。
            trade_amount_usd (float): 你希望下单的总金额（以 USD 或 USDT 计价）。
            order_price (float): 下单的价格。
        返回:
            str: 格式化后的下单数量字符串，如果无法计算则返回 None。
        """
        if order_price <= 0:
            print(f"下单价格必须大于0，当前价格: {order_price}")
            return None

        inst_info = self.get_instrument_info(inst_id)
        if not inst_info:
            print(f"无法计算 {inst_id} 的数量，未能获取产品信息。")
            return None

        inst_type = inst_info.get("instType")
        lot_sz = float(inst_info.get("lotSz"))
        min_sz = float(inst_info.get("minSz"))

        calculated_sz = 0.0
        if inst_type == "SPOT":
            calculated_sz = trade_amount_usd / order_price
        elif inst_type in ["SWAP", "FUTURES"]:
            ct_val = float(inst_info.get("ctVal", 0)) # 默认为0，避免NoneType错误
            if ct_val <= 0:
                print(f"合约 {inst_id} 缺少有效 ctVal (合约乘数) 信息 ({ct_val})，无法计算数量。")
                return None
            calculated_sz = trade_amount_usd / (order_price * ct_val)
        else:
            print(f"不支持的产品类型 {inst_type}，无法计算数量。")
            return None

        # 向下取整到 lot_sz 的倍数
        adjusted_sz = math.floor(calculated_sz / lot_sz) * lot_sz

        if adjusted_sz < min_sz:
            print(f"计算数量 {adjusted_sz} 小于最小下单数量 {min_sz} (minSz: {min_sz})。请增加下单金额。")
            return None
        
        # 格式化数量，保留 lot_sz 的小数位数
        if '.' in str(lot_sz):
            decimal_places = len(str(lot_sz).split('.')[-1])
            return f"{adjusted_sz:.{decimal_places}f}"
        else:
            return str(int(adjusted_sz))


# --- 示例用法 (在一个单独的文件中，比如 `your_main_script.py`) ---
if __name__ == "__main__":
    """
    这个 main 函数仅用于演示 OKXInstrumentInfoManager 的功能。
    在实际项目中，它应该被你的交易策略或其他模块调用。
    """
    dotenv_path = r'C:\Users\<USER>\Desktop\my_python\claudeV1\量化\123.env'
    if not os.path.exists(dotenv_path):
        print(f"错误: .env 文件不存在于路径: {dotenv_path}")
        print("请确保 .env 文件存在，并包含您的 OKX_API_KEY, OKX_SECRET_KEY, OKX_PASSPHRASE。")
        exit() # 直接退出，因为没有凭据无法进行API调用
    load_dotenv(dotenv_path=dotenv_path)

    apikey = os.getenv("OKX_API_KEY")
    secretkey = os.getenv("OKX_SECRET_KEY")
    passphrase = os.getenv("OKX_PASSPHRASE")

    if not all([apikey, secretkey, passphrase]):
        print("错误: 无法从环境变量中加载 OKX API 凭据。请检查 .env 文件。")
        exit()

    current_flag = "0" # "0" 为实盘，"1" 为模拟盘。请根据您的 API Key 选择。
    print(f"--- 正在测试 OKX 产品信息管理 (环境: {'模拟盘' if current_flag == '1' else '实盘'}) ---")

    # 实例化产品信息管理器
    info_manager = OKXInstrumentInfoManager(apikey, secretkey, passphrase, current_flag)

    # 示例1: 获取 BTC-USDT-SWAP 的信息并计算数量
    inst_id_btc = "BTC-USDT-SWAP"
    trade_amount_btc = 100.0 # 假设你希望用 100 USDT 的价值开仓
    current_price_btc = 65000.0 # 假设 BTC 当前价格

    print(f"\n--- 获取 {inst_id_btc} 信息并计算下单数量 ---")
    sz_btc = info_manager.calculate_order_sz(inst_id_btc, trade_amount_btc, current_price_btc)
    if sz_btc:
        print(f"根据 {trade_amount_btc} USDT 的下单金额和 {current_price_btc} 的价格，{inst_id_btc} 的计算数量为: {sz_btc}")
    else:
        print(f"未能计算 {inst_id_btc} 的下单数量。")

    # 示例2: 获取 ETH-USDT (现货) 的信息并计算数量
    inst_id_eth_spot = "ETH-USDT"
    trade_amount_eth_spot = 50.0 # 假设你希望用 50 USDT 购买现货
    current_price_eth_spot = 3500.0 # 假设 ETH 现货当前价格

    print(f"\n--- 获取 {inst_id_eth_spot} 信息并计算下单数量 ---")
    sz_eth_spot = info_manager.calculate_order_sz(inst_id_eth_spot, trade_amount_eth_spot, current_price_eth_spot)
    if sz_eth_spot:
        print(f"根据 {trade_amount_eth_spot} USDT 的下单金额和 {current_price_eth_spot} 的价格，{inst_id_eth_spot} 的计算数量为: {sz_eth_spot}")
    else:
        print(f"未能计算 {inst_id_eth_spot} 的下单数量。")

    # 示例3: 尝试获取一个可能一开始不在缓存中的产品，触发API调用
    inst_id_sol_swap = "SOL-USDT-SWAP"
    trade_amount_sol = 20.0
    current_price_sol = 150.0

    print(f"\n--- 首次获取 {inst_id_sol_swap} 信息 (可能触发 API 调用) ---")
    sz_sol = info_manager.calculate_order_sz(inst_id_sol_swap, trade_amount_sol, current_price_sol)
    if sz_sol:
        print(f"根据 {trade_amount_sol} USDT 的下单金额和 {current_price_sol} 的价格，{inst_id_sol_swap} 的计算数量为: {sz_sol}")
    else:
        print(f"未能计算 {inst_id_sol_swap} 的下单数量。")

    # 再次获取 SOL-USDT-SWAP 信息，此时应该从缓存中读取，不会再触发 API 调用
    print(f"\n--- 再次获取 {inst_id_sol_swap} 信息 (应从缓存读取) ---")
    sol_info_cached = info_manager.get_instrument_info(inst_id_sol_swap)
    if sol_info_cached:
        print(f"成功从缓存中获取 {inst_id_sol_swap} 信息，lotSz: {sol_info_cached.get('lotSz')}")
        
      # 示例3: 尝试获取一个可能一开始不在缓存中的产品，触发API调用
    inst_id_sol_swap = "DOGE-USDT-SWAP"
    trade_amount_sol = 20.0
    current_price_sol = 150.0

    print(f"\n--- 首次获取 {inst_id_sol_swap} 信息 (可能触发 API 调用) ---")
    sz_sol = info_manager.calculate_order_sz(inst_id_sol_swap, trade_amount_sol, current_price_sol)
    if sz_sol:
        print(f"根据 {trade_amount_sol} USDT 的下单金额和 {current_price_sol} 的价格，{inst_id_sol_swap} 的计算数量为: {sz_sol}")
    else:
        print(f"未能计算 {inst_id_sol_swap} 的下单数量。")
          # 示例3: 尝试获取一个可能一开始不在缓存中的产品，触发API调用
    inst_id_sol_swap = "DOGE-USDT-SWAP"
    trade_amount_sol = 50.0
    current_price_sol = 0.450

    print(f"\n--- 首次获取 {inst_id_sol_swap} 信息 (可能触发 API 调用) ---")
    sz_sol = info_manager.calculate_order_sz(inst_id_sol_swap, trade_amount_sol, current_price_sol)
    if sz_sol:
        print(f"根据 {trade_amount_sol} USDT 的下单金额和 {current_price_sol} 的价格，{inst_id_sol_swap} 的计算数量为: {sz_sol}")
    else:
        print(f"未能计算 {inst_id_sol_swap} 的下单数量。")