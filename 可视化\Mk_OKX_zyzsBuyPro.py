#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多重确认阶段性止盈止损策略 (单次交易回测模型)
作者: AI Assistant
版本: 2.4 (基于LiveTradingStrategy v2.3，封装单次交易回测函数)
适用场景: 提供一个价格序列，执行一次完整的、基于K线最低价的多头策略回测
"""

import numpy as np
import pandas as pd
# import matplotlib.pyplot as plt # 如果不需要绘图，可以注释掉
from typing import Dict, Optional, List
from datetime import datetime
import json
import warnings
warnings.filterwarnings('ignore', category=UserWarning)

# --- 猴子补丁开始 ---
if not hasattr(np, 'NaN'):
    np.NaN = np.nan
# --- 猴子补丁结束 ---

# IMPORTANT: This `load_json` is a placeholder for demonstration purposes.
# When running locally, ensure your actual `MkKu` module and `load_json` function are imported.
# Please DELETE or COMMENT OUT this placeholder function and uncomment your original import:
# from MkKu import load_json 


class LiveTradingStrategy: # 改回 LiveTradingStrategy 名字，以便区分，但内部逻辑是DynamicStopLossStrategy的修正版
    """
    实盘交易多重确认阶段性止盈止损策略类 (仅用于多头)
    
    核心思想：
    - 阶段0: 风险期，宽松止损等待确认
    - 阶段1: 确认期，收紧止损
    - 阶段2: 安全期，止损移至盈利区
    - 阶段3: 跟踪期，动态跟踪止损
    
    特点：
    - 支持逐个价格点处理
    - 维持内部状态
    - 支持状态保存和恢复
    - 详细的交易日志
    - **明确只支持多头（做多）策略，并按照K线最低价进行严格回测**
    """
    
    def __init__(self, 
                 stage1_threshold: float = 0.005,   # 进入阶段1的盈利阈值 (0.5%)
                 stage2_threshold: float = 0.015,   # 进入阶段2的盈利阈值 (1.5%)
                 stage3_threshold: float = 0.025,   # 进入阶段3的盈利阈值 (2.5%)
                 initial_stop_loss_ratio: float = 0.015, # 初始止损比例 (1.5%)
                 stage1_stop_loss_ratio: float = 0.005,  # 阶段1止损比例 (0.5%)
                 stage2_stop_profit_ratio: float = 0.005, # 阶段2止损设在盈利区 (0.5%)
                 trailing_ratio: float = 0.3,       # 跟踪止损回撤比例 (30%)
                 min_profit_lock_ratio: float = 0.002,   # 最小利润锁定 (0.2%)
                 enable_logging: bool = True):      # 启用详细日志
        """
        初始化策略参数
        
        Args:
            stage1_threshold: 进入阶段1的盈利阈值
            stage2_threshold: 进入阶段2的盈利阈值
            stage3_threshold: 进入阶段3的盈利阈值
            initial_stop_loss_ratio: 初始止损比例
            stage1_stop_loss_ratio: 阶段1止损比例
            stage2_stop_profit_ratio: 阶段2止损设在盈利区
            trailing_ratio: 跟踪止损回撤比例
            min_profit_lock_ratio: 最小利润锁定比例
            enable_logging: 是否启用详细日志
        """
        # 策略参数
        self.stage1_threshold = stage1_threshold
        self.stage2_threshold = stage2_threshold
        self.stage3_threshold = stage3_threshold
        self.initial_stop_loss_ratio = initial_stop_loss_ratio
        self.stage1_stop_loss_ratio = stage1_stop_loss_ratio
        self.stage2_stop_profit_ratio = stage2_stop_profit_ratio
        self.trailing_ratio = trailing_ratio
        self.min_profit_lock_ratio = min_profit_lock_ratio
        self.enable_logging = enable_logging
        
        # 交易状态
        self.reset()
    
    def reset(self):
        """重置策略状态"""
        self.position = False
        self.entry_price = 0.0
        self.current_stage = 0
        self.stop_loss = 0.0
        self.high_water_mark = 0.0 # 仅用于多头，追踪最高价
        self.entry_time = None
        self.tick_count = 0
        
        # 历史记录
        self.price_history = []
        self.stop_loss_history = []
        self.stage_history = []
        self.profit_history = []
        self.signal_history = []
        
        # 交易日志
        self.trade_log = []
        # 新增一个列表来存储每次平仓的最终盈利百分比
        self.closed_trade_profits = []
        
        if self.enable_logging:
            self._log("策略状态已重置 (多头)")
    
    def _log(self, message: str, level: str = "INFO"):
        """内部日志记录"""
        if self.enable_logging:
            timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
            log_entry = {
                'timestamp': timestamp,
                'tick': self.tick_count,
                'level': level,
                'message': message
            }
            self.trade_log.append(log_entry)
            # print(f"[{timestamp}] {level}: {message}") # 在模拟中不频繁打印，只在重要事件打印
    
    def enter_position(self, price: float, signal_info: Optional[Dict] = None) -> Dict:
        """
        开仓操作 (多头)
        
        Args:
            price: 开仓价格
            signal_info: 信号信息 (可选)
            
        Returns:
            Dict: 开仓结果信息
        """
        if self.position:
            self._log("警告: 已有持仓，无法重复开仓", "WARNING")
            return {'success': False, 'reason': 'ALREADY_IN_POSITION', 'message': '已有持仓，无法重复开仓'}
        
        # 设置交易状态
        self.position = True
        self.entry_price = price
        self.current_stage = 0
        self.stop_loss = price * (1 - self.initial_stop_loss_ratio) # 多头止损在下方
        self.high_water_mark = price # 多头追踪最高价
        self.entry_time = datetime.now()
        self.tick_count = 0
        
        # 重置历史记录
        self.price_history = [price]
        self.stop_loss_history = [self.stop_loss]
        self.stage_history = [0]
        self.profit_history = [0.0]
        
        result = {
            'success': True,
            'action': 'ENTER',
            'position_type': 'LONG', 
            'price': price,
            'stop_loss': self.stop_loss,
            'stage': self.current_stage,
            'profit_pct': 0.0,
            'entry_time': self.entry_time,
            'signal_info': signal_info or {},
            'message': f"开仓成功: 价格={price:.6f}, 止损={self.stop_loss:.6f}"
        }
        
        self._log(f"开仓成功 (多头): 价格={price:.6f}, 止损={self.stop_loss:.6f}")
        return result
    
    def process_tick(self, price: float, timestamp: Optional[datetime] = None) -> Dict:
        """
        处理单个价格tick (实盘核心函数，仅用于多头)
        
        Args:
            price: 当前价格
            timestamp: 时间戳 (可选)
            
        Returns:
            Dict: 处理结果
        """
        self.tick_count += 1
        
        if not self.position:
            # 返回一个明确无持仓的结果，包含 action 和 message
            return {
                'success': False,
                'action': 'NO_POSITION',
                'price': price,
                'message': '当前无持仓' 
            }
        
        # 更新价格历史
        self.price_history.append(price)
        
        # 更新最高水位 (仅多头，严格按照传入价格更新，符合最低价回测逻辑)
        old_hwm = self.high_water_mark
        self.high_water_mark = max(self.high_water_mark, price) 
        
        # 计算当前盈利 (多头)
        current_profit = (price - self.entry_price) / self.entry_price
        profit_pct = current_profit * 100
        
        # 检查阶段升级
        old_stage = self.current_stage
        self._update_stage(current_profit) # 阶段更新仅依赖盈利百分比
        
        # 更新止损
        old_stop_loss = self.stop_loss
        self._update_stop_loss(current_profit) # 止损更新仅依赖盈利百分比和高水位
        
        # 记录历史
        self.stop_loss_history.append(self.stop_loss)
        self.stage_history.append(self.current_stage)
        self.profit_history.append(profit_pct)
        
        # 检查止损触发 (多头)
        if price <= self.stop_loss: 
            return self._exit_position(price, 'STOP_LOSS', timestamp)
        
        # 构建结果
        result = {
            'success': True,
            'action': 'HOLD', # 当持有仓位时，action 是 HOLD
            'position_type': 'LONG',
            'price': price,
            'profit_pct': profit_pct,
            'stop_loss': self.stop_loss,
            'stage': self.current_stage,
            'high_water_mark': self.high_water_mark,
            'tick_count': self.tick_count,
            'stage_changed': old_stage != self.current_stage,
            'stop_loss_changed': abs(old_stop_loss - self.stop_loss) > 1e-8,
            'hwm_updated': self.high_water_mark > old_hwm,
            'message': '继续持有' # 为 HOLD 动作添加一个默认 message
        }
        
        # 记录重要变化
        if result['stage_changed']:
            self._log(f"阶段升级: {old_stage} -> {self.current_stage}, 价格={price:.6f}, 盈利={profit_pct:.2f}%")
        
        if result['stop_loss_changed']:
            self._log(f"止损更新: {old_stop_loss:.6f} -> {self.stop_loss:.6f}")
        
        if result['hwm_updated']:
            self._log(f"高水位更新: {old_hwm:.6f} -> {self.high_water_mark:.6f}")
            
        return result
    
    def _update_stage(self, profit: float):
        """更新交易阶段 (仅多头)"""
        if self.current_stage == 0 and profit >= self.stage1_threshold:
            self.current_stage = 1
        elif self.current_stage == 1 and profit >= self.stage2_threshold:
            self.current_stage = 2
        elif self.current_stage == 2 and profit >= self.stage3_threshold:
            self.current_stage = 3
    
    def _update_stop_loss(self, profit: float):
        """更新止损价格 (仅多头)"""
        # 止损逻辑只针对多头
        
        if self.current_stage == 0:
            # 阶段0: 保持初始止损
            pass # 初始止损已在开仓时设置
        elif self.current_stage == 1:
            # 阶段1: 收紧止损
            new_stop = self.entry_price * (1 - self.stage1_stop_loss_ratio)
            self.stop_loss = max(self.stop_loss, new_stop) # 多头止损向上收紧
        elif self.current_stage == 2:
            # 阶段2: 止损移至盈利区
            new_stop = self.entry_price * (1 + self.stage2_stop_profit_ratio)
            self.stop_loss = max(self.stop_loss, new_stop) # 多头止损向上收紧
        elif self.current_stage == 3:
            # 阶段3: 跟踪止损
            # high_water_mark 已经是根据严格最低价更新的
            trailing_distance = (self.high_water_mark - self.entry_price) * self.trailing_ratio
            new_stop = self.high_water_mark - trailing_distance
            # 确保止损不低于最小利润锁定
            min_profit_stop = self.entry_price * (1 + self.min_profit_lock_ratio)
            self.stop_loss = max(self.stop_loss, new_stop, min_profit_stop) 
    
    def manual_exit(self, price: float, reason: str = 'MANUAL', timestamp: Optional[datetime] = None) -> Dict:
        """
        手动平仓 (多头)
        
        Args:
            price: 平仓价格
            reason: 平仓原因
            timestamp: 时间戳
            
        Returns:
            Dict: 平仓结果
        """
        if not self.position:
            return {
                'success': False,
                'action': 'NO_POSITION',
                'message': '当前无持仓' 
            }
        
        return self._exit_position(price, reason, timestamp)
    
    def _exit_position(self, price: float, reason: str, timestamp: Optional[datetime] = None) -> Dict:
        """内部平仓处理 (仅多头)"""
        if not self.position:
            return {'success': False, 'message': '无持仓'} 
        
        # 计算最终收益和最大浮盈 (仅多头)
        final_profit = (price - self.entry_price) / self.entry_price
        max_profit = (self.high_water_mark - self.entry_price) / self.entry_price
        
        # 计算持仓时间
        exit_time = timestamp or datetime.now()
        holding_duration = exit_time - self.entry_time if self.entry_time else None
        
        result = {
            'success': True,
            'action': 'EXIT', # 当平仓时，action 是 EXIT
            'reason': reason,
            'position_type': 'LONG',
            'price': price,
            'stock_name': 'N/A', # 示例占位符
            'entry_price': self.entry_price,
            'final_profit_pct': final_profit * 100,
            'max_profit_pct': max_profit * 100,
            'final_stage': self.current_stage,
            'total_ticks': self.tick_count,
            'holding_duration': str(holding_duration), # 转换为字符串以便序列化
            'exit_time': exit_time.isoformat(),
            'high_water_mark': self.high_water_mark,
            'message': f"平仓：原因={reason}, 收益={final_profit*100:.2f}%" 
        }
        
        # 将本次交易的最终盈利添加到列表中
        self.closed_trade_profits.append(final_profit * 100)

        # 重置状态
        self.position = False
        
        # 记录日志
        self._log(f"平仓 (多头): 原因={reason}, 价格={price:.6f}, 收益={final_profit*100:.2f}%, 最大浮盈={max_profit*100:.2f}%")
        
        return result
    
    def get_current_status(self) -> Dict:
        """
        获取当前状态信息 (仅多头)
        
        Returns:
            Dict: 当前状态
        """
        if not self.position:
            return {
                'has_position': False,
                'message': '当前无持仓'
            }
        
        current_price = self.price_history[-1] if self.price_history else self.entry_price
        current_profit = (current_price - self.entry_price) / self.entry_price * 100
        
        # 计算距离止损的百分比
        distance_to_stop = ((current_price - self.stop_loss) / current_price * 100) if current_price > 0 else 0

        return {
            'has_position': True,
            'position_type': 'LONG',
            'entry_price': self.entry_price,
            'current_price': current_price,
            'profit_pct': current_profit,
            'stop_loss': self.stop_loss,
            'current_stage': self.current_stage,
            'high_water_mark': self.high_water_mark,
            'tick_count': self.tick_count,
            'entry_time': self.entry_time.isoformat() if self.entry_time else None,
            'distance_to_stop_pct': distance_to_stop
        }
    
    def save_state(self, filepath: str):
        """
        保存策略状态到文件 (仅多头)
        
        Args:
            filepath: 保存路径
        """
        state = {
            'position': self.position,
            'entry_price': self.entry_price,
            'current_stage': self.current_stage,
            'stop_loss': self.stop_loss,
            'high_water_mark': self.high_water_mark,
            'entry_time': self.entry_time.isoformat() if self.entry_time else None,
            'tick_count': self.tick_count,
            'price_history': self.price_history,
            'stop_loss_history': self.stop_loss_history,
            'stage_history': self.stage_history,
            'profit_history': self.profit_history,
            'closed_trade_profits': self.closed_trade_profits, # 保存已平仓交易的盈亏
            'parameters': {
                'stage1_threshold': self.stage1_threshold,
                'stage2_threshold': self.stage2_threshold,
                'stage3_threshold': self.stage3_threshold,
                'initial_stop_loss_ratio': self.initial_stop_loss_ratio,
                'stage1_stop_loss_ratio': self.stage1_stop_loss_ratio,
                'stage2_stop_profit_ratio': self.stage2_stop_profit_ratio,
                'trailing_ratio': self.trailing_ratio,
                'min_profit_lock_ratio': self.min_profit_lock_ratio
            }
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(state, f, indent=2, ensure_ascii=False)
        
        self._log(f"状态已保存到: {filepath}")
    
    def load_state(self, filepath: str):
        """
        从文件加载策略状态 (仅多头)
        
        Args:
            filepath: 文件路径
        """
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                state = json.load(f)
            
            self.position = state['position']
            self.entry_price = state['entry_price']
            self.current_stage = state['current_stage']
            self.stop_loss = state['stop_loss']
            self.high_water_mark = state.get('high_water_mark', 0.0) 
            self.entry_time = datetime.fromisoformat(state['entry_time']) if state['entry_time'] else None
            self.tick_count = state['tick_count']
            self.price_history = state['price_history']
            self.stop_loss_history = state['stop_loss_history']
            self.stage_history = state['stage_history']
            self.profit_history = state['profit_history']
            self.closed_trade_profits = state.get('closed_trade_profits', []) # 加载已平仓交易的盈亏
            
            # 加载参数（可选，如果希望加载时保留原参数）
            if 'parameters' in state:
                params = state['parameters']
                self.stage1_threshold = params.get('stage1_threshold', self.stage1_threshold)
                self.stage2_threshold = params.get('stage2_threshold', self.stage2_threshold)
                self.stage3_threshold = params.get('stage3_threshold', self.stage3_threshold)
                self.initial_stop_loss_ratio = params.get('initial_stop_loss_ratio', self.initial_stop_loss_ratio)
                self.stage1_stop_loss_ratio = params.get('stage1_stop_loss_ratio', self.stage1_stop_loss_ratio)
                self.stage2_stop_profit_ratio = params.get('stage2_stop_profit_ratio', self.stage2_stop_profit_ratio)
                self.trailing_ratio = params.get('trailing_ratio', self.trailing_ratio)
                self.min_profit_lock_ratio = params.get('min_profit_lock_ratio', self.min_profit_lock_ratio)
            
            self._log(f"状态已从文件加载: {filepath}")
            
        except Exception as e:
            self._log(f"加载状态失败: {e}", "ERROR")
            raise
    
    def get_trade_log(self, last_n: Optional[int] = None) -> List[Dict]:
        """
        获取交易日志
        
        Args:
            last_n: 返回最后N条日志，None表示返回全部
            
        Returns:
            List[Dict]: 日志列表
        """
        if last_n is None:
            return self.trade_log.copy()
        else:
            return self.trade_log[-last_n:] if last_n > 0 else []
    
    def get_performance_stats(self) -> Dict:
        """
        获取性能统计信息 (仅多头)
        
        Returns:
            Dict: 性能统计
        """
        if not self.price_history or len(self.price_history) < 2:
            return {'error': '数据不足'}
        
        current_price = self.price_history[-1]
        
        current_profit = 0
        max_profit = 0
        current_drawdown = 0

        if self.position: # 如果仍有持仓，基于当前价格和历史高点计算
            current_profit = (current_price - self.entry_price) / self.entry_price * 100
            max_profit = (self.high_water_mark - self.entry_price) / self.entry_price * 100
            current_drawdown = (self.high_water_mark - current_price) / self.high_water_mark * 100 if self.high_water_mark > 0 else 0
        
        # 计算价格波动
        price_changes = np.diff(self.price_history)
        price_volatility = np.std(price_changes) if len(price_changes) > 1 else 0
        
        return {
            'current_profit_pct': current_profit,
            'max_profit_pct': max_profit,
            'current_drawdown_pct': current_drawdown,
            'price_volatility': price_volatility,
            'total_ticks': len(self.price_history),
            'stage_distribution': {
                f'stage_{i}': self.stage_history.count(i) for i in range(4)
            } if self.stage_history else {}
        }


# --- 辅助函数：将字典键翻译为中文 ---

def translate_keys_to_chinese(data: Dict) -> Dict:
    """递归地将字典中的英文键翻译为中文。"""
    translation_map = {
        "day": "天", "reference_price": "参考价格", "k_line_high": "K线最高价", 
        "k_line_low": "K线最低价", "strategy_signal": "策略信号", "action": "动作", 
        "message": "信息", "current_strategy_status": "当前策略状态", "entry_price": "入场价格", 
        "stage": "阶段", "stop_loss": "止损价格", "realtime_profit": "实时盈利", 
        "max_profit": "最大浮盈", "current_day_high_vs_entry_ratio_growth": "当日最高价相对入场价增长率",
        "success": "成功", "reason": "原因", "position_type": "仓位类型", "price": "价格", 
        "final_profit_pct": "最终盈利百分比", "max_profit_pct": "最大浮盈百分比", "final_stage": "最终阶段", 
        "total_ticks": "总Tick数", "holding_duration": "持仓时长", "exit_time": "平仓时间", 
        "high_water_mark": "最高水位", "has_position": "有持仓", "current_price": "当前价格", 
        "profit_pct": "盈利百分比", "distance_to_stop_pct": "距止损百分比", "current_profit_pct": "当前盈利百分比", 
        "current_drawdown_pct": "当前回撤百分比", "price_volatility": "价格波动率", "stage_distribution": "阶段分布", 
        "error": "错误", "entry_time": "入场时间", "stock_name": "股票名称" 
    }
    if isinstance(data, dict):
        new_dict = {}
        for k, v in data.items():
            new_key = translation_map.get(k, k) 
            new_dict[new_key] = translate_keys_to_chinese(v) 
        return new_dict
    elif isinstance(data, list):
        return [translate_keys_to_chinese(elem) for elem in data] 
    else:
        return data

# --- 新增回测函数：simulate_single_trade_backtest ---
def simulate_single_trade_backtest(
    price_series: List[float], 
    strategy_params: Optional[Dict] = None,
    verbose: bool = True # 是否打印详细的日内信息
) -> Dict:
    """
    对单个价格序列执行一次完整的、基于LiveTradingStrategy的多头策略回测。
    
    Args:
        price_series: 完整的K线价格序列 (例如，K线最低价序列，用于严格回测)。
                      第一个价格点将被用作开仓价格。
        strategy_params: 策略参数字典，用于初始化 LiveTradingStrategy。
        verbose: 如果为True，将在每次价格点处理后打印详细状态。
        
    Returns:
        Dict: 包含本次回测的最终结果、策略实例和交易日志。
    """
    if not price_series or len(price_series) < 1:
        print("错误: 价格序列为空或不足以进行回测。")
        return {
            'final_profit_pct': 0.0,
            'strategy_instance': None,
            'trade_log': [],
            'message': '价格序列无效'
        }

    if strategy_params is None:
        strategy_params = {}
    
    # 初始化策略实例
    # 模拟时通常enable_logging=False以减少控制台输出，但为了调试可以改为True
    strategy = LiveTradingStrategy(enable_logging=False, **strategy_params) 
    
    # 开仓
    entry_price = price_series[0]
    entry_result = strategy.enter_position(entry_price)
    if verbose:
        print(f"\n--- 策略开仓 ---")
        print(f"开仓价格: {entry_price:.6f}, 初始止损: {strategy.stop_loss:.6f}")
        print(f"开仓信息: {translate_keys_to_chinese(entry_result)}")

    trade_outcome_info = None
    
    # 逐日（或逐点）处理价格序列
    # 从第二个价格点开始，因为第一个已经用于开仓
    for day_idx, current_price in enumerate(price_series[1:], 1):
        if not strategy.position:
            if verbose:
                print(f"第 {day_idx} 天: 策略已平仓，停止模拟。")
            break # 如果策略已平仓，则停止处理后续价格
        
        if pd.isna(current_price):
            if verbose:
                print(f"警告: 第 {day_idx} 天价格数据缺失 ({current_price})，跳过此点。")
            continue # 跳过缺失的价格点

        # 处理当前价格点
        process_result = strategy.process_tick(current_price)
        
        if verbose:
            current_status = strategy.get_current_status()
            print(f"\n--- 第 {day_idx} 天 (价格: {current_price:.6f}) ---")
            print(f"信号动作: {process_result.get('action', 'N/A')}, 消息: {process_result.get('message', 'N/A')}")
            print(f"当前状态: 盈利={current_status['profit_pct']:.2f}%, 阶段={current_status['current_stage']}, 止损={current_status['stop_loss']:.6f}, 距止损={current_status['distance_to_stop_pct']:.2f}%")

        if process_result.get('action') == 'EXIT':
            trade_outcome_info = process_result
            if verbose:
                print(f"交易平仓: 原因={trade_outcome_info.get('reason')}, 价格={trade_outcome_info.get('price'):.6f}, 收益={trade_outcome_info.get('final_profit_pct'):.2f}%")
            break # 交易已平仓，退出循环

    # 如果循环结束时仍有持仓，进行手动平仓
    if strategy.position:
        final_price_for_exit = price_series[-1]
        manual_exit_result = strategy.manual_exit(final_price_for_exit, 'END_OF_DATA')
        trade_outcome_info = manual_exit_result
        if verbose:
            print(f"\n--- 模拟结束，手动平仓 ---")
            print(f"平仓价格: {final_price_for_exit:.6f}, 收益={manual_exit_result.get('final_profit_pct'):.2f}%")
            print(f"最终平仓信息: {translate_keys_to_chinese(manual_exit_result)}")
    
    # 汇总最终结果
    final_profit_pct = trade_outcome_info['final_profit_pct'] if trade_outcome_info else 0.0
    
    return {
        'final_profit_pct': final_profit_pct,
        'strategy_instance': strategy, # 返回策略实例，可以用于获取完整日志和历史数据
        'trade_log': strategy.get_trade_log(),
        'all_trade_results': strategy.closed_trade_profits # 策略内部记录的所有已完成交易的盈亏
    }


# --- 原始的 testBuy 函数 (已调整为使用新的 simulate_single_trade_backtest) ---
def testBuy(buysellHistory_list):
    buysellHistory = buysellHistory_list
    total_net_profit = 0.0 # 存储总净盈亏

    print("\n=== 多组数据回测 (基于 simulate_single_trade_backtest) ===")

    for i in range(len(buysellHistory[0])):
        trade_type = buysellHistory[0][i]
        
        # 只处理多头 (trade_type == 1)
        if trade_type != 1:
            print(f"警告: buysellHistory[0][{i}] 类型为 {trade_type}，非多头策略，跳过此回测。")
            continue

        # 获取 K线最高价序列作为回测价格 (根据您最新提供的代码)
        test_prices_for_this_trade = buysellHistory[1][i] 
        
        if not test_prices_for_this_trade:
            print(f"警告: 第 {i+1} 组价格序列为空，跳过此回测。")
            continue

        print(f"\n--- 开始模拟第 {i+1} 组交易 (类型: 多头) ---")
        print(f"价格数据点数: {len(test_prices_for_this_trade)}")
        print(f"开仓价格: {test_prices_for_this_trade[0]:.5f}")
        print(f"最终价格: {test_prices_for_this_trade[-1]:.5f}")
        print(f"理论最大收益 (基于当前序列): {((test_prices_for_this_trade[-1] - test_prices_for_this_trade[0]) / test_prices_for_this_trade[0] * 100):.2f}%\n")
        
        # 调用新的单次交易回测函数
        # 禁用 verbose 以减少多组数据时的打印量，只看最终结果
        single_trade_result = simulate_single_trade_backtest(test_prices_for_this_trade, verbose=False)
        
        # 累加本次交易的最终盈利百分比 (包含止损亏损)
        total_net_profit += single_trade_result['final_profit_pct']

        print(f"本次交易模拟结束。最终收益: {single_trade_result['final_profit_pct']:.2f}%")
        print(f"当前累计总净盈亏: {total_net_profit:.2f}%")
        print("-" * 60) 

    print("\n=== 所有多组回测完成 ===")
    print(f"最终总历史净盈亏: {total_net_profit:.2f}%")

def main():
   

    # --- 演示2: 使用 buysellHistory 进行多组数据回测 (您的原始用途) ---
    print("\n\n" + "="*50)
    print("=== 演示2: 多组数据回测 (读取 buysellHistory) ===")

    # 导入您真实的 load_json 函数 (请在您的本地环境取消注释此行)
    from MkKu import load_json 
    buysellHistory_data = load_json('buysellHistory.json')
    
    # 打印 buysellHistory 的结构以便确认
    print(f"buysellHistory中交易组数量: {len(buysellHistory_data[0])}")
    print(f"第一组交易类型: {buysellHistory_data[0][0]}")
    print(f"第一组K线最高价序列前5个: {buysellHistory_data[1][0][:5]}...")
    print(f"第一组K线最低价序列前5个: {buysellHistory_data[2][0][:5]}...")
    
    # 调用 testBuy 函数来执行多组数据回测
    testBuy(buysellHistory_data)
    
if __name__ == "__main__":
    main()