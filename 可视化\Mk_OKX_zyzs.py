import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from Mkjson import  update_order_regular,update_order_dynamic_tracking,update_order_close

# --- 猴子补丁开始 ---
if not hasattr(np, 'NaN'):
    np.NaN = np.nan
# --- 猴子补丁结束 ---

def calculate_trailing_stop_prices(
    entry_price: float,
    trade_direction: str,
    now_price: float,
    current_high: float,
    current_low: float,
    is_activated: bool = False,
    initial_stop_loss_percentage: float = None,
    initial_take_profit_percentage: float = None,
    last_known_trailing_stop_price: float = None,
    base_retracement_percentage: float = 0.005,
    guaranteed_profit_percentage_from_entry: float = None
) -> tuple[float, float, float, float, bool, bool]:
    """
    优化后的动态止盈止损计算函数
    
    Args:
        entry_price (float): 开仓价格
        trade_direction (str): 交易方向 "buy" 或 "sell"
        now_price (float): 当前价格
        current_high (float): 从开仓以来的最高价
        current_low (float): 从开仓以来的最低价
        is_activated (bool): 动态机制是否已激活
        
        # 仅在 is_activated=False 时需要的参数：
        initial_stop_loss_percentage (float): 初始止损百分比
        initial_take_profit_percentage (float): 初始止盈百分比（用于计算激活价格）
        guaranteed_profit_percentage_from_entry (float): 保底盈利百分比
        
        # 仅在 is_activated=True 时需要的参数：
        last_known_trailing_stop_price (float): 上次计算的跟踪止损价
        
        # 两种情况都需要：
        base_retracement_percentage (float): 基础回撤百分比
    
    Returns:
        tuple: (动态平仓价格, 更新后最高价, 更新后最低价, 下次调用的止损价, 是否激活, 是否应平仓)
    """
    
    if trade_direction not in ["buy", "sell"]:
        raise ValueError("trade_direction 必须是 'buy' 或 'sell'")
    
    # 更新最高/最低价
    updated_current_high = current_high
    updated_current_low = current_low
    
    if trade_direction == "buy":
        updated_current_high = max(current_high, now_price)
    elif trade_direction == "sell":
        updated_current_low = min(current_low, now_price)
    
    # 判断是否处于盈利状态
    is_in_profit_zone = False
    if trade_direction == "buy" and updated_current_high > entry_price:
        is_in_profit_zone = True
    elif trade_direction == "sell" and updated_current_low < entry_price:
        is_in_profit_zone = True
    
    # 动态机制激活逻辑
    is_dynamic_trailing_active = False
    
    if not is_activated:
        # ============ 初始激活判断 ============
        if initial_take_profit_percentage is None or initial_stop_loss_percentage is None:
            raise ValueError("is_activated=False时，必须提供 initial_take_profit_percentage 和 initial_stop_loss_percentage")
        
        # 自动计算最小激活价格
        if trade_direction == "buy":
            min_activation_price = entry_price * (1 + initial_take_profit_percentage)
            if now_price >= min_activation_price and is_in_profit_zone:
                is_dynamic_trailing_active = True
        elif trade_direction == "sell":
            min_activation_price = entry_price * (1 - initial_take_profit_percentage)
            if now_price <= min_activation_price and is_in_profit_zone:
                is_dynamic_trailing_active = True
    else:
        # ============ 已激活状态 ============
        if last_known_trailing_stop_price is None:
            raise ValueError("is_activated=True时，必须提供 last_known_trailing_stop_price")
        is_dynamic_trailing_active = True
    
    # 计算有效回撤百分比
    effective_retracement_percentage = base_retracement_percentage
    
    if is_dynamic_trailing_active:
        # 使用动态回撤公式
        if trade_direction == "buy":
            x_profit_percentage = (updated_current_high / entry_price) - 1
            effective_retracement_percentage = max(base_retracement_percentage, 0.25 * x_profit_percentage + 0.0025)
            effective_retracement_percentage = min(0.10, effective_retracement_percentage)
        elif trade_direction == "sell":
            x_profit_percentage = 1 - (updated_current_low / entry_price)
            effective_retracement_percentage = max(base_retracement_percentage, 0.25 * x_profit_percentage + 0.0025)
            effective_retracement_percentage = min(0.10, effective_retracement_percentage)
    
    # 计算动态平仓价格
    if not is_activated:
        # ============ 初始状态：使用固定止损 ============
        if trade_direction == "buy":
            current_calculated_stop_price = entry_price * (1 - initial_stop_loss_percentage)
        elif trade_direction == "sell":
            current_calculated_stop_price = entry_price * (1 + initial_stop_loss_percentage)
        
        # 如果激活了动态机制，计算动态止损
        if is_dynamic_trailing_active:
            if trade_direction == "buy":
                dynamic_stop = updated_current_high * (1 - effective_retracement_percentage)
                current_calculated_stop_price = max(dynamic_stop, current_calculated_stop_price)
                
                # 应用保底盈利线（仅在初始激活时需要）
                if guaranteed_profit_percentage_from_entry and guaranteed_profit_percentage_from_entry > 0:
                    guaranteed_profit_price = entry_price * (1 + guaranteed_profit_percentage_from_entry)
                    if updated_current_high >= guaranteed_profit_price:
                        current_calculated_stop_price = max(current_calculated_stop_price, guaranteed_profit_price)
                        
            elif trade_direction == "sell":
                dynamic_stop = updated_current_low * (1 + effective_retracement_percentage)
                current_calculated_stop_price = min(dynamic_stop, current_calculated_stop_price)
                
                # 应用保底盈利线（仅在初始激活时需要）
                if guaranteed_profit_percentage_from_entry and guaranteed_profit_percentage_from_entry > 0:
                    guaranteed_profit_price = entry_price * (1 - guaranteed_profit_percentage_from_entry)
                    if updated_current_low <= guaranteed_profit_price:
                        current_calculated_stop_price = min(current_calculated_stop_price, guaranteed_profit_price)
    else:
        # ============ 已激活状态：使用动态跟踪 ============
        if trade_direction == "buy":
            dynamic_stop = updated_current_high * (1 - effective_retracement_percentage)
            # 确保止损价只向上移动（单调性）
            current_calculated_stop_price = max(dynamic_stop, last_known_trailing_stop_price)
        elif trade_direction == "sell":
            dynamic_stop = updated_current_low * (1 + effective_retracement_percentage)
            # 确保止损价只向下移动（单调性）
            current_calculated_stop_price = min(dynamic_stop, last_known_trailing_stop_price)
    
    # 判断是否应该平仓
    should_trigger_close = False
    if trade_direction == "buy":
        if now_price <= current_calculated_stop_price:
            should_trigger_close = True
    elif trade_direction == "sell":
        if now_price >= current_calculated_stop_price:
            should_trigger_close = True
    
    return (
        current_calculated_stop_price,
        updated_current_high,
        updated_current_low,
        current_calculated_stop_price,  # 下次调用使用的止损价
        is_dynamic_trailing_active,
        should_trigger_close
    )

# --- 优化后的模拟运行函数 ---
def run_simulation_optimized(scenario_name: str, params: dict, price_movements: list):
    """
    使用优化后的函数运行模拟
    """
    print(f"\n{'='*10} {scenario_name} {'='*10}")
    
    entry_price = params['entry_price']
    trade_direction = params['trade_direction']
    initial_stop_loss_percentage = params['initial_stop_loss_percentage']
    initial_take_profit_percentage = params['initial_take_profit_percentage']
    base_retracement_percentage = params['base_retracement_percentage']
    guaranteed_profit_percentage = params.get('guaranteed_profit_percentage_from_entry', 0.0)
    
    print(f"\n[策略参数]")
    print(f"  开仓价格: {entry_price:.4f}")
    print(f"  交易方向: {trade_direction}")
    print(f"  初始止损 (%): {initial_stop_loss_percentage*100:.1f}%")
    print(f"  初始止盈 (%): {initial_take_profit_percentage*100:.1f}% (激活价格)")
    print(f"  基础回撤 (%): {base_retracement_percentage*100:.1f}%")
    if guaranteed_profit_percentage > 0:
        print(f"  保底盈利 (%): {guaranteed_profit_percentage*100:.1f}%")
    print(f"  动态回撤公式: y = 0.25x + 0.0025\n")
    
    # 初始化状态
    current_high_price_tracked = entry_price
    current_low_price_tracked = entry_price
    is_position_open = True
    is_dynamic_activated = False
    last_trailing_stop = None
    
    print(f"{'时间步':<8} | {'实时价':<8} | {'追踪极值':<10} | {'盈利%':<8} | {'回撤%':<8} | {'动态激活':<12} | {'动态平仓价':<12} | {'是否平仓':<10}")
    print(f"{'-'*8}-|-{'-'*8}-|-{'-'*10}-|-{'-'*8}-|-{'-'*8}-|-{'-'*12}-|-{'-'*12}-|-{'-'*10}")
    
    for i, current_price in enumerate(price_movements):
        if not is_position_open:
            print(f"{i+1:<8} | {current_price:<8.4f} | {'已平仓':<10} | {'-':<8} | {'-':<8} | {'-':<12} | {'-':<12} | {'-':<10}")
            continue
        
        # 调用优化后的函数
        if not is_dynamic_activated:
            # 初始判断阶段
            result = calculate_trailing_stop_prices(
                entry_price=entry_price,
                trade_direction=trade_direction,
                now_price=current_price,
                current_high=current_high_price_tracked,
                current_low=current_low_price_tracked,
                is_activated=False,
                initial_stop_loss_percentage=initial_stop_loss_percentage,
                initial_take_profit_percentage=initial_take_profit_percentage,
                base_retracement_percentage=base_retracement_percentage,
                guaranteed_profit_percentage_from_entry=guaranteed_profit_percentage
            )
        else:
            # 已激活阶段
            result = calculate_trailing_stop_prices(
                entry_price=entry_price,
                trade_direction=trade_direction,
                now_price=current_price,
                current_high=current_high_price_tracked,
                current_low=current_low_price_tracked,
                is_activated=True,
                last_known_trailing_stop_price=last_trailing_stop,
                base_retracement_percentage=base_retracement_percentage
            )
        
        # 解析结果
        calculated_stop_price, updated_high, updated_low, next_stop_price, is_activated, should_close = result
        
        # 更新状态
        current_high_price_tracked = updated_high
        current_low_price_tracked = updated_low
        is_dynamic_activated = is_activated
        last_trailing_stop = next_stop_price
        
        # 计算显示用的盈利百分比
        if trade_direction == "buy":
            profit_pct = ((current_high_price_tracked / entry_price) - 1) * 100
        else:
            profit_pct = (1 - (current_low_price_tracked / entry_price)) * 100
        
        # 计算当前使用的回撤百分比
        current_retracement_pct = base_retracement_percentage * 100  # 默认使用基础回撤
        if is_activated and profit_pct > 0:
            # 计算动态回撤百分比
            x_profit_decimal = profit_pct / 100
            dynamic_retracement = max(base_retracement_percentage, 0.25 * x_profit_decimal + 0.0025)
            dynamic_retracement = min(0.10, dynamic_retracement)
            current_retracement_pct = dynamic_retracement * 100
        
        tracked_extreme = current_high_price_tracked if trade_direction == "buy" else current_low_price_tracked
        
        print(f"{i+1:<8} | {current_price:<8.4f} | {tracked_extreme:<10.4f} | {profit_pct:<7.2f}% | {current_retracement_pct:<7.2f}% | {str(is_activated):<12} | {calculated_stop_price:<12.4f} | {str(should_close):<10}")
        
        if should_close:
            final_profit = ((current_price / entry_price) - 1) * 100 if trade_direction == "buy" else (1 - (current_price / entry_price)) * 100
            print(f"  *** 策略发出平仓信号! 成交价: {current_price:.4f}, 收益: {final_profit:.2f}% ***")
            is_position_open = False
            break
    
    if is_position_open:
        print("\n[模拟结束] 仍在持仓中。")
    else:
        print("\n[模拟结束] 策略已平仓。")
        
        

def check_json(orders, slice, base_retracement_percentage=0.005, guaranteed_profit_percentage_from_entry=0.025):
    """
    检查订单状态并处理跟踪止盈止损、固定止损和超时平仓
    
    参数:
    orders: 所有订单列表
    slice: 当前K线数据切片 (slice_df)
    base_retracement_percentage: 基础回撤百分比，默认0.005 (0.5%)
    guaranteed_profit_percentage_from_entry: 保底收益百分比，默认0.025 (2.5%)
    """
    all_orders = orders  # 保持原变量名兼容性
    slice_df = slice     # 保持原变量名兼容性
    
    # 获取当前价格 (假设使用收盘价作为当前价格)
    nowPrice = slice_df.iloc[-1]['close']
    
    for order in all_orders:
        # 获取当前K线的最高价和最低价
        current_high_price = slice_df.iloc[-1]['high']
        current_low_price = slice_df.iloc[-1]['low']
        
        ##1.检查是否触发跟踪止盈止损，如果触发就更新数据
        # 根据持仓方向确定触发价格
        if order["开仓方向"] == 'buy':
            trigger_price = current_high_price  # 多头用最高价判断
        else:  # sell
            trigger_price = current_low_price   # 空头用最低价判断
        
        # 调用函数计算止盈止损点
        if order["动态机制状态"] == 0:  ##未启动
            result = calculate_trailing_stop_prices(
                entry_price=order["开仓价格"],
                trade_direction=order['开仓方向'],
                now_price=trigger_price,  # 使用正确的触发价格
                current_high=max(order["最高观察价格"], current_high_price),  # 更新最高价
                current_low=min(order["最低观察价格"], current_low_price),    # 更新最低价
                is_activated=False,
                initial_stop_loss_percentage=order["止损百分比"],
                initial_take_profit_percentage=order["止盈百分比"],
                base_retracement_percentage=base_retracement_percentage,
                guaranteed_profit_percentage_from_entry=guaranteed_profit_percentage_from_entry
            )
            stop_price, current_high, current_low, last_stop, is_activated, should_close = result
            
            if is_activated == True:  ##动态机制激活
                update_order_dynamic_tracking(all_orders, 
                                            order['order_id'],  # 修正字段名
                                            trigger_price,  # 使用正确的触发价格
                                            slice_df.iloc[-1]['open_time'],
                                            dynamic_tp_sl_price=stop_price)  # 添加缺失的参数
            else:
                update_order_regular(all_orders, 
                                    order['order_id'],  # 修正字段名
                                    trigger_price,  # 使用正确的触发价格
                                    slice_df.iloc[-1]['open_time']) 

        else:  # 动态机制已启动
            last_known_trailing_stop_price = order["动态止盈止损历史"][-1]["动态平仓价"]  # 修正为列表访问
            result = calculate_trailing_stop_prices(
                entry_price=order["开仓价格"],
                trade_direction=order['开仓方向'],
                now_price=trigger_price,  # 使用正确的触发价格
                current_high=max(order["最高观察价格"], current_high_price),  # 更新最高价
                current_low=min(order["最低观察价格"], current_low_price),    # 更新最低价
                is_activated=True,
                last_known_trailing_stop_price=last_known_trailing_stop_price,      # 上次计算的止损价
                base_retracement_percentage=base_retracement_percentage,
            )
            stop_price, new_high, new_low, next_stop, is_active, should_close = result
            
            ##检查是否触发动态平仓
            if should_close == True:
                #触发动态平仓
                update_order_close(all_orders, order['order_id'], 
                                 order["动态止盈止损历史"][-1]['动态平仓价'],  # 修正为列表访问
                                 order["当前持仓量"], slice_df.iloc[-1]['open_time'])
                continue  # 已平仓，跳过后续检查
            else:
                # 根据持仓方向判断是否需要更新动态止损价
                if order["开仓方向"] == 'buy':
                    # 多头持仓：止损价上移才更新
                    if next_stop > order["动态止盈止损历史"][-1]['动态平仓价']:  # 修正为列表访问
                        update_order_dynamic_tracking(all_orders, 
                                                    order['order_id'],  # 修正字段名
                                                    trigger_price,  # 使用正确的触发价格
                                                    slice_df.iloc[-1]['open_time'],
                                                    dynamic_tp_sl_price=next_stop)
                elif order["开仓方向"] == 'sell':
                    # 空头持仓：止损价下移才更新
                    if next_stop < order["动态止盈止损历史"][-1]['动态平仓价']:  # 修正为列表访问
                        update_order_dynamic_tracking(all_orders, 
                                                    order['order_id'],  # 修正字段名
                                                    trigger_price,  # 使用正确的触发价格
                                                    slice_df.iloc[-1]['open_time'],
                                                    dynamic_tp_sl_price=next_stop)

        #2.如果没有触发动态平仓，就检查有没有触发固定止损，如果触发就更新订单状态
        if order["持仓状态"] == 0:  # 修正字段名：0=持仓中，确保订单还在持仓状态
            entry_price = order["开仓价格"]
            stop_loss_pct = order["止损百分比"]
            take_profit_pct = order["止盈百分比"]
            
            if order["开仓方向"] == 'buy':
                # 多头持仓
                # 固定止损价 = 开仓价 * (1 - 止损百分比)
                fixed_stop_loss = entry_price * (1 - stop_loss_pct)
                # 固定止盈价 = 开仓价 * (1 + 止盈百分比)
                fixed_take_profit = entry_price * (1 + take_profit_pct)
                
                # 检查是否触发固定止损或止盈
                if current_low_price <= fixed_stop_loss:
                    # 触发固定止损
                    update_order_close(all_orders, order['order_id'],  # 修正字段名
                                     fixed_stop_loss, order["当前持仓量"], 
                                     slice_df.iloc[-1]['open_time'])
                    continue  # 已平仓，跳过后续检查
                elif current_high_price >= fixed_take_profit:
                    # 触发固定止盈
                    update_order_close(all_orders, order['order_id'],  # 修正字段名
                                     fixed_take_profit, order["当前持仓量"], 
                                     slice_df.iloc[-1]['open_time'])
                    continue  # 已平仓，跳过后续检查
                    
            elif order["开仓方向"] == 'sell':
                # 空头持仓
                # 固定止损价 = 开仓价 * (1 + 止损百分比)
                fixed_stop_loss = entry_price * (1 + stop_loss_pct)
                # 固定止盈价 = 开仓价 * (1 - 止盈百分比)
                fixed_take_profit = entry_price * (1 - take_profit_pct)
                
                # 检查是否触发固定止损或止盈
                if current_high_price >= fixed_stop_loss:
                    # 触发固定止损
                    update_order_close(all_orders, order['order_id'],  # 修正字段名
                                     fixed_stop_loss, order["当前持仓量"], 
                                     slice_df.iloc[-1]['open_time'])
                    continue  # 已平仓，跳过后续检查
                elif current_low_price <= fixed_take_profit:
                    # 触发固定止盈
                    update_order_close(all_orders, order['order_id'],  # 修正字段名
                                     fixed_take_profit, order["当前持仓量"], 
                                     slice_df.iloc[-1]['open_time'])
                    continue  # 已平仓，跳过后续检查

        ##3.继续检查，在没有触发跟踪止盈止损和固定止损的情况下检查持仓时间2小时内平仓
        if order["持仓状态"] == 0:  # 修正字段名：0=持仓中，确保订单还在持仓状态
            # 计算持仓时长
            open_time = pd.to_datetime(order["开仓时间"])
            current_time = pd.to_datetime(slice_df.iloc[-1]['open_time'])
            holding_duration = current_time - open_time
            
            # 检查是否持仓超过2小时
            if holding_duration >= timedelta(hours=24):
                # 超过2小时，按当前价格平仓 (根据持仓方向选择合适的平仓价格)
                close_price = current_high_price if order["开仓方向"] == 'buy' else current_low_price
                update_order_close(all_orders, order['order_id'],  # 修正字段名
                                 close_price, order["当前持仓量"], 
                                 slice_df.iloc[-1]['open_time'])

# --- 测试用例 ---
if __name__ == "__main__":
    print("--- 优化后的动态止盈止损策略测试 ---")
    
    # 测试场景1: 开多止盈
    scenario1_params = {
        'entry_price': 100.00,
        'trade_direction': "buy",
        'initial_stop_loss_percentage': 0.02,        # 2%止损
        'initial_take_profit_percentage': 0.01,      # 1%激活动态
        'base_retracement_percentage': 0.005,        # 0.5%基础回撤
        'guaranteed_profit_percentage_from_entry': 0.015  # 1.5%保底
    }
    scenario1_prices = [
        100.00, 100.50, 101.00, 101.50,  # 逐步上涨，1.5%时激活
        102.00, 103.00, 105.00,          # 继续上涨到5%
        104.50, 104.00, 103.50,          # 开始回撤
        103.25                            # 触发动态止盈
    ]
    run_simulation_optimized("场景1: 开多动态止盈", scenario1_params, scenario1_prices)
    
    # 测试场景2: 开空止盈  
    scenario2_params = {
        'entry_price': 50.00,
        'trade_direction': "sell",
        'initial_stop_loss_percentage': 0.02,        # 2%止损
        'initial_take_profit_percentage': 0.01,      # 1%激活动态
        'base_retracement_percentage': 0.005,        # 0.5%基础回撤
        'guaranteed_profit_percentage_from_entry': 0.015  # 1.5%保底
    }
    scenario2_prices = [
        50.00, 49.70, 49.50, 49.30,      # 逐步下跌，1%时激活
        49.00, 48.50, 47.50,             # 继续下跌到5%
        47.75, 48.00, 48.25,             # 开始反弹
        48.40                             # 触发动态止盈
    ]
    run_simulation_optimized("场景2: 开空动态止盈", scenario2_params, scenario2_prices)
    
    # 测试场景3: 固定止损
    scenario3_params = {
        'entry_price': 100.00,
        'trade_direction': "buy", 
        'initial_stop_loss_percentage': 0.015,       # 1.5%止损
        'initial_take_profit_percentage': 0.02,      # 2%激活（不会触发）
        'base_retracement_percentage': 0.005
    }
    scenario3_prices = [
        100.00, 99.50, 99.00, 98.50, 98.40  # 直接下跌触发固定止损
    ]
    run_simulation_optimized("场景3: 固定止损", scenario3_params, scenario3_prices)