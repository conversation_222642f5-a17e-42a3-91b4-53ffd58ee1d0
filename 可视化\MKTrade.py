import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime

def rolling_trade(df, initial_amount=100, leverage=1, margin_call_ratio=0.2, stop_loss_pct=5.0, take_profit_pct=10.0):
    """
    滚仓交易函数（支持杠杆和爆仓重投）
    
    参数:
    df: DataFrame，包含交易数据
    initial_amount: 初始金额，默认100
    leverage: 杠杆倍数，默认1（无杠杆）
    margin_call_ratio: 爆仓比例，默认0.2（账户剩余20%时爆仓）
    stop_loss_pct: 止损百分比，默认5.0（亏损5%止损）
    take_profit_pct: 止盈百分比，默认10.0（盈利10%止盈）
    
    返回:
    DataFrame，包含交易记录和资金变化
    """
    # 复制数据框避免修改原始数据
    df_trade = df.copy()
    
    # 初始化所有变量（确保都有默认值）
    current_amount = initial_amount  # 当前资金
    position = 0  # 当前持仓：0=空仓，1=多仓，-1=空仓
    entry_price = 0  # 开仓价格
    shares = 0  # 持仓数量
    total_invested = initial_amount  # 累计投入资金
    liquidation_count = 0  # 爆仓次数
    
    # 记录交易过程
    trade_records = []
    
    # 遍历每一行数据
    for i in range(len(df_trade)):
        row = df_trade.iloc[i]
        
        # 获取当前K线数据
        open_price = row['open']
        high_price = row['high']
        low_price = row['low']
        close_price = row['close']
        trade_signal = row['tradeS']
        trade_price = row['tradePrice']
        
        # 初始化当前记录的所有字段
        current_record = {
            'index': i,
            'open_time': row['open_time'],
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price,
            'trade_signal': trade_signal,
            'trade_price': trade_price,
            'position_before': position,
            'amount_before': current_amount,
            'entry_price': entry_price,
            'shares': shares,
            'leverage': leverage,
            'liquidated': False,
            'liquidation_price': 0,
            'close_position': False,
            'open_position': '无',
            'close_price': 0,
            'pnl': 0,
            'stop_loss_triggered': False,
            'take_profit_triggered': False,
            'exit_reason': '无'
        }
        
        # 检查是否爆仓（在有持仓的情况下）
        liquidated = False
        if position != 0:
            if position == 1:  # 多仓
                # 多仓浮动盈亏
                floating_pnl = (low_price - entry_price) * shares  # 用最低价计算最坏情况
                # 爆仓条件：当前资金 + 浮动盈亏 <= 保证金 × 爆仓比例
                if current_amount + floating_pnl <= current_amount * margin_call_ratio:
                    liquidated = True
                    
            elif position == -1:  # 空仓
                # 空仓浮动盈亏
                floating_pnl = (entry_price - high_price) * shares  # 用最高价计算最坏情况
                # 爆仓条件：当前资金 + 浮动盈亏 <= 保证金 × 爆仓比例
                if current_amount + floating_pnl <= current_amount * margin_call_ratio:
                    liquidated = True
        
        # 处理爆仓
        if liquidated:
            current_record['liquidated'] = True
            current_record['liquidation_price'] = high_price if position == -1 else low_price
            current_record['pnl'] = -current_amount  # 当前资金全部亏损
            current_record['exit_reason'] = '爆仓'
            
            # 爆仓后重新投入初始资金
            current_amount = initial_amount
            total_invested += initial_amount
            liquidation_count += 1
            position = 0
            entry_price = 0
            shares = 0
        
        # 检查止盈止损（优先级最高，如果有持仓且没有爆仓）
        stop_triggered = False
        if not liquidated and position != 0:
            if position == 1:  # 多仓止盈止损
                # 止损：最低价跌破止损点
                if low_price <= entry_price * (1 - stop_loss_pct / 100):
                    # 平仓
                    exit_price = entry_price * (1 - stop_loss_pct / 100)
                    pnl = (exit_price - entry_price) * shares
                    current_amount = current_amount + pnl
                    
                    current_record['close_position'] = True
                    current_record['close_price'] = exit_price
                    current_record['pnl'] = pnl
                    current_record['stop_loss_triggered'] = True
                    current_record['exit_reason'] = '止损'
                    stop_triggered = True
                    
                # 止盈：最高价突破止盈点
                elif high_price >= entry_price * (1 + take_profit_pct / 100):
                    # 平仓
                    exit_price = entry_price * (1 + take_profit_pct / 100)
                    pnl = (exit_price - entry_price) * shares
                    current_amount = current_amount + pnl
                    
                    current_record['close_position'] = True
                    current_record['close_price'] = exit_price
                    current_record['pnl'] = pnl
                    current_record['take_profit_triggered'] = True
                    current_record['exit_reason'] = '止盈'
                    stop_triggered = True
                    
            elif position == -1:  # 空仓止盈止损
                # 止损：最高价突破止损点
                if high_price >= entry_price * (1 + stop_loss_pct / 100):
                    # 平仓
                    exit_price = entry_price * (1 + stop_loss_pct / 100)
                    pnl = (entry_price - exit_price) * shares
                    current_amount = current_amount + pnl
                    
                    current_record['close_position'] = True
                    current_record['close_price'] = exit_price
                    current_record['pnl'] = pnl
                    current_record['stop_loss_triggered'] = True
                    current_record['exit_reason'] = '止损'
                    stop_triggered = True
                    
                # 止盈：最低价跌破止盈点
                elif low_price <= entry_price * (1 - take_profit_pct / 100):
                    # 平仓
                    exit_price = entry_price * (1 - take_profit_pct / 100)
                    pnl = (entry_price - exit_price) * shares
                    current_amount = current_amount + pnl
                    
                    current_record['close_position'] = True
                    current_record['close_price'] = exit_price
                    current_record['pnl'] = pnl
                    current_record['take_profit_triggered'] = True
                    current_record['exit_reason'] = '止盈'
                    stop_triggered = True
            
            # 如果触发了止盈止损，重置持仓
            if stop_triggered:
                position = 0
                entry_price = 0
                shares = 0
        
        # 处理原始交易信号（如果没有爆仓且没有触发止盈止损）
        if not liquidated and not stop_triggered and trade_signal != 0 and trade_price > 0:
            # 如果有持仓，先平仓
            if position != 0:
                # 平仓计算 - 修复杠杆交易盈亏计算
                if position == 1:  # 平多仓
                    # 多仓盈亏 = (平仓价 - 开仓价) × 股数
                    pnl = (trade_price - entry_price) * shares
                    current_amount = current_amount + pnl
                elif position == -1:  # 平空仓
                    # 空仓盈亏 = (开仓价 - 平仓价) × 股数
                    pnl = (entry_price - trade_price) * shares
                    current_amount = current_amount + pnl
                
                current_record['close_position'] = True
                current_record['close_price'] = trade_price
                current_record['pnl'] = pnl
                current_record['exit_reason'] = '信号平仓'
                
                # 重置持仓
                position = 0
                entry_price = 0
                shares = 0
            
            # 开新仓（修复杠杆计算）
            if trade_signal == 1:  # 开多仓
                position = 1
                entry_price = trade_price
                # 正确的杠杆计算：股数 = (保证金 × 杠杆) / 价格
                shares = (current_amount * leverage) / trade_price
                current_record['open_position'] = f'多仓({leverage}x)'
                current_record['exit_reason'] = '开多仓'
                
            elif trade_signal == -1:  # 开空仓
                position = -1
                entry_price = trade_price
                # 正确的杠杆计算：股数 = (保证金 × 杠杆) / 价格
                shares = (current_amount * leverage) / trade_price
                current_record['open_position'] = f'空仓({leverage}x)'
                current_record['exit_reason'] = '开空仓'
        
        # 计算当前市值（修复市值计算）
        if position == 1:  # 多仓
            # 多仓浮动盈亏 = (当前价 - 开仓价) × 股数
            unrealized_pnl = (close_price - entry_price) * shares
            current_market_value = current_amount + unrealized_pnl
        elif position == -1:  # 空仓
            # 空仓浮动盈亏 = (开仓价 - 当前价) × 股数
            unrealized_pnl = (entry_price - close_price) * shares
            current_market_value = current_amount + unrealized_pnl
        else:  # 空仓
            current_market_value = current_amount
            unrealized_pnl = 0
        
        # 添加计算字段
        current_record['position_after'] = position
        current_record['amount_after'] = current_amount
        current_record['market_value'] = current_market_value
        current_record['unrealized_pnl'] = unrealized_pnl
        current_record['total_return'] = (current_market_value - total_invested) / total_invested * 100
        current_record['net_invested'] = total_invested
        current_record['liquidation_count'] = liquidation_count
        current_record['stop_loss_pct'] = stop_loss_pct
        current_record['take_profit_pct'] = take_profit_pct
        
        trade_records.append(current_record)
    
    # 转换为DataFrame
    result_df = pd.DataFrame(trade_records)
    
    return result_df

def analyze_trading_performance(trade_df):
    """
    分析交易表现
    """
    # 计算总收益
    final_value = trade_df.iloc[-1]['market_value']
    total_invested = trade_df.iloc[-1]['net_invested']
    total_return = (final_value - total_invested) / total_invested * 100
    
    # 计算交易次数
    trades = trade_df[trade_df['trade_signal'] != 0]
    total_trades = len(trades)
    
    # 计算盈利交易数量
    profitable_trades = len(trades[trades['pnl'] > 0])
    
    # 计算爆仓信息
    liquidations = trade_df[trade_df['liquidated'] == True]
    liquidation_count = len(liquidations)
    
    # 计算止盈止损统计
    stop_loss_trades = len(trade_df[trade_df['stop_loss_triggered'] == True])
    take_profit_trades = len(trade_df[trade_df['take_profit_triggered'] == True])
    signal_trades = len(trade_df[trade_df['exit_reason'] == '信号平仓'])
    
    # 计算最大回撤
    max_value = trade_df['market_value'].expanding().max()
    drawdown = (trade_df['market_value'] - max_value) / max_value * 100
    max_drawdown = drawdown.min()
    
    print(f"交易分析报告:")
    print(f"总投入资金: {total_invested:.2f}")
    print(f"最终资金: {final_value:.2f}")
    print(f"总收益率: {total_return:.2f}%")
    print(f"交易次数: {total_trades}")
    print(f"盈利交易数: {profitable_trades}")
    print(f"胜率: {profitable_trades/total_trades*100:.2f}%" if total_trades > 0 else "胜率: 0%")
    print(f"爆仓次数: {liquidation_count}")
    print(f"止损次数: {stop_loss_trades}")
    print(f"止盈次数: {take_profit_trades}")
    print(f"信号平仓次数: {signal_trades}")
    print(f"最大回撤: {max_drawdown:.2f}%")
    
    return {
        'total_return': total_return,
        'total_trades': total_trades,
        'profitable_trades': profitable_trades,
        'win_rate': profitable_trades/total_trades*100 if total_trades > 0 else 0,
        'liquidation_count': liquidation_count,
        'stop_loss_count': stop_loss_trades,
        'take_profit_count': take_profit_trades,
        'signal_close_count': signal_trades,
        'max_drawdown': max_drawdown,
        'final_value': final_value,
        'total_invested': total_invested
    }

def plot_trading_results(trade_df, title="滚仓交易资金曲线"):
    """
    绘制交易结果图表
    """
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']  # 支持中文显示
    plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号
    
    # 创建图形和子图
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 12))
    
    # 处理时间数据
    try:
        time_data = pd.to_datetime(trade_df['open_time'])
    except:
        time_data = range(len(trade_df))
    
    # 子图1: 资金曲线
    ax1.plot(time_data, trade_df['market_value'], label='账户市值', color='blue', linewidth=2)
    ax1.plot(time_data, trade_df['net_invested'], label='累计投入', color='red', linestyle='--', alpha=0.7)
    ax1.axhline(y=trade_df.iloc[0]['net_invested'], color='gray', linestyle=':', alpha=0.5, label='初始投入')
    
    # 标记爆仓点
    liquidations = trade_df[trade_df['liquidated'] == True]
    if len(liquidations) > 0:
        liq_times = pd.to_datetime(liquidations['open_time']) if isinstance(time_data, pd.DatetimeIndex) else liquidations.index
        ax1.scatter(liq_times, liquidations['market_value'], color='red', s=100, marker='x', 
                   label=f'爆仓点 ({len(liquidations)}次)', zorder=5)
    
    ax1.set_title(f'{title} - 资金变化', fontsize=14, fontweight='bold')
    ax1.set_ylabel('资金金额', fontsize=12)
    ax1.legend(prop={'size': 10})  # 设置图例字体大小
    ax1.grid(True, alpha=0.3)
    
    # 子图2: 价格走势和交易信号
    ax2.plot(time_data, trade_df['close'], label='收盘价', color='black', linewidth=1)
    
    # 标记买卖点
    buy_signals = trade_df[trade_df['trade_signal'] == 1]
    sell_signals = trade_df[trade_df['trade_signal'] == -1]
    
    if len(buy_signals) > 0:
        buy_times = pd.to_datetime(buy_signals['open_time']) if isinstance(time_data, pd.DatetimeIndex) else buy_signals.index
        ax2.scatter(buy_times, buy_signals['trade_price'], color='green', s=80, marker='^', 
                   label=f'买入信号 ({len(buy_signals)}次)', zorder=5)
    
    if len(sell_signals) > 0:
        sell_times = pd.to_datetime(sell_signals['open_time']) if isinstance(time_data, pd.DatetimeIndex) else sell_signals.index
        ax2.scatter(sell_times, sell_signals['trade_price'], color='red', s=80, marker='v', 
                   label=f'卖出信号 ({len(sell_signals)}次)', zorder=5)
    
    ax2.set_title('价格走势与交易信号', fontsize=14, fontweight='bold')
    ax2.set_ylabel('价格', fontsize=12)
    ax2.legend(prop={'size': 10})
    ax2.grid(True, alpha=0.3)
    
    # 子图3: 收益率和回撤
    ax3.plot(time_data, trade_df['total_return'], label='总收益率', color='purple', linewidth=2)
    ax3.axhline(y=0, color='gray', linestyle='-', alpha=0.5)
    
    # 计算回撤
    max_value = trade_df['market_value'].expanding().max()
    drawdown = (trade_df['market_value'] - max_value) / max_value * 100
    ax3.fill_between(time_data, 0, drawdown, where=(drawdown < 0), color='red', alpha=0.3, label='回撤')
    
    ax3.set_title('收益率与回撤', fontsize=14, fontweight='bold')
    ax3.set_ylabel('百分比 (%)', fontsize=12)
    ax3.set_xlabel('时间', fontsize=12)
    ax3.legend(prop={'size': 10})
    ax3.grid(True, alpha=0.3)
    
    # 调整布局
    plt.tight_layout()
    plt.show()
    
    # 打印统计信息
    final_return = trade_df.iloc[-1]['total_return']
    max_drawdown = drawdown.min()
    liquidation_count = len(trade_df[trade_df['liquidated'] == True])
    
    print(f"\n📊 图表统计:")
    print(f"最终收益率: {final_return:.2f}%")
    print(f"最大回撤: {max_drawdown:.2f}%")
    print(f"爆仓次数: {liquidation_count}")
    print(f"数据点数: {len(trade_df)}")

# 示例用法
if __name__ == "__main__":
    # 使用示例:
    # result = rolling_trade(df, initial_amount=100, leverage=5, margin_call_ratio=0.2, 
    #                       stop_loss_pct=5.0, take_profit_pct=10.0)
    # performance = analyze_trading_performance(result)
    # plot_trading_results(result, "5倍杠杆+止盈止损滚仓交易")
    # 
    # # 查看关键交易记录
    # print(result[['open_time', 'trade_signal', 'exit_reason', 'stop_loss_triggered', 
    #              'take_profit_triggered', 'market_value', 'total_return']].head(20))
    pass