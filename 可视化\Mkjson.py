import json
import os
import datetime
import time 
import random 
from typing import Optional, Union 

import numpy as np
if not hasattr(np, 'NaN'):
    np.NaN = np.nan

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
DATA_FILE = os.path.join(SCRIPT_DIR, 'trading_orders.json')

MAX_ORDER_ID_VALUE = 2696334268299304960 
REQUIRED_ORDER_ID_LENGTH = 32

def parse_percentage_string(pct_input) -> float:
    if isinstance(pct_input, (float, int)):
        return float(pct_input) / 100.0
    elif isinstance(pct_input, str):
        clean_str = pct_input.strip().replace('%', '')
        try:
            return float(clean_str) / 100.0
        except ValueError:
            raise ValueError(f"无法将字符串 '{pct_input}' 解析为有效的百分比数值。请确保格式为 'XX.YY%' 或 'XX.YY'。")
    else:
        raise TypeError(f"预期百分比输入为字符串、浮点数或整数，但收到类型: {type(pct_input)}。")

def load_orders() -> list:
    if os.path.exists(DATA_FILE):
        with open(DATA_FILE, 'r', encoding='utf-8') as f:
            try:
                orders = json.load(f)
                for order in orders:
                    order.setdefault("累计已实现收益额", 0.0)
                    order.setdefault("平仓记录", [])
                    order.setdefault("动态机制状态", 0) 
                    order.setdefault("杠杆倍数", 1) 
                    order.setdefault("交易对", "UNKNOWN") 
                    order.setdefault("产品类型", "SWAP") 
                    order.setdefault("开仓保证金", 0.0) 
                    order.setdefault("动态止盈止损历史", []) 

                    order.setdefault("最高观察价格", order.get("开仓价格", 0.0)) 
                    order.setdefault("最低观察价格", order.get("开仓价格", 0.0)) 

                    if "收益率历史" in order and isinstance(order["收益率历史"], list):
                        for entry in order["收益率历史"]:
                            if "数值" in entry and "收益额" not in entry:
                                entry["收益率"] = entry.pop("数值")
                                entry["收益额"] = 0.0
                                entry["当前价格"] = 0.0
                            entry.setdefault("收益额", 0.0)
                            entry.setdefault("收益率", 0.0)
                            entry.setdefault("当前价格", 0.0)
                    else:
                        order["收益率历史"] = []

                return orders if isinstance(orders, list) else []
            except json.JSONDecodeError:
                print(f"警告: 无法解析 JSON 文件 {DATA_FILE}，可能已损坏或为空，返回空列表。")
                return []
    else:
        print(f"文件 {DATA_FILE} 不存在，将创建一个新的空订单列表。")
        return []

def load_old_orders() -> list:
    old_data_file = os.path.dirname(os.path.abspath(__file__)) + os.sep + 'trading_ordersOld.json'
    
    if os.path.exists(old_data_file):
        with open(old_data_file, 'r', encoding='utf-8') as f:
            try:
                old_orders = json.load(f)
                if isinstance(old_orders, list):
                    print(f"成功加载 {len(old_orders)} 个历史订单")
                    return old_orders
                else:
                    print("警告: 历史订单文件格式不正确")
                    return []
            except json.JSONDecodeError:
                print(f"警告: 无法解析历史订单文件 {old_data_file}")
                return []
            except IOError as e:
                print(f"警告: 无法读取历史订单文件 {old_data_file} - {e}")
                return []
    else:
        print("历史订单文件不存在")
        return []

def save_orders(orders: list) -> list:
    """
    将订单数据保存到JSON文件。
    **修正后的逻辑**: 当主文件中的**持仓中订单**数量超过20时，才会将所有**当前已平仓且尚未存在于历史文件**的订单迁移到历史文件。
    同时确保历史文件中的订单不重复。
    **新增**: 函数返回处理后的（只包含活跃订单的）列表，以更新内存中的订单数据。
    """
    try:
        old_data_file = os.path.dirname(os.path.abspath(__file__)) + os.sep + 'trading_ordersOld.json'
        
        active_orders = [order for order in orders if order.get("持仓状态", 0) == 0]
        closed_orders_in_memory = [order for order in orders if order.get("持仓状态", 0) == 1]
        
        orders_to_save_to_main_file = orders # Default to saving all incoming orders
        
        if len(orders) > 20:
            print(f"--- 总订单数量超过20个({len(orders)}个)，开始处理已平仓订单迁移 ---")
            
            if closed_orders_in_memory:
                existing_old_orders = load_old_orders()
                existing_old_order_ids = {order['order_id'] for order in existing_old_orders}
                
                new_closed_orders_to_migrate = []
                for order in closed_orders_in_memory:
                    if order['order_id'] not in existing_old_order_ids:
                        new_closed_orders_to_migrate.append(order)
                
                if new_closed_orders_to_migrate:
                    all_old_orders = existing_old_orders + new_closed_orders_to_migrate
                    
                    def sort_key(order_item):
                        open_time = order_item.get("开仓时间", "1970-01-01 00:00:00+00:00")
                        order_id = order_item.get("order_id", "0")
                        try:
                            time_obj = datetime.datetime.fromisoformat(open_time)
                            return (time_obj, int(order_id) if order_id.isdigit() else float('inf'))
                        except (ValueError, TypeError):
                            return (open_time, int(order_id) if order_id.isdigit() else float('inf'))
                    
                    all_old_orders.sort(key=sort_key)
                    
                    with open(old_data_file, 'w', encoding='utf-8') as f:
                        json.dump(all_old_orders, f, indent=4, ensure_ascii=False)
                    
                    print(f"成功迁移 {len(new_closed_orders_to_migrate)} 个新的已平仓订单到历史文件")
                    print(f"历史文件现包含 {len(all_old_orders)} 个订单")
                else:
                    print("没有新的已平仓订单需要迁移（可能已全部在历史文件中）。")
                
                orders_to_save_to_main_file = active_orders
                print(f"主文件将保存 {len(orders_to_save_to_main_file)} 个持仓中订单。")
            else:
                print("总订单数量已超限，但内存中没有已平仓订单需要迁移。")
                orders_to_save_to_main_file = active_orders

        else:
            print(f"--- 总订单数量未超过20个({len(orders)}个)，暂不迁移已平仓订单。将保存所有订单。")
            orders_to_save_to_main_file = orders
        
        with open(DATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(orders_to_save_to_main_file, f, indent=4, ensure_ascii=False)
            
        print(f"--- 主订单文件 '{DATA_FILE}' 已更新。")
        
        return orders_to_save_to_main_file 
            
    except IOError as e:
        print(f"错误: 无法保存文件 {DATA_FILE} - {e}")
        return orders
    except Exception as e:
        print(f"订单保存/迁移过程中发生意外错误: {e}")
        try:
            with open(DATA_FILE, 'w', encoding='utf-8') as f:
                json.dump(orders, f, indent=4, ensure_ascii=False)
            print("已保存原始订单数据作为备份，以防迁移错误。")
            return orders
        except Exception as backup_error:
            print(f"备份保存也失败: {backup_error}")
            return []

def load_all_orders() -> tuple[list, list]:
    current_orders = load_orders()
    old_orders = load_old_orders()
    
    print(f"总计加载: 当前订单 {len(current_orders)} 个, 历史订单 {len(old_orders)} 个")
    return current_orders, old_orders

def _generate_pure_numeric_32_digit_id(timestamp_str_for_prefix: str) -> str:
    """
    生成一个32位纯数字的订单ID，以YYYYMMDDHHmm格式的时间戳作为前缀。
    后面的随机数部分确保唯一性。
    """
    try:
        dt_obj = datetime.datetime.fromisoformat(timestamp_str_for_prefix)
    except ValueError:
        print(f"警告: 传入的时间戳 '{timestamp_str_for_prefix}' 格式无效，自动生成ID时将使用当前时间。")
        dt_obj = datetime.datetime.now(datetime.timezone.utc).astimezone()

    time_prefix = dt_obj.strftime("%Y%m%d%H%M")
    
    remaining_length = REQUIRED_ORDER_ID_LENGTH - len(time_prefix) # 32 - 12 = 20
    
    nanoseconds_part = str(time.time_ns())[-min(10, remaining_length):] # 取纳秒时间戳的最后几位，最多10位
    
    fill_length = remaining_length - len(nanoseconds_part)
    random_fill = ''.join(str(random.randint(0, 9)) for _ in range(fill_length))
    
    generated_id = time_prefix + nanoseconds_part + random_fill
    
    if len(generated_id) != REQUIRED_ORDER_ID_LENGTH:
        print(f"错误：生成的订单ID长度不符预期。实际：{len(generated_id)}，预期：{REQUIRED_ORDER_ID_LENGTH}")
        return ''.join(str(random.randint(0, 9)) for _ in range(REQUIRED_ORDER_ID_LENGTH))

    return generated_id

def open_new_order(
    orders: list,
    timestamp_str: str, 
    open_price: float,
    position_quantity: float, 
    direction: Union[str, int],
    take_profit_pct: float, 
    stop_loss_pct: float, 
    dynamic_mechanism: int = 0, 
    custom_order_id: Optional[Union[str, int]] = None,
    leverage: int = 1, 
    contract_pair: str = "UNKNOWN", 
    product_type: str = "SWAP" 
) -> dict:
    """
    开立一笔新订单并添加到订单列表中，初始化所有新字段。
    开仓保证金现在强制等于 开仓价格 * 开仓总量。
    动态止盈止损历史在开仓时不初始化。
    新增：最高观察价格 和 最低观察价格 作为持久化字段，并在开仓时初始化。
    时间参数 timestamp_str 用于记录开仓时间，格式为 "YYYY-MM-DD HH:MM:SS+HH:MM"。
    Direction 参数现在支持字符串 "buy"/"sell" 或整数 1/-1。
    订单ID逻辑完善：
    - 订单ID必须是纯数字且固定32位。
    - 如果提供的ID不符合规范（长度不为32、包含非数字字符、重复、超出数值范围），则自动生成纯数字的32位时间格式ID。
    - 自动生成的ID以传入的timestamp_str为基础，格式为 YYYYMMDDHHmm + 随机数字填充至32位。
    """
    try:
        datetime.datetime.fromisoformat(timestamp_str)
    except ValueError:
        raise ValueError(f"时间参数 timestamp_str 格式不正确。预期格式为 'YYYY-MM-DD HH:MM:SS+HH:MM'，但收到 '{timestamp_str}'。")

    if isinstance(direction, int):
        if direction == 1:
            direction_str = "buy"
        elif direction == -1:
            direction_str = "sell"
        else:
            raise ValueError(f"Direction 参数 '{direction}' 无效。当为整数时，只接受 1 (buy) 或 -1 (sell)。")
    elif isinstance(direction, str):
        if direction.lower() == "buy":
            direction_str = "buy"
        elif direction.lower() == "sell":
            direction_str = "sell"
        else:
            raise ValueError(f"Direction 参数 '{direction}' 无效。当为字符串时，只接受 'buy' 或 'sell'。")
    else:
        raise TypeError(f"Direction 参数类型不正确。预期为字符串 ('buy'/'sell') 或整数 (1/-1)，但收到类型: {type(direction)}。")

    final_order_id_str = None
    if custom_order_id is not None:
        temp_order_id_str = str(custom_order_id)
        
        if not temp_order_id_str.isdigit():
            print(f"警告: 自定义订单ID '{temp_order_id_str}' 包含非数字字符，将自动生成ID。")
        elif len(temp_order_id_str) != REQUIRED_ORDER_ID_LENGTH:
            print(f"警告: 自定义订单ID '{temp_order_id_str}' 长度 ({len(temp_order_id_str)}) 不符合 {REQUIRED_ORDER_ID_LENGTH} 位的纯数字规范，将自动生成ID。")
        elif any(order["order_id"] == temp_order_id_str for order in orders):
            print(f"警告: 自定义订单ID '{temp_order_id_str}' 已存在，将自动生成ID。")
        else:
            try:
                order_id_int = int(temp_order_id_str)
                if order_id_int <= MAX_ORDER_ID_VALUE:
                    final_order_id_str = temp_order_id_str
                    print(f"使用自定义订单ID: {final_order_id_str}")
                else:
                    print(f"警告: 自定义订单ID '{temp_order_id_str}' (转换为整数后为 {order_id_int}) 超过了最大允许值 {MAX_ORDER_ID_VALUE}，将自动生成ID。")
            except ValueError:
                print(f"警告: 自定义订单ID '{temp_order_id_str}' 无法转换为整数进行数值验证，将自动生成ID。")
    
    if final_order_id_str is None:
        final_order_id_str = _generate_pure_numeric_32_digit_id(timestamp_str)
        print(f"自动生成订单ID: {final_order_id_str}")

    final_initial_margin = open_price * position_quantity

    new_order = {
        "order_id": final_order_id_str, 
        "开仓时间": timestamp_str, 
        "交易对": contract_pair, 
        "产品类型": product_type, 
        "开仓价格": open_price,
        "开仓总量": position_quantity, 
        "开仓方向": direction_str, 
        "杠杆倍数": leverage, 
        "开仓保证金": final_initial_margin, 
        "当前持仓量": position_quantity, 
        "止盈百分比": take_profit_pct, 
        "止损百分比": stop_loss_pct, 
        "当前未实现收益额": 0.0,   
        "当前未实现收益率": 0.0,   
        "累计已实现收益额": 0.0,  
        "持仓状态": 0,  
        "动态止盈止损历史": [],
        "平仓记录": [],    
        "收益率历史": [], 
        "最高观察价格": open_price, 
        "最低观察价格": open_price 
    }

    orders.append(new_order)
    print(f"--- 开立新订单成功: ID={final_order_id_str}, 交易对={contract_pair}, 方向={direction_str}, 开仓总量={position_quantity:.2f}, 开仓价格={open_price:.5f}, 杠杆={leverage}, 开仓保证金={final_initial_margin:.4f}, 动态机制初始状态={dynamic_mechanism}")
    return new_order

def _find_order(orders: list, order_id: str) -> Optional[dict]:
    return next((o for o in orders if o["order_id"] == order_id), None)

def _calculate_unrealized_pnl(order: dict, current_price: float) -> tuple[float, float]:
    open_price = order["开仓价格"]
    position_quantity = order["当前持仓量"]
    direction = order["开仓方向"]
    initial_margin = order.get("开仓保证金", 0.0)

    current_pnl_amount = 0.0
    if direction == "buy":
        current_pnl_amount = (current_price - open_price) * position_quantity
    elif direction == "sell":
        current_pnl_amount = (open_price - current_price) * position_quantity

    current_pnl_ratio = 0.0
    if initial_margin != 0:
        current_pnl_ratio = current_pnl_amount / initial_margin

    return current_pnl_amount, current_pnl_ratio

def update_order_regular(orders: list, order_id: str, current_price: float, timestamp_str: str): 
    try:
        datetime.datetime.fromisoformat(timestamp_str)
    except ValueError:
        raise ValueError(f"时间参数 timestamp_str 格式不正确。预期格式为 'YYYY-MM-DD HH:MM:SS+HH:MM'，但收到 '{timestamp_str}'。")

    order = _find_order(orders, order_id)
    if not order:
        print(f"--- 警告: 未找到订单 {order_id} 进行常规更新。")
        return

    if order["持仓状态"] != 0:
        return

    current_pnl_amount, current_pnl_ratio = _calculate_unrealized_pnl(order, current_price)
    order["当前未实现收益额"] = current_pnl_amount
    order["当前未实现收益率"] = current_pnl_ratio

    order["收益率历史"].append({
        "时刻": timestamp_str,
        "收益额": current_pnl_amount,
        "当前价格": current_price,
        "收益率": current_pnl_ratio
    })

    open_price = order["开仓价格"]
    direction = order["开仓方向"]
    
    if direction == "buy":
        if current_price > order.get("最高观察价格", open_price):
            order["最高观察价格"] = current_price
    elif direction == "sell":
        if current_price < order.get("最低观察价格", open_price):
            order["最低观察价格"] = current_price

def update_order_dynamic_tracking(
    orders: list, 
    order_id: str, 
    current_price: float, 
    timestamp_str: str, 
    realtime_output_data: dict # <-- 修改：现在接收整个realtime_output_data字典
):
    """
    更新订单的未实现收益、收益率历史，并记录外部传入的动态止盈止损的完整状态。
    此函数负责激活动态机制，并以增量方式记录完整的动态追踪数据。
    时间参数 timestamp_str 用于记录。
    """
    try:
        datetime.datetime.fromisoformat(timestamp_str)
    except ValueError:
        raise ValueError(f"时间参数 timestamp_str 格式不正确。预期格式为 'YYYY-MM-DD HH:MM:SS+HH:MM'，但收到 '{timestamp_str}'。")
        
    order = _find_order(orders, order_id)
    if not order:
        print(f"--- 警告: 未找到订单 {order_id} 进行动态追踪更新。")
        return

    if order["持仓状态"] != 0:
        return

    # 动态机制激活逻辑：如果动态机制未激活，则在此处激活它
    if order.get("动态机制状态", 0) != 1:
        order["动态机制状态"] = 1
        print(f"--- 动态机制: 订单 {order_id} 已激活。")
    
    # 提取 realtime_output_data 中的所有相关字段
    strategy_status = realtime_output_data.get("当前策略状态", {})
    
    new_dynamic_history_entry = {
        "时刻": timestamp_str, 
        "开仓方向": order["开仓方向"], # 从订单本身获取开仓方向
        "开仓价格": strategy_status.get("开仓价格", order["开仓价格"]), # 优先从策略状态获取，否则使用订单开仓价格
        "参考价格": realtime_output_data.get("参考价格", 0.0),
        "K线最高价": realtime_output_data.get("K线最高价", 0.0),
        "K线最低价": realtime_output_data.get("K线最低价", 0.0),
        "策略信号": realtime_output_data.get("策略信号", {"动作": "UNKNOWN", "信息": "N/A"}),
        "止损价格": realtime_output_data.get("止损价格", 0.0),
        "阶段": strategy_status.get("阶段", 0),
        "实时盈利": strategy_status.get("实时盈利", 0.0),
        "最高盈利": strategy_status.get("最高盈利", 0.0),
        "当前回撤": strategy_status.get("当前回撤", 0.0)
    }

    # 处理初始占位记录 (如果存在且是唯一的占位符，则替换)
    history = order["动态止盈止损历史"]
    if len(history) == 1 and history[0].get("时刻") == "0":
        history[0] = new_dynamic_history_entry # 替换占位符
        print(f"--- 动态追踪: 订单 {order_id} 替换初始占位符。")
    else:
        history.append(new_dynamic_history_entry) # 否则追加新条目
        print(f"--- 动态追踪: 订单 {order_id} 添加新历史条目。")


    # 计算并更新未实现收益
    current_pnl_amount, current_pnl_ratio = _calculate_unrealized_pnl(order, current_price)
    order["当前未实现收益额"] = current_pnl_amount
    order["当前未实现收益率"] = current_pnl_ratio

    # 记录收益率历史
    order["收益率历史"].append({
        "时刻": timestamp_str,
        "收益额": current_pnl_amount,
        "当前价格": current_price,
        "收益率": current_pnl_ratio
    })

    print(f"--- 动态追踪更新: 订单 {order_id} (当前市场价格: {current_price:.5f}) -> 未实现收益率={order['当前未实现收益率']:.4f}, 未实现收益额={order['当前未实现收益额']:.4f}, 策略止损价格={realtime_output_data.get('止损价格', 0.0):.5f}, 阶段={new_dynamic_history_entry.get('阶段')}")


def update_order_close(orders: list, order_id: str, close_price: float, quantity_to_close: float = -1.0, timestamp_str: str = ""): 
    """
    处理订单的平仓操作，更新持仓量、收益、平仓记录和持仓状态。
    此函数不负责触发平仓，只负责执行平仓后的数据更新。时间参数 timestamp_str 用于记录。
    同时，修正了收益率历史和观察价格的更新逻辑，以准确记录平仓时刻的数据。
    """
    try:
        datetime.datetime.fromisoformat(timestamp_str)
    except ValueError:
        raise ValueError(f"时间参数 timestamp_str 格式不正确。预期格式为 'YYYY-MM-DD HH:MM:SS+HH:MM'，但收到 '{timestamp_str}'。")

    order = _find_order(orders, order_id)
    if not order:
        print(f"--- 警告: 未找到订单 {order_id} 进行平仓更新。")
        return

    if order["持仓状态"] != 0:
        return

    current_quantity = order["当前持仓量"] 
    direction = order["开仓方向"]
    open_price = order["开仓价格"]

    if quantity_to_close == -1.0 or quantity_to_close >= current_quantity:
        closed_quantity = current_quantity
        print(f"--- 平仓操作: 订单 {order_id} -> **全部平仓** {closed_quantity:.2f} 份持仓。")
    else:
        closed_quantity = quantity_to_close
        print(f"--- 平仓操作: 订单 {order_id} -> **部分平仓** {closed_quantity:.2f} 份持仓。")
    
    realized_profit_per_unit = 0.0
    if direction == "buy":
        realized_profit_per_unit = (close_price - open_price)
    elif direction == "sell":
        realized_profit_per_unit = (open_price - close_price)

    realized_profit_amount_this_close = realized_profit_per_unit * closed_quantity

    # --- 修正 bug 1: 在归零之前记录平仓时刻的收益率历史 ---
    pnl_amount_at_close, pnl_ratio_at_close = _calculate_unrealized_pnl(order, close_price)
    order["收益率历史"].append({
        "时刻": timestamp_str,
        "收益额": pnl_amount_at_close,
        "当前价格": close_price,
        "收益率": pnl_ratio_at_close
    })
    # --- 修正 bug 1 结束 ---

    order["累计已实现收益额"] += realized_profit_amount_this_close 
    order["当前持仓量"] -= closed_quantity

    order["平仓记录"].append({
        "平仓时间": timestamp_str,
        "平仓价格": close_price,
        "平仓数量": closed_quantity,
        "本次实现盈亏": realized_profit_amount_this_close
    })

    order["当前未实现收益额"] = 0.0 
    order["当前未实现收益率"] = 0.0 

    # --- 修正 bug 2: 更新最高/最低观察价格 ---
    if direction == "buy":
        if close_price > order.get("最高观察价格", open_price):
            order["最高观察价格"] = close_price
    elif direction == "sell":
        if close_price < order.get("最低观察价格", open_price):
            order["最低观察价格"] = close_price
    # --- 修正 bug 2 结束 ---

    if order["当前持仓量"] <= 0:
        order["持仓状态"] = 1
    #     print(f"       订单 {order_id} 现在已完全平仓。")
    # print(f"       平仓价格: {close_price:.5f}, 本次平仓已实现盈亏: {realized_profit_amount_this_close:.4f}")
    # print(f"       剩余持仓量: {order['当前持仓量']:.2f}, 当前未实现收益率: {order['当前未实现收益率']:.4f}, 当前未实现收益额: {order['当前未实现收益额']:.4f}")
    # print(f"       累计已实现收益额: {order['累计已实现收益额']:.4f}") 



def display_order_full_details(orders: list, order_id: str):
    for order in orders:
        if order["order_id"] == order_id:
            status_text = "持仓中" if order["持仓状态"] == 0 else "已平仓"
            direction_text = "买入" if order["开仓方向"] == "buy" else "卖出" 
            dynamic_mechanism_text = "开启" if order.get("动态机制状态", 0) == 1 else "关闭" 

            print(f"\n--- 订单 {order_id} 详细信息 (状态: {status_text}) ---")
            print(f" 开仓时间: {order['开仓时间']}") 
            print(f" 交易对: {order.get('交易对', 'N/A')}") 
            print(f" 产品类型: {order.get('产品类型', 'N/A')}") 
            print(f" 开仓价格: {order['开仓价格']:.5f}") 
            print(f" 开仓总量: {order['开仓总量']:.2f}") 
            print(f" 开仓方向: {direction_text}") 
            print(f" 杠杆倍数: {order.get('杠杆倍数', 1)}") 
            print(f" 开仓保证金: {order.get('开仓保证金', 0.0):.4f}") 
            print(f" 当前持仓量: {order['当前持仓量']:.2f}") 
            print(f" 止盈百分比: {order['止盈百分比']:.2%}") 
            print(f" 止损百分比: {order['止损百分比']:.2%}") 
            print(f" 当前未实现收益额: {order['当前未实现收益额']:.4f}") 
            print(f" 当前未实现收益率: {order['当前未实现收益率']:.4f}") 
            print(f" 累计已实现收益额: {order.get('累计已实现收益额', 0.0):.4f}") 
            print(f" 最高观察价格: {order.get('最高观察价格', 0.0):.5f}") 
            print(f" 最低观察价格: {order.get('最低观察价格', 0.0):.5f}") 

            print(f" --- 动态止盈止损历史 ({len(order.get('动态止盈止损历史', []))} 条记录) ---") 
            if not order.get("动态止盈止损历史", []):
                print(" (无动态止盈止损历史)")
            for entry in order.get("动态止盈止损历史", []):
                时刻_str = '(时刻缺失)' if entry.get('时刻') is None else ('初始占位' if entry.get('时刻') == "0" else str(entry.get('时刻')))
                
                # 打印所有相关字段
                print(f" 时刻: {时刻_str}")
                print(f"  开仓方向: {entry.get('开仓方向', 'N/A')}")
                print(f"  开仓价格: {entry.get('开仓价格', 0.0):.5f}")
                print(f"  参考价格: {entry.get('参考价格', 0.0):.5f}")
                print(f"  K线最高价: {entry.get('K线最高价', 0.0):.5f}")
                print(f"  K线最低价: {entry.get('K线最低价', 0.0):.5f}")
                # 策略信号是一个字典，可能需要进一步解包或直接打印
                if isinstance(entry.get('策略信号'), dict):
                    print(f"  策略信号: 动作={entry['策略信号'].get('动作', 'N/A')}, 信息={entry['策略信号'].get('信息', 'N/A')}")
                else:
                    print(f"  策略信号: {entry.get('策略信号', 'N/A')}") # 如果不是字典，直接打印
                print(f"  止损价格: {entry.get('止损价格', 0.0):.5f}")
                print(f"  阶段: {entry.get('阶段', 0)}")
                print(f"  实时盈利: {entry.get('实时盈利', 0.0):.5f}")
                print(f"  最高盈利: {entry.get('最高盈利', 0.0):.5f}")
                print(f"  当前回撤: {entry.get('当前回撤', 0.0):.5f}")
                print("  --------------------") # 分隔符
            print("-----------------------------------")
            return
    print(f"--- 警告: 未找到订单 {order_id} 详情。")