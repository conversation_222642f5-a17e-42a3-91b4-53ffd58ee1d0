# -*- coding: utf-8 -*-

import ccxt
import pandas as pd
import time
from datetime import datetime, timezone, timedelta
import sys
# 假设您的okx_sdkV1文件与此脚本在同一目录下
from ku import *
from config import  calculate_kline_needed,get_kline_data,add_rsi_to_kline_data


from tradeing import check_trading_single ,judge_signal,calc_price,batch_judge_trade

from okx_sdkV1 import get_kline_data, OKXClient 
from emailHl import send_email_with_attachment_enhanced

import mplfinance as mpf
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from matplotlib.font_manager import FontProperties
from typing import Dict, List, Any
import warnings
warnings.filterwarnings('ignore', category=UserWarning)
from matplotlib.font_manager import FontProperties, findfont, FontManager
# ==============================================================================
# 0. 时区配置
# ==============================================================================
# 定义北京时区 (UTC+8)
BEIJING_TZ = timezone(timedelta(hours=8))


# ==============================================================================
# 1. 参数配置 (Separation of Concerns: Configuration)
# ==============================================================================
# 所有策略和程序的配置都集中在这里
my_pram = {
    # --- 技术指标参数 ---
    "rsi_length": 8,
    "macd_length": 26, # 通常指MACD中的慢速EMA周期 (slow)
    "my_sma_length": 5,
    "my_ema_length": 5,
    "my_gaussian_sigma":1.9,
    
    # --- 策略与回测参数 ---
    "max_lookback_kline_count":150, # 保证指标计算有足够数据量的最小K线数量
    "zhiyinPre": 2,
    "zhisunPre": 1,
    
    # --- 实时运行参数 ---
    "windowsTime": 12,
    "Uname": "doge", # 交易币种名称
    "Ktime": "15m",  # K线的时间周期 ('1m', '5m', '15m', '30m', '1h', '4h', '1d')
    
    # --- 历史数据参数 (主要用于回测, 实时模式下不直接使用) ---
    "strTime": "2024-03-01",
    "endTime": "2024-04-07"
}


# ==============================================================================
# 2. 核心功能函数 (Separation of Concerns: Business Logic)
# ==============================================================================




def parse_interval_to_seconds(interval_str: str) -> int:
    """
    将交易所标准的时间周期字符串（如 '1m', '30m', '1h'）转换为秒。
    """
    unit = interval_str[-1].lower()
    value = int(interval_str[:-1])
    if unit == 'm':
        return value * 60
    elif unit == 'h':
        return value * 60 * 60
    elif unit == 'd':
        return value * 24 * 60 * 60
    else:
        raise ValueError(f"Unsupported interval format: {interval_str}")


def get_next_trigger_timestamp(interval_str: str) -> int:
    """
    计算下一个K线收盘时刻的Unix时间戳（以秒为单位）。
    """
    interval_seconds = parse_interval_to_seconds(interval_str)
    now_ts = int(time.time())
    current_bucket_start_ts = (now_ts // interval_seconds) * interval_seconds
    next_trigger_ts = current_bucket_start_ts + interval_seconds
    return next_trigger_ts

def analyze_kline_performance(df: pd.DataFrame, open_direction: int) -> tuple[list[str], pd.Series]:
    if df.empty:
        raise ValueError("输入的DataFrame不能为空。")
    if not all(col in df.columns for col in ['open', 'high', 'low', 'close']):
        raise ValueError("DataFrame 必须包含 'open', 'high', 'low', 'close' 列。")
    if open_direction not in [1, -1]:
        raise ValueError("open_direction 必须是 1 (开多) 或 -1 (开空)。")

    # 根据你提供的代码，这里是使用第二根K线的开盘价作为计算最大涨幅/回撤的基准
    # 如果你希望使用第一根K线的开盘价 (df.iloc[0]['open']) 或收盘价 (df.iloc[0]['close'])
    # 作为基准，请相应地修改这一行。
    if len(df) < 2:
        raise ValueError("DataFrame 至少需要包含 2 行数据才能使用 df.iloc[1]['open'] 作为基准。")
    
    # 确保用于性能计算的初始价格不为0
    initial_price_for_performance = df.iloc[0]['open']
    if initial_price_for_performance == 0:
        raise ValueError("计算最大涨幅和回撤的初始价格不能为0。")

    # 获取整个 DataFrame 的最高价和最低价
    max_price_overall = df['high'].max()
    min_price_overall = df['low'].min()

    max_good_value = 0.0 # 实际浮点数值
    max_bad_value = 0.0  # 实际浮点数值

    ## 计算整体最大涨幅 (max_good) 和最大回撤 (max_bad)
    if open_direction == 1: # 开多
        # max_good: 从 initial_price_for_performance 到整体最高价的涨幅
        max_good_value = (max_price_overall / initial_price_for_performance) - 1
        # max_bad: 从 initial_price_for_performance 到整体最低价的回撤
        max_bad_value = (min_price_overall / initial_price_for_performance) - 1
    elif open_direction == -1: # 开空
        # max_good (盈利): 从 initial_price_for_performance 到整体最低价的跌幅
        # 注意：如果 min_price_overall 为0，会导致除零。
        if min_price_overall == 0:
            max_good_value = np.inf # 无限盈利
        else:
            max_good_value = (initial_price_for_performance / min_price_overall) - 1

        # max_bad (亏损): 从 initial_price_for_performance 到整体最高价的涨幅
        # 注意：如果 max_price_overall 为0，会导致除零。
        if max_price_overall == 0:
            max_bad_value = -np.inf # 无限亏损
        else:
            max_bad_value = (initial_price_for_performance / max_price_overall) - 1

    ## 修复后的每根 K 线涨跌幅计算
    # 增加对 open 为0的保护，避免 ZeroDivisionError
    # 将 open 为 0 的替换为 NaN，以便在计算百分比变化时得到 NaN
    open_safe = df['open'].replace(0, np.NaN)
    
    # 正确计算从开盘价到收盘价的百分比变化: (close - open) / open
    percentage_change = (df['close'] - df['open']) / open_safe
    
    # 格式化为百分比字符串。对于 NaN 值，显示为 'NaN%'
    percentage_formatted = percentage_change.apply(lambda x: f"{x:.3%}" if pd.notna(x) else "NaN%")
    
    # 格式化单个数值 max_good_value 和 max_bad_value 为百分比字符串
    # 确保对 NaN, inf, -inf 值进行妥善处理，避免格式化错误
    formatted_max_good = f"{max_good_value:.3%}" if pd.notna(max_good_value) and np.isfinite(max_good_value) else f"{max_good_value}"
    formatted_max_bad = f"{max_bad_value:.3%}" if pd.notna(max_bad_value) and np.isfinite(max_bad_value) else f"{max_bad_value}"

    # 返回格式化后的 max_good 和 max_bad，以及逐 K 线的百分比 Series
    return [ open_direction,formatted_max_good, formatted_max_bad], percentage_formatted


def setup_chinese_fonts():
    """配置中文字体"""
    plt.rcParams['font.sans-serif'] = [
        'SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 
        'WenQuanYi Micro Hei', 'sans-serif'
    ]
    plt.rcParams['axes.unicode_minus'] = False
    
def plot_kline_with_trade_signals(df: pd.DataFrame,
                                ohlc_cols: List[str] = ['open', 'high', 'low', 'close'],
                                time_col: str = 'open_time',
                                trade_col: str = 'tradeS',
                                state_col: str = 'emaStas',
                                title: str = 'K线图与交易信号',
                                figsize: tuple = (16, 10)):
    # 设置中文字体
    setup_chinese_fonts()
    
    # 数据验证
    if df.empty:
        raise ValueError("DataFrame不能为空")
    
    required_cols = ohlc_cols + [time_col, trade_col]
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        raise ValueError(f"缺少必需的列: {missing_cols}")
    
    # 数据准备
    df_plot = df.copy()
    
    # 重命名OHLC列
    rename_map = {
        ohlc_cols[0]: 'Open',
        ohlc_cols[1]: 'High', 
        ohlc_cols[2]: 'Low',
        ohlc_cols[3]: 'Close'
    }
    df_plot.rename(columns=rename_map, inplace=True)
    
    # 设置时间索引
    if time_col in df_plot.columns:
        df_plot[time_col] = pd.to_datetime(df_plot[time_col])
        df_plot.set_index(time_col, inplace=True)
    else:
        # 如果没有时间列，使用虚拟时间索引
        dummy_index = pd.to_datetime(pd.date_range(start='2024-01-01', periods=len(df_plot)))
        df_plot.set_index(dummy_index, inplace=True)
    
    # 处理交易信号
    long_signals = df_plot[df_plot[trade_col] == 1]
    short_signals = df_plot[df_plot[trade_col] == -1]
    
    # 创建交易信号标记
    addplot_list = []
    
    # 做多信号 (绿色向上三角)
    if not long_signals.empty:
        long_markers = pd.Series(index=df_plot.index, dtype=float)
        long_markers.loc[long_signals.index] = long_signals['Low'] * 0.998  # 稍微低于最低价
        addplot_list.append(
            mpf.make_addplot(long_markers, type='scatter', markersize=100, 
                           marker='^', color='green', alpha=0.8)
        )
    
    # 做空信号 (红色向下三角)
    if not short_signals.empty:
        short_markers = pd.Series(index=df_plot.index, dtype=float)
        short_markers.loc[short_signals.index] = short_signals['High'] * 1.002  # 稍微高于最高价
        addplot_list.append(
            mpf.make_addplot(short_markers, type='scatter', markersize=100,
                           marker='v', color='red', alpha=0.8)
        )
    
    # 绘制K线图
    mc = mpf.make_marketcolors(up='g', down='r', inherit=True)
    s = mpf.make_mpf_style(marketcolors=mc, gridstyle=':')
    
    # 抑制警告
    with warnings.catch_warnings():
        warnings.simplefilter("ignore")
        
        try:
            if addplot_list:
                fig, axes = mpf.plot(
                    df_plot[['Open', 'High', 'Low', 'Close']], 
                    type='candle',
                    style=s,
                    addplot=addplot_list,
                    figsize=figsize,
                    returnfig=True,
                    show_nontrading=False
                )
            else:
                fig, axes = mpf.plot(
                    df_plot[['Open', 'High', 'Low', 'Close']], 
                    type='candle',
                    style=s,
                    figsize=figsize,
                    returnfig=True,
                    show_nontrading=False
                )
        except Exception as e:
            raise RuntimeError(f"绘制K线图失败: {e}")
    
    # 获取主图轴
    ax = axes[0]
    
    # 设置标题和标签
    ax.set_title(title, fontsize=14, pad=20, fontweight='bold')
    ax.set_ylabel('价格', fontsize=12)
    
    # 创建图例
    legend_elements = []
    
    if not long_signals.empty:
        legend_elements.append(
            plt.Line2D([0], [0], marker='^', color='w', markerfacecolor='green',
                      markersize=10, label='做多信号 (1)', linestyle='None')
        )
    
    if not short_signals.empty:
        legend_elements.append(
            plt.Line2D([0], [0], marker='v', color='w', markerfacecolor='red',
                      markersize=10, label='做空信号 (-1)', linestyle='None')
        )
    
    if legend_elements:
        ax.legend(handles=legend_elements, loc='upper left', fontsize=10)
    
    # 统计信息
    total_signals = len(long_signals) + len(short_signals)
    long_count = len(long_signals)
    short_count = len(short_signals)
    
    # 在图上添加统计信息
    stats_text = f"交易信号统计:\n做多: {long_count}次\n做空: {short_count}次\n总计: {total_signals}次"
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=9,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 优化布局
    plt.tight_layout()
    
    # 显示图表
    try:
        mpf.show()
    except Exception as e:
        print(f"显示图表时出错: {e}")
    
    # 返回统计信息
    return {
        'total_signals': total_signals,
        'long_signals': long_count,
        'short_signals': short_count,
        'long_signal_times': long_signals.index.tolist() if not long_signals.empty else [],
        'short_signal_times': short_signals.index.tolist() if not short_signals.empty else []
    }


def analyze_trade_results_advanced(trade_data: list[list[int, str, str]]) -> dict:
    """
    高级分析交易结果列表，提供更详细的中文统计信息，包括平均值、中位数、标准差和百分位数。

    Args:
        trade_data: 一个列表的列表，每个子列表包含 [open_direction, max_good_str, max_bad_str]。
                    open_direction: 1 表示做多，-1 表示做空。
                    max_good_str: 最大涨幅的字符串表示 (例如 '0.626%')。
                    max_bad_str: 最大回撤的字符串表示 (例如 '-1.059%')。

    Returns:
        一个字典，包含做多和做空方向的统计结果，所有描述和数值都已格式化为中文。
    """
    long_max_goods = []
    long_max_bads = []
    short_max_goods = []
    short_max_bads = []

    for trade in trade_data:
        if len(trade) != 3:
            # 兼容性处理，如果数据格式不符合预期，跳过并给出警告
            print(f"警告: 跳过格式不正确的交易数据: {trade}")
            continue

        direction = trade[0]
        try:
            # 移除百分号并转换为浮点数，处理可能的 'inf' 或 '-inf'
            max_good = float(trade[1].replace('%', '')) / 100
            max_bad = float(trade[2].replace('%', '')) / 100
        except ValueError:
            # 处理无法解析的数值，例如 "inf" 或其他非数字字符串
            print(f"警告: 无法解析百分比字符串，跳过交易数据: {trade}")
            continue

        if direction == 1:
            long_max_goods.append(max_good)
            long_max_bads.append(max_bad)
        elif direction == -1:
            short_max_goods.append(max_good)
            short_max_bads.append(max_bad)
        else:
            # 处理未知的开盘方向
            print(f"警告: 未知的开盘方向 ({direction})，跳过交易数据: {trade}")

    results = {}

    # --- 做多交易统计 ---
    results['做多交易统计'] = {
        '交易次数': f"{len(long_max_goods)} 笔"
    }
    if long_max_goods:
        results['做多交易统计'].update({
            '平均最大涨幅': f"{np.mean(long_max_goods):.3%}",
            '平均最大回撤': f"{np.mean(long_max_bads):.3%}",
            '最大回撤中位数': f"{np.median(long_max_bads):.3%}",
            '最大回撤标准差': f"{np.std(long_max_bads):.3%}",
            '最大回撤最小值': f"{np.min(long_max_bads):.3%}",
            '最大回撤最大值': f"{np.max(long_max_bads):.3%}",
            '最大回撤P75': f"{np.percentile(long_max_bads, 75):.3%}",
            '最大回撤P90': f"{np.percentile(long_max_bads, 90):.3%}",
            '最大回撤P95': f"{np.percentile(long_max_bads, 95):.3%}"
        })
    else:
        results['做多交易统计'].update({
            '详情': "无做多交易数据。"
        })

    # --- 做空交易统计 ---
    results['做空交易统计'] = {
        '交易次数': f"{len(short_max_goods)} 笔"
    }
    if short_max_goods:
        results['做空交易统计'].update({
            '平均最大涨幅': f"{np.mean(short_max_goods):.3%}",
            '平均最大回撤': f"{np.mean(short_max_bads):.3%}",
            '最大回撤中位数': f"{np.median(short_max_bads):.3%}",
            '最大回撤标准差': f"{np.std(short_max_bads):.3%}",
            '最大回撤最小值': f"{np.min(short_max_bads):.3%}",
            '最大回撤最大值': f"{np.max(short_max_bads):.3%}",
            '最大回撤P75': f"{np.percentile(short_max_bads, 75):.3%}",
            '最大回撤P90': f"{np.percentile(short_max_bads, 90):.3%}",
            '最大回撤P95': f"{np.percentile(short_max_bads, 95):.3%}"
        })
    else:
        results['做空交易统计'].update({
            '详情': "无做空交易数据。"
        })

    return results

# ==============================================================================
# 3. 主执行函数 (Main Execution Block)
# ==============================================================================

def main():
    Uname='doge'
    Ktime='15m'
    strTime='2024-1-1'
    endTime='2024-2-4'

    params = {
    "rsi_length": 8,
    "macd_length": 26,
    "max_lookback_kline_count": 150,
    "my_gaussian_sigma": 1.9,
    "zhiyinPre":1.2,
    "zhisunPre":1.7,
    "windowsTime":16
}

    try:
        df = get_local_kline_data(Uname, Ktime, strTime, endTime)
        print(df.head())
    except Exception as e:
        print(e)

    count =params['max_lookback_kline_count']
    singleTop=[0,0,0,0,0,0]
    singleDown=[0,0,0,0,0,0]
    tradeS=[]
    any_good_bad=[]
    for i in range(0,len(df)):
        cline_df=df.iloc[i:i+count]
        any_df=df.iloc[i+count:i+count+8]
        if i+count==len(df)-8:
            # any_df=df.iloc[i+count:i+count+8]
            break
        
        if cline_df is not None and not cline_df.empty:
                close_values = cline_df['close'].values  # 预提取，避免重复调用
                nowPrice = close_values[- 1]  # 获取最新值
                window_close = close_values[:-1]  # 使用预提取的数据 
   
                
                ##计算rsi会导致长度减1
                window_rsi = fast_rsi_calculation(window_close, params["rsi_length"])
                valid_rsi = window_rsi[~np.isnan(window_rsi)]
                # print(f'valid_rsi:{len(valid_rsi)}')
                # print(valid_rsi)
                if len(valid_rsi) > 0:
                    smooth_rsi_window = fast_gaussian_smooth(valid_rsi, params["my_gaussian_sigma"])

                stats = analyze_numerical_series( smooth_rsi_window [-params["max_lookback_kline_count"]:])
                # print(smooth_rsi_window)
                # print("正态分布置信区间1σ区:", stats['conf_interval_1sigma'], "数量:", stats['n_1sigma'])
                # print("正态分布置信区间2σ区:", stats['conf_interval_2sigma'], "数量:", stats['n_2sigma'])
                # print("正态分布置信区间3σ区:", stats['conf_interval_3sigma'], "数量:", stats['n_3sigma'])


                a=pd.Series(window_close[1:])
                b=pd.Series(smooth_rsi_window)
                # print(len(a),len(b))
                 ##计算超空做多
                if smooth_rsi_window[-1]<=stats['conf_interval_2sigma'][0]:
                    
                        #     # --------- 双轴图组合（RSI+价格） ----------
                    # 正确的移位逻辑：各自独立更新历史
                    singleTop = singleTop[1:] + [0]    # Top信号在本周期为0
                    singleDown = singleDown[1:] + [1]  # Down信号在本周期为1 (做多)
                    tradeS.append(1)
                    print(f"{i+count}超空做多信号nowPrice:{nowPrice}",smooth_rsi_window[-1],singleDown)
                    # print(f'{params["rsi_length"]}长度的rsi值和gau值：')
                    print("正态分布置信区间1σ区:", stats['conf_interval_1sigma'], "数量:", stats['n_1sigma'])
                    print("正态分布置信区间2σ区:", stats['conf_interval_2sigma'], "数量:", stats['n_2sigma'])
                    print("正态分布置信区间3σ区:", stats['conf_interval_3sigma'], "数量:", stats['n_3sigma'])
                    print(cline_df.tail(2))
                    good_bad,df_zf=analyze_kline_performance(any_df,1)
                    any_good_bad.append(good_bad)
                    # print(any_df)
                    print(good_bad,"\n",df_zf)
                #     generate_dual_axis_plot_V3(
                #     y1_values=a,
                #     y2_values=b,
                #     hline_y=50, # 水平线的值
                #     y1_label="Price",
                #     y2_label="Gua",
                #     hline_label=f"qushi: ", # 图例标签
                #     title="Price vs Gua",
                #     filename = f"trade_logs/多{i+count}-Price-Gua-Rsi.png"
                # ) 

                          
                ##计算超多做空  
                elif smooth_rsi_window[-1]>= stats['conf_interval_2sigma'][1]:
                    
                    singleTop = singleTop[1:] + [-1]   # Top信号在本周期为-1 (做空)
                    singleDown = singleDown[1:] + [0]  # Down信号在本周期为0
                    tradeS.append(-1)
                    print(f"{i+count}超多做空信号nowPrice:{nowPrice}",smooth_rsi_window[-1],singleTop)
                    # print(f'{params["rsi_length"]}长度的rsi值和gau值：')
                    print("正态分布置信区间1σ区:", stats['conf_interval_1sigma'], "数量:", stats['n_1sigma'])
                    print("正态分布置信区间2σ区:", stats['conf_interval_2sigma'], "数量:", stats['n_2sigma'])
                    print("正态分布置信区间3σ区:", stats['conf_interval_3sigma'], "数量:", stats['n_3sigma'])
                    print(cline_df.tail(2))
                    good_bad,df_zf=analyze_kline_performance(any_df,-1)
                    any_good_bad.append(good_bad)
                    # print(any_df)
                    print(good_bad,"\n",df_zf)
                #     generate_dual_axis_plot_V3(
                #     y1_values=a,
                #     y2_values=b,
                #     hline_y=50, # 水平线的值
                #     y1_label="Price",
                #     y2_label="Gua",
                #     hline_label=f"qushi: ", # 图例标签
                #     title="Price vs Gua",
                #     filename = f"trade_logs/空{i+count}-Price-Gua-Rsi.png"
                # ) 


                else:
                    
                     # 正确的移位逻辑：各自独立更新历史
                    singleTop = singleTop[1:] + [0]    # 两种信号在本周期都为0
                    singleDown = singleDown[1:] + [0]
                    tradeS.append(0)
                    # print(f'{i+count}震荡',smooth_rsi_window[-1],stats['conf_interval_2sigma'],singleTop,singleDown)  
                
                
        if i+count>=len(df):
            break
    # print(any_good_bad)
    tradeS = [0] * (count - 1) + tradeS+[0]*9
    print(len(df),len(tradeS))
    # analysis_results_cn = analyze_trade_results_advanced(any_good_bad)
    # import json
    # print(json.dumps(analysis_results_cn, indent=4, ensure_ascii=False))
    df['tradeS'] = tradeS

    # plot_result = plot_kline_with_trade_signals(df, title='交易信号分析图')
    from live_tra_juece import advanced_trade_analysis,print_comprehensive_analysis
       # 进行高级分析
    result = advanced_trade_analysis(any_good_bad)
    
    # 打印分析报告

    print_comprehensive_analysis(any_good_bad)
    # print(any_good_bad)
    return None




if __name__ == "__main__":
    main()