
import ccxt
import pandas as pd
import time
from datetime import datetime, timezone, timedelta
import sys
# 假设您的okx_sdkV1文件与此脚本在同一目录下
from ku import *
from config import  calculate_kline_needed,get_kline_data,add_rsi_to_kline_data


from tradeing import check_trading_single ,judge_signal,calc_price,batch_judge_trade

from okx_sdkV1 import get_kline_data, OKXClient 
from emailHl import send_email_with_attachment_enhanced

# ==============================================================================
# 0. 时区配置
# ==============================================================================
# 定义北京时区 (UTC+8)
BEIJING_TZ = timezone(timedelta(hours=8))


# ==============================================================================
# 1. 参数配置 (Separation of Concerns: Configuration)
# ==============================================================================
# 所有策略和程序的配置都集中在这里
my_pram = {
    # --- 技术指标参数 ---
    "rsi_length": 8,
    "macd_Fast": 12, # 通常指MACD中的慢速EMA周期 (
    "macd_Slow": 26, # 通常指MACD中的快速EMA周期 (
    "macd_Signal": 9, # 通常指MACD中信号线EMA周期 (
    "my_sma_length": 5,
    "my_ema_length": 5,
    "my_gaussian_sigma": 1.39,
    
    # --- 策略与回测参数 ---
    "max_lookback_kline_count": 100, # 保证指标计算有足够数据量的最小K线数量
    "zhiyinPre": 2,
    "zhisunPre": 1,
    
    # --- 实时运行参数 ---
    "windowsTime": 12,
    "Uname": "doge", # 交易币种名称
    "Ktime": "15m",  # K线的时间周期 ('1m', '5m', '15m', '30m', '1h', '4h', '1d')
    
    # --- 历史数据参数 (主要用于回测, 实时模式下不直接使用) ---
    "strTime": "2025-03-01",
    "endTime": "2025-04-07"
}
# ==============================================================================
# 0. 时区配置
# ==============================================================================
# 定义北京时区 (UTC+8)
BEIJING_TZ = timezone(timedelta(hours=8))

if __name__ == "__main__":
    params = my_pram
    symbol = params["Uname"].upper() + "-USDT-SWAP" # OKX 交易对格式通常是 XXX-USDT
    interval = params["Ktime"]
    print("=== OKX交易所SDK测试 ===\n")
    try:
        # 注意: 您的 OKXClient 和 get_kline_data 需要能被正常导入
        client = OKXClient()
    except Exception as e:
        print(f"初始化OKX客户端失败: {e}")
    
    needed_klines = calculate_kline_needed(params)
    print(f"计算得出需要获取的K线数量: {needed_klines}")

    kline_df = get_kline_data(client, symbol, interval, count= needed_klines)
    # print(kline_df.head())
    # print(kline_df)


    if kline_df is not None and not kline_df.empty:
        print(f"成功获取 {len(kline_df)} 条 {symbol} 的K线数据。")
        print("最新一条K线数据:")
        print(kline_df.tail(2))
        close_values = kline_df['close'].values  # 预提取，避免重复调用
        # close_values  = close_values[0 : params["max_lookback_kline_count"]]  # 使用预提取的数据
        nowPrice = close_values[- 1]  # 获取最新值
        # print(close_values)
        # print(f'zuixinjiage{nowPrice}')
        
        window_close = close_values[:-1]  # 使用预提取的数据 
        # print(window_close)
        # print(f'windiw_close:{len(window_close)}')     
        
        ##计算macd指标
        window_MACD = MACD_calculation(window_close, fast_period=params["macd_Fast"],slow_period=params["macd_Slow"],
            signal_period=params["macd_Signal"])
        window_MACD=fast_macd_cross_check(window_close, fast_period=params["macd_Fast"],slow_period=params["macd_Slow"],
            signal_period=params["macd_Signal"])
        print(f"MACD交叉信号:{window_MACD['cross_type']}")
        print("-" * 50)
       
        ##计算rsi会导致长度减1
        window_rsi = fast_rsi_calculation(window_close, params["rsi_length"])
        valid_rsi = window_rsi[~np.isnan(window_rsi)]
        print(f'valid_rsi:{len(valid_rsi)}')
        # print(valid_rsi)
        if len(valid_rsi) > 0:
            smooth_rsi_window = fast_gaussian_smooth(valid_rsi, params["my_gaussian_sigma"])
        # print(f"smooth:{len(smooth_rsi_window)}")
        # print(window_rsi[-10:])
        # print(smooth_rsi_window)
        # print(smooth_rsi_window[-10:])
        stats = analyze_numerical_series( smooth_rsi_window [-params["max_lookback_kline_count"]:])
        print("正态分布置信区间1σ区:", stats['conf_interval_1sigma'], "数量:", stats['n_1sigma'])
        print("正态分布置信区间2σ区:", stats['conf_interval_2sigma'], "数量:", stats['n_2sigma'])
        print("正态分布置信区间3σ区:", stats['conf_interval_3sigma'], "数量:", stats['n_3sigma'])
    
        print(close_values[-8:])
        print(window_rsi[-8:])
        print(valid_rsi[-8:])
        print(smooth_rsi_window[-8:])
        a=pd.Series(close_values[1:-1])
        b=pd.Series(smooth_rsi_window)
        # print(a[-10:],b[-10:])
        # print(b)
    #     print(len(a),len(b))
    #     # 调用函数
        generate_dual_axis_plot_V3(
        y1_values=a,
        y2_values=b,
        hline_y=50, # 水平线的值
        y1_label="Price",
        y2_label="Gua",
        hline_label=f"qushi: ", # 图例标签
        title="Price vs Gua",
        filename = "Price-Gua-Rsi.png"
     ) 