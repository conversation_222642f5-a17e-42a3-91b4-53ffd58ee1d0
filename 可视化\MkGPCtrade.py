import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
import matplotlib.pyplot as plt
import seaborn as sns
from mpl_toolkits.mplot3d import Axes3D # 导入用于3D绘图的模块

# 设置 Matplotlib 支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei'] # 或者 'Arial Unicode MS'
plt.rcParams['axes.unicode_minus'] = False

def perform_clustering(df: pd.DataFrame, n_clusters: int = 3, random_state: int = 42) -> tuple:
    """
    对包含时间、价格、交易信号的数据进行K-Means聚类分析。
    特别处理：假设交易信号为0时，价格也为0。此脚本将这些点纳入聚类。

    参数:
    df (pd.DataFrame): 待聚类的数据框，包含 '时间', '价格', '交易信号' 列。
                       '时间' 列假定为 Unix 时间戳或其他可转换为数值的格式。
                       '交易信号' 列假定为 -1, 0, 1。
    n_clusters (int): 聚类的目标簇数量。
    random_state (int): 随机种子，用于K-Means初始化，保证结果可复现性。

    返回:
    tuple: (df_clustered, kmeans_model, scaler)
           df_clustered (pd.DataFrame): 原始数据框，新增 'cluster' 列表示所属簇。
           kmeans_model (KMeans): 训练好的K-Means模型。
           scaler (StandardScaler): 用于特征缩放的Scaler对象。
    """
    if not all(col in df.columns for col in ['时间', '价格', '交易信号']):
        raise ValueError("数据框必须包含 '时间', '价格', '交易信号' 列。")

    print("--- 数据预处理开始 ---")

    df_processed = df.copy()

    # 1. 时间特征处理: 确保时间列是数值类型 (Unix 时间戳)
    if pd.api.types.is_datetime64_any_dtype(df_processed['时间']):
        print("将 '时间' 列从 datetime 类型转换为 Unix 时间戳（秒级）。")
        # astype(np.int64) 得到纳秒级时间戳，再除以 10**9 转换为秒级
        df_processed['时间'] = df_processed['时间'].astype(np.int64) // 10**9
    elif not pd.api.types.is_numeric_dtype(df_processed['时间']):
        try:
            print("尝试将 '时间' 列转换为数值类型。")
            df_processed['时间'] = pd.to_numeric(df_processed['时间'], errors='coerce')
            df_processed.dropna(subset=['时间'], inplace=True) # 删除无法转换的行
        except Exception as e:
            raise TypeError(f"无法将 '时间' 列转换为数值类型。请检查数据格式。错误: {e}")

    # 特殊条件处理：如果交易信号为0，价格强制设置为0 (假设这是数据的固有属性)
    # 如果您希望在聚类前排除这些点，可以在这里添加过滤逻辑
    # 例如：df_processed = df_processed[~((df_processed['交易信号'] == 0) & (df_processed['价格'] == 0))]
    print("注意: 脚本默认将 '交易信号为0时价格为0' 的数据点纳入聚类。")
    # 这一行代码是为了在数据不严格满足该条件时，强制使其符合，如果您的数据已确保此条件，则无需。
    # df_processed.loc[df_processed['交易信号'] == 0, '价格'] = 0

    # 选择用于聚类的特征
    features = ['时间', '价格', '交易信号']
    X = df_processed[features].copy()

    # 2. 特征缩放: 使用 StandardScaler
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    print("特征已使用 StandardScaler 进行缩放。")

    print("--- 数据预处理完成 ---")
    print(f"--- K-Means 聚类开始 (目标簇数: {n_clusters}) ---")

    # 3. K-Means 聚类
    kmeans = KMeans(n_clusters=n_clusters, random_state=random_state, n_init='auto')
    df_processed['cluster'] = kmeans.fit_predict(X_scaled)
    print("K-Means 聚类完成。")
    print(f"聚类中心 (原始尺度):\n{pd.DataFrame(scaler.inverse_transform(kmeans.cluster_centers_), columns=features)}")

    print("--- 聚类分析结果可视化开始 ---")
    # 4. 结果可视化：三维散点图
    fig = plt.figure(figsize=(12, 10))
    ax = fig.add_subplot(111, projection='3d')

    # 根据聚类结果绘制散点图
    scatter = ax.scatter(df_processed['时间'], df_processed['价格'], df_processed['交易信号'],
                         c=df_processed['cluster'], cmap='viridis', s=60, alpha=0.8,
                         label='数据点')

    # 绘制聚类中心 (逆缩放回原始尺度)
    cluster_centers_original_scale = scaler.inverse_transform(kmeans.cluster_centers_)
    ax.scatter(cluster_centers_original_scale[:, 0],
               cluster_centers_original_scale[:, 1],
               cluster_centers_original_scale[:, 2],
               marker='X', s=200, color='red', label='聚类中心', zorder=10, edgecolor='black')

    ax.set_xlabel('时间')
    ax.set_ylabel('价格')
    ax.set_zlabel('交易信号')
    ax.set_title(f'K-Means 聚类结果 ({n_clusters} 簇)')
    plt.colorbar(scatter, ax=ax, label='簇')
    ax.legend()
    plt.tight_layout()
    plt.show()

    # 可选：二维投影图，方便查看各个维度之间的关系
    fig, axes = plt.subplots(1, 3, figsize=(20, 6))

    # 时间 vs 价格
    sns.scatterplot(x='时间', y='价格', hue='cluster', data=df_processed, palette='viridis', ax=axes[0], s=50, alpha=0.7)
    axes[0].set_title('时间 vs 价格')
    axes[0].set_xlabel('时间')
    axes[0].set_ylabel('价格')
    axes[0].grid(True, linestyle='--', alpha=0.6)

    # 时间 vs 交易信号
    sns.scatterplot(x='时间', y='交易信号', hue='cluster', data=df_processed, palette='viridis', ax=axes[1], s=50, alpha=0.7)
    axes[1].set_title('时间 vs 交易信号')
    axes[1].set_xlabel('时间')
    axes[1].set_ylabel('交易信号')
    axes[1].set_yticks([-1, 0, 1])
    axes[1].grid(True, linestyle='--', alpha=0.6)

    # 价格 vs 交易信号
    sns.scatterplot(x='价格', y='交易信号', hue='cluster', data=df_processed, palette='viridis', ax=axes[2], s=50, alpha=0.7)
    axes[2].set_title('价格 vs 交易信号')
    axes[2].set_xlabel('价格')
    axes[2].set_ylabel('交易信号')
    axes[2].set_yticks([-1, 0, 1])
    axes[2].grid(True, linestyle='--', alpha=0.6)

    plt.tight_layout()
    plt.show()

    print("--- 聚类分析结果可视化完成 ---")

    return df_processed, kmeans, scaler

if __name__ == '__main__':
    # --- 创建示例数据框，包含交易信号为0时价格为0的场景 ---
    print("--- 创建示例数据框 ---")
    data = {
        '时间': pd.to_datetime([
            '2025-07-01 09:00:00', '2025-07-01 09:05:00', '2025-07-01 09:10:00', # 信号1
            '2025-07-01 10:00:00', '2025-07-01 10:05:00', '2025-07-01 10:10:00', # 信号1
            '2025-07-01 11:00:00', '2025-07-01 11:05:00', '2025-07-01 11:10:00', # 信号0，价格0
            '2025-07-02 09:00:00', '2025-07-02 09:05:00', '2025-07-02 09:10:00', # 信号-1
            '2025-07-02 10:00:00', '2025-07-02 10:05:00', '2025-07-02 10:10:00', # 信号-1
            '2025-07-02 11:00:00', '2025-07-02 11:05:00', '2025-07-02 11:10:00', # 信号0，价格0
            '2025-07-03 09:00:00', '2025-07-03 09:05:00', '2025-07-03 09:10:00'  # 信号1
        ]),
        '价格': [
            100, 102, 101, 105, 107, 106,
            0, 0, 0, # 交易信号为0时价格为0
            90, 88, 89, 92, 91, 93,
            0, 0, 0, # 交易信号为0时价格为0
            110, 112, 111
        ],
        '交易信号': [
            1, 1, 1, 1, 1, 1,
            0, 0, 0, # 交易信号为0
            -1, -1, -1, -1, -1, -1,
            0, 0, 0, # 交易信号为0
            1, 1, 1
        ]
    }
    df_example = pd.DataFrame(data)
    print("示例数据框已创建:")
    print(df_example.head())
    print("\n")

    # --- 执行聚类分析 ---
    # 可以通过肘部法则（Elbow Method）或轮廓系数（Silhouette Score）来确定最佳簇数
    # 这里我们先手动指定一个簇数作为示例
    num_clusters = 3 # 尝试将信号1、信号-1、信号0的数据聚成3类
    print(f"--- 尝试使用 {num_clusters} 个簇进行聚类 ---")

    clustered_df, model, scaler_obj = perform_clustering(df_example, n_clusters=num_clusters)

    print("\n--- 聚类结果示例 (前5行) ---")
    print(clustered_df.head())

    print("\n--- 每个簇的样本数量 ---")
    print(clustered_df['cluster'].value_counts().sort_index())

    print("\n--- 每个簇的平均特征值 (原始尺度) ---")
    # 为了更好地理解聚类结果，我们将聚类中心逆变换回原始尺度
    # 首先，为方便计算，我们按cluster分组
    grouped_clusters = clustered_df.groupby('cluster')[['时间', '价格', '交易信号']].mean()
    print(grouped_clusters)
    
    print("\n--- 评估最佳簇数 (肘部法则) ---")
    # 肘部法则用于寻找最佳的K值
    sse = [] # Sum of Squared Errors
    k_range = range(1, 7) # 尝试1到6个簇
    features_for_elbow = clustered_df[['时间', '价格', '交易信号']]

    # 再次缩放数据，确保肘部法则使用的数据是缩放过的
    scaler_elbow = StandardScaler()
    X_elbow_scaled = scaler_elbow.fit_transform(features_for_elbow)


    for k in k_range:
        kmeans_elbow = KMeans(n_clusters=k, random_state=42, n_init='auto')
        kmeans_elbow.fit(X_elbow_scaled)
        sse.append(kmeans_elbow.inertia_) # inertia_ 是SSE的值

    plt.figure(figsize=(10, 6))
    plt.plot(k_range, sse, marker='o')
    plt.xlabel('簇数量 (K)')
    plt.ylabel('误差平方和 (SSE)')
    plt.title('肘部法则确定最佳簇数量')
    plt.grid(True, linestyle='--', alpha=0.6)
    plt.xticks(k_range)
    plt.show()
    print("通过观察肘部法则图，选择SSE下降开始变缓的“肘部”作为最佳簇数。")

    print("\n--- 评估最佳簇数 (轮廓系数) ---")
    # 轮廓系数用于衡量聚类效果，值越接近1越好
    from sklearn.metrics import silhouette_score
    silhouette_scores = []
    # 轮廓系数至少需要2个簇
    for k in range(2, 7):
        kmeans_silhouette = KMeans(n_clusters=k, random_state=42, n_init='auto')
        cluster_labels = kmeans_silhouette.fit_predict(X_elbow_scaled)
        score = silhouette_score(X_elbow_scaled, cluster_labels)
        silhouette_scores.append(score)
        print(f"簇数 K={k}, 轮廓系数: {score:.4f}")

    plt.figure(figsize=(10, 6))
    plt.plot(range(2, 7), silhouette_scores, marker='o')
    plt.xlabel('簇数量 (K)')
    plt.ylabel('轮廓系数')
    plt.title('轮廓系数确定最佳簇数量')
    plt.grid(True, linestyle='--', alpha=0.6)
    plt.xticks(range(2, 7))
    plt.show()
    print("选择轮廓系数最高的簇数作为最佳选择。")

    print("\n--- 聚类分析结束 ---")