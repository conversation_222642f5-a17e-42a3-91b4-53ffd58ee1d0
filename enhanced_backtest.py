#   my_pram = {
#     "rsi_length": 17,
#     "macd_length": 26,
#     "max_lookback_kline_count": 100,
#     "my_sma_length": 5,
#     "my_ema_length": 5,
#     "my_gaussian_sigma": 1.8,
#     "zhiyinPre":2,
#     "zhisunPre":1,
#     "windowsTime":16
# }
#     Uname='doge'
#     Ktime='15m'
#     strTime='2024-2-1'
#     endTime='2024-3-4'
#     ###获取k线
#     # 1. 计算所需获取的K线数量
#     needed_klines = calculate_kline_needed(my_pram)
#     kline_df = None
#     kline_df_with_rsi = None
#     ####历史数据回测
#     strTime=sub_kline_time(strTime, needed_klines,Ktime)
#     # print(strTime)
#     try:
#         kline_df = get_local_kline_data(Uname, Ktime, strTime, endTime)
#         print(kline_df.head())
#     except Exception as e:
#         print(e)

# ###逐段计算出
#     kline_df_with_rsi = kline_df.copy()  # 先复制，后续逐段赋值
#     kline_df_with_rsi['RSI'] = None
#     kline_df_with_rsi['RSI_Gaussian_2.0'] = None
#     kline_df_with_rsi['投资建议'] = None
#     output = ""

#     lookback_period=30
#     detector = TrendPullbackDetector(
#         lookback_period,
#         rsi_threshold_bull=50,
#         rsi_threshold_bear=50,
#         price_retracement_pct=0.382,
#         min_trend_strength=0.6,
#         smoothing_factor=0.8
#     )
    
#     tradingSingle=0  ##交易信号
#     close_values = kline_df_with_rsi['close'].values  # 预提取，避免重复调用
#     total_loops = len(kline_df_with_rsi) - needed_klines + 1
#     print(f"开始处理 {total_loops} 个窗口...")
#     for count in range(total_loops):
#     # 添加异常处理
#         nowPrice = close_values[count + needed_klines - 1]  # 使用预提取的数据
#         window_close = close_values[count : count + needed_klines]  # 使用预提取的数据
#         window_rsi = fast_rsi_calculation(window_close, my_pram["rsi_length"])
#         valid_rsi = window_rsi[~np.isnan(window_rsi)]
#         if len(valid_rsi) > 0:
#             smooth_rsi_window = fast_gaussian_smooth(valid_rsi, my_pram["my_gaussian_sigma"])
#         else:
#             continue
   
#         window_close=window_close[-lookback_period:]
#         window_rsi=window_rsi[-lookback_period:]
#         smooth_rsi_window=smooth_rsi_window[-lookback_period:]
        
#         result = detector.detect_pattern(window_close.tolist(), window_rsi.tolist(), smooth_rsi_window.tolist(), "15m")

#         next_index = count + needed_klines
        
#         # 模拟交易
#         nowSingle = judge_signal(round(result['confidence'], 2),result['pattern'])

#         tradingSingle, nowSingle = check_trading_single(tradingSingle, nowSingle)
#         # # 写入结果
#         if next_index < len(kline_df_with_rsi):  # 添加边界检查
#             kline_df_with_rsi.at[next_index, 'tradingSingle'] = tradingSingle
#             kline_df_with_rsi.at[next_index, 'nowSingle'] = nowSingle
#             if nowSingle != 0:
#                 kline_df_with_rsi.at[next_index, 'buyPrice'] = calc_price(nowSingle, nowPrice, 0)
                
#         kline_df_with_rsi.at[next_index, '识别模式'] = result['pattern']
#         kline_df_with_rsi.at[next_index, '置信度'] = f"{result['confidence']:.2%}"
#         kline_df_with_rsi.at[next_index, '建议'] = result['recommendation']
#         kline_df_with_rsi.at[next_index, '价格趋势'] = result['price_features']['trend_type']
#         kline_df_with_rsi.at[next_index, '当前RSI'] = f"{result['rsi_features']['current_rsi']:.2f}"
#         # 进度显示
#         if count % 50 == 0:
#             print(f"进度: {count}/{total_loops} ({count/total_loops*100:.1f}%)")

#     # 保存结果
#     # kline_df_with_rsi=batch_judge_trade(kline_df_with_rsi,my_pram['zhiyinPre'],my_pram['zhisunPre'],my_pram['windowsTime'],'15m')