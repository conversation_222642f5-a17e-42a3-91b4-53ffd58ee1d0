#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多重确认阶段性止盈止损策略
作者: AI Assistant
版本: 1.0
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import List, Dict, Tuple, Optional
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore', category=UserWarning)
from MkKu import load_json
 
class DynamicStopLossStrategy:
    """
    多重确认阶段性止盈止损策略类
    
    核心思想：
    - 阶段0: 风险期，宽松止损等待确认
    - 阶段1: 确认期，收紧止损
    - 阶段2: 安全期，止损移至盈利区
    - 阶段3: 跟踪期，动态跟踪止损
    """
    
    def __init__(self, 
                 stage1_threshold: float = 0.005,  # 进入阶段1的盈利阈值
                 stage2_threshold: float = 0.015,  # 进入阶段2的盈利阈值  
                 stage3_threshold: float = 0.025,  # 进入阶段3的盈利阈值
                 initial_stop_loss: float = 0.015, # 初始止损比例
                 stage1_stop_loss: float = 0.005,  # 阶段1止损比例
                 stage2_stop_profit: float = 0.005, # 阶段2止损设在盈利区
                 trailing_ratio: float = 0.3):     # 跟踪止损回撤比例
        """
        初始化策略参数
        
        Args:
            stage1_threshold: 进入阶段1的盈利阈值 (默认0.5%)
            stage2_threshold: 进入阶段2的盈利阈值 (默认1.5%)
            stage3_threshold: 进入阶段3的盈利阈值 (默认2.5%)
            initial_stop_loss: 初始止损比例 (默认1.5%)
            stage1_stop_loss: 阶段1止损比例 (默认0.5%)
            stage2_stop_profit: 阶段2止损设在盈利区 (默认0.5%)
            trailing_ratio: 跟踪止损回撤比例 (默认30%)
        """
        self.stage1_threshold = stage1_threshold
        self.stage2_threshold = stage2_threshold
        self.stage3_threshold = stage3_threshold
        self.initial_stop_loss = initial_stop_loss
        self.stage1_stop_loss = stage1_stop_loss
        self.stage2_stop_profit = stage2_stop_profit
        self.trailing_ratio = trailing_ratio
        
        # 交易状态
        self.reset()
    
    def reset(self):
        """重置策略状态"""
        self.position = False
        self.entry_price = 0.0
        self.current_stage = 0
        self.stop_loss = 0.0
        self.high_water_mark = 0.0
        self.trade_history = []
        self.stop_loss_history = []
        self.stage_history = []
        self.profit_history = []
        
    def enter_position(self, price: float) -> Dict:
        """
        开仓
        
        Args:
            price: 开仓价格
            
        Returns:
            Dict: 开仓信息
        """
        self.position = True
        self.entry_price = price
        self.current_stage = 0
        self.stop_loss = price * (1 - self.initial_stop_loss)
        self.high_water_mark = price
        
        result = {
            'action': 'ENTER',
            'price': price,
            'stage': self.current_stage,
            'stop_loss': self.stop_loss,
            'profit_pct': 0.0
        }
        
        self.trade_history.append(result)
        return result
    
    def update_position(self, price: float, period: int) -> Dict:
        """
        更新持仓状态
        
        Args:
            price: 当前价格
            period: 当前周期
            
        Returns:
            Dict: 更新信息
        """
        if not self.position:
            return {'action': 'NO_POSITION', 'price': price}
        
        # 更新最高水位
        self.high_water_mark = max(self.high_water_mark, price)
        
        # 计算当前盈利
        current_profit = (price - self.entry_price) / self.entry_price
        
        # 检查是否需要升级阶段
        self._update_stage(price, current_profit)
        
        # 更新止损
        self._update_stop_loss(price, current_profit)
        
        # 检查是否触发止损
        if price <= self.stop_loss:
            result = self._exit_position(price, period, 'STOP_LOSS')
        else:
            result = {
                'action': 'HOLD',
                'price': price,
                'stage': self.current_stage,
                'stop_loss': self.stop_loss,
                'profit_pct': current_profit * 100,
                'high_water_mark': self.high_water_mark
            }
        
        # 记录历史
        self.stop_loss_history.append(self.stop_loss)
        self.stage_history.append(self.current_stage)
        self.profit_history.append(current_profit * 100)
        
        return result
    
    def _update_stage(self, price: float, profit: float):
        """更新交易阶段"""
        old_stage = self.current_stage
        
        if self.current_stage == 0 and profit >= self.stage1_threshold:
            self.current_stage = 1
        elif self.current_stage == 1 and profit >= self.stage2_threshold:
            self.current_stage = 2
        elif self.current_stage == 2 and profit >= self.stage3_threshold:
            self.current_stage = 3
            
        if old_stage != self.current_stage:
            print(f"阶段升级: {old_stage} -> {self.current_stage}, 价格: {price:.5f}, 盈利: {profit*100:.2f}%")
    
    def _update_stop_loss(self, price: float, profit: float):
        """更新止损价格"""
        if self.current_stage == 0:
            # 阶段0: 保持初始止损
            pass
        elif self.current_stage == 1:
            # 阶段1: 收紧止损
            new_stop = self.entry_price * (1 - self.stage1_stop_loss)
            self.stop_loss = max(self.stop_loss, new_stop)
        elif self.current_stage == 2:
            # 阶段2: 止损移至盈利区
            new_stop = self.entry_price * (1 + self.stage2_stop_profit)
            self.stop_loss = max(self.stop_loss, new_stop)
        elif self.current_stage == 3:
            # 阶段3: 跟踪止损
            trailing_distance = (self.high_water_mark - self.entry_price) * self.trailing_ratio
            new_stop = self.high_water_mark - trailing_distance
            self.stop_loss = max(self.stop_loss, new_stop)
    
    def _exit_position(self, price: float, period: int, reason: str) -> Dict:
        """平仓"""
        final_profit = (price - self.entry_price) / self.entry_price
        
        result = {
            'action': 'EXIT',
            'reason': reason,
            'price': price,
            'period': period,
            'entry_price': self.entry_price,
            'final_profit_pct': final_profit * 100,
            'max_profit_pct': (self.high_water_mark - self.entry_price) / self.entry_price * 100,
            'final_stage': self.current_stage
        }
        
        self.position = False
        self.trade_history.append(result)
        return result
    
    def manual_exit(self, price: float, period: int) -> Dict:
        """手动平仓"""
        return self._exit_position(price, period, 'MANUAL')

def backtest_strategy(prices: List[float], 
                     entry_index: int = 0,
                     strategy_params: Optional[Dict] = None) -> Dict:
    """
    回测策略
    
    Args:
        prices: 价格序列
        entry_index: 开仓位置索引
        strategy_params: 策略参数字典
        
    Returns:
        Dict: 回测结果
    """
    if strategy_params is None:
        strategy_params = {}
    
    strategy = DynamicStopLossStrategy(**strategy_params)
    
    # 开仓
    entry_price = prices[entry_index]
    enter_result = strategy.enter_position(entry_price)
    print(f"开仓: 价格={entry_price:.5f}")
    
    # 逐步更新
    results = [enter_result]
    shouyi_result=0
    for i in range(entry_index + 1, len(prices)):
        result = strategy.update_position(prices[i], i)
        results.append(result)
        # print(results)
        
        # if result['action'] == 'HOLD':
        #     print(f"Action: HOLD, Stop Loss: {result['stop_loss']:.5f}")
        # elif result['action'] == 'EXIT':
        #     print(f"Action: EXIT, Reason: {result['reason']}")
        # else:
        #     print(f"Action: {result['action']}")
        
        # 如果平仓则结束
        if result['action'] == 'EXIT':
            # print(f"\n=== 交易结束 ===")
            # print(f"平仓原因: {result['reason']}")
            print(f"平仓价格: {result['price']:.5f}")
            # print(f"持有周期: {result['period'] - entry_index} 天")
            print(f"最终收益: {result['final_profit_pct']:.2f}%")
            # print(f"最大浮盈: {result['max_profit_pct']:.2f}%")
            # print(f"最终阶段: {result['final_stage']}")
            shouyi_result=round(result['final_profit_pct'], 2)
            break
    
    # 如果没有触发止损，手动平仓
    if strategy.position:
        final_result = strategy.manual_exit(prices[-1], len(prices) - 1)
        results.append(final_result)
        # print(f"\n=== 交易结束 (持有到最后) ===")
        # print(f"最终价格: {final_result['price']:.5f}")
        # print(f"持有周期: {len(prices) - entry_index - 1} 天")
        # print(f"最终收益: {final_result['final_profit_pct']:.2f}%")
        # print(f"最大浮盈: {final_result['max_profit_pct']:.2f}%")
        # print(f"最终阶段: {final_result['final_stage']}")
        shouyi_result=round(final_result['final_profit_pct'], 2)
    
  
    return {
        'strategy': strategy,
        'results': results,
        'prices': prices,
        'entry_index': entry_index,
        'shouyi_result': shouyi_result
    }

def plot_backtest_results(backtest_data: Dict, save_path: Optional[str] = None):
    """
    绘制回测结果图表
    
    Args:
        backtest_data: 回测数据
        save_path: 保存路径，如果为None则显示图表
    """
    strategy = backtest_data['strategy']
    prices = backtest_data['prices']
    entry_index = backtest_data['entry_index']
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))
    
    # 价格和止损线图
    x = range(len(prices))
    ax1.plot(x, prices, 'b-', linewidth=2, label='价格走势')
    
    # 绘制止损线
    stop_loss_full = [np.nan] * len(prices)
    for i, stop in enumerate(strategy.stop_loss_history):
        if i + entry_index + 1 < len(prices):
            stop_loss_full[i + entry_index + 1] = stop
    
    ax1.plot(x, stop_loss_full, 'r--', linewidth=2, label='动态止损线')
    ax1.axhline(y=strategy.entry_price, color='g', linestyle=':', linewidth=2, label='开仓价格')
    ax1.axvline(x=entry_index, color='g', linestyle=':', alpha=0.7, label='开仓时间')
    
    # 添加阶段背景色
    stage_colors = ['lightcoral', 'lightsalmon', 'lightgreen', 'lightblue']
    stage_names = ['阶段0(风险期)', '阶段1(确认期)', '阶段2(安全期)', '阶段3(跟踪期)']
    
    if strategy.stage_history:
        for i in range(len(strategy.stage_history)):
            stage = strategy.stage_history[i]
            x_pos = entry_index + 1 + i
            if x_pos < len(prices):
                ax1.axvspan(x_pos, x_pos + 1, alpha=0.3, color=stage_colors[stage])
    
    ax1.set_title('多重确认阶段性止盈止损策略回测结果', fontsize=14, fontweight='bold')
    ax1.set_xlabel('时间 (天)')
    ax1.set_ylabel('价格')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 盈利曲线图
    if strategy.profit_history:
        profit_x = range(entry_index + 1, entry_index + 1 + len(strategy.profit_history))
        ax2.plot(profit_x, strategy.profit_history, 'purple', linewidth=2, label='浮动盈亏 (%)')
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax2.fill_between(profit_x, strategy.profit_history, 0, alpha=0.3, color='purple')
    
    ax2.set_title('盈亏变化曲线', fontsize=12)
    ax2.set_xlabel('时间 (天)')
    ax2.set_ylabel('盈亏 (%)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图表已保存到: {save_path}")
    else:
        plt.show()

def compare_strategies(prices: List[float], entry_index: int = 0) -> Dict:
    """
    比较不同策略的效果
    
    Args:
        prices: 价格序列
        entry_index: 开仓位置
        
    Returns:
        Dict: 比较结果
    """
    strategies = {
        '保守型': {
            'stage1_threshold': 0.003,
            'stage2_threshold': 0.01,
            'stage3_threshold': 0.02,
            'initial_stop_loss': 0.01,
            'trailing_ratio': 0.4
        },
        '标准型': {
            'stage1_threshold': 0.005,
            'stage2_threshold': 0.015,
            'stage3_threshold': 0.025,
            'initial_stop_loss': 0.015,
            'trailing_ratio': 0.3
        },
        '激进型': {
            'stage1_threshold': 0.008,
            'stage2_threshold': 0.02,
            'stage3_threshold': 0.03,
            'initial_stop_loss': 0.02,
            'trailing_ratio': 0.25
        }
    }
    
    results = {}
    
    for name, params in strategies.items():
        print(f"\n=== 测试{name}策略 ===")
        backtest_result = backtest_strategy(prices, entry_index, params)
        
        # 提取关键指标
        strategy = backtest_result['strategy']
        final_result = backtest_result['results'][-1]
        
        if final_result['action'] == 'EXIT':
            final_profit = final_result['final_profit_pct']
            max_profit = final_result['max_profit_pct']
            holding_days = final_result['period'] - entry_index
            success = final_result['reason'] != 'STOP_LOSS'
        else:
            final_profit = (prices[-1] - strategy.entry_price) / strategy.entry_price * 100
            max_profit = (strategy.high_water_mark - strategy.entry_price) / strategy.entry_price * 100
            holding_days = len(prices) - entry_index - 1
            success = True
        
        results[name] = {
            'final_profit': final_profit,
            'max_profit': max_profit,
            'holding_days': holding_days,
            'success': success,
            'strategy_obj': strategy
        }
        
        print(f"最终收益: {final_profit:.2f}%")
        print(f"最大浮盈: {max_profit:.2f}%")
        print(f"持有天数: {holding_days}")
        print(f"是否成功: {'是' if success else '否'}")
    
    return results

def testBuy(list):
    buysellHistory=list
    resultBuy=0
    for i in range(0,len(buysellHistory[0])):
        if buysellHistory[0][i]==1:
            test_prices=buysellHistory[2][i]
            
            
            # print("\n","=== 回测 ===")
            # print(f"价格数据点数: {len(test_prices)}")
            # print(f"开仓价格: {test_prices[0]:.5f}")
            # print(f"最终价格: {test_prices[-1]:.5f}")
            # print(f"理论最大收益: {((test_prices[-1] - test_prices[0]) / test_prices[0] * 100):.2f}%\n")
            
            # 单策略回测
            # print("=== 单策略回测 ===")
            backtest_result = backtest_strategy(test_prices, entry_index=0)
            resultBuy+=backtest_result['shouyi_result']
    print(resultBuy)
    print(f"最终收益: {resultBuy:.2f}%")

def main():
    """
    主函数 - 你可以在这里传入你的价格数据进行测试
    """
    print("=== 多重确认阶段性止盈止损策略测试 ===\n")
    
    # 示例价格数据 (你可以替换为你的实际数据)
    test_prices = [
        0.32362, 0.32437, 0.3245, 0.32457, 0.3242, 0.32387, 0.3245, 0.32375, 
        0.32355, 0.32356, 0.32277, 0.32257, 0.32271, 0.32222, 0.32266, 0.32279, 
        0.32287, 0.32338, 0.32486, 0.32541, 0.32483, 0.32446, 0.3248, 0.32541, 
        0.32562, 0.32536, 0.32489, 0.32516, 0.3249, 0.32445, 0.3245, 0.32471, 
        0.32473, 0.32464, 0.32439, 0.32444, 0.3252, 0.32471, 0.32518, 0.32496, 
        0.32512, 0.32532, 0.32523, 0.32544, 0.32521, 0.324, 0.32431, 0.32459, 
        0.32493, 0.32558, 0.32717, 0.32609, 0.3259, 0.32568, 0.32571, 0.32574, 
        0.32639, 0.32598, 0.3258, 0.32604, 0.32637, 0.32647, 0.32688, 0.3264, 
        0.32571, 0.3254, 0.32543, 0.32551, 0.32541, 0.32574, 0.32577, 0.32574, 
        0.32576, 0.32639, 0.32589, 0.32602, 0.32614, 0.326, 0.32582, 0.32586, 
        0.32572, 0.32646, 0.32605, 0.32565, 0.32589, 0.32634, 0.32599, 0.32564, 
        0.32498, 0.32522, 0.32541, 0.32567, 0.32543, 0.32487, 0.32479, 0.32452, 
        0.3249, 0.32497, 0.32506, 0.32473, 0.32471, 0.32489, 0.32504, 0.3254, 
        0.32499, 0.32579, 0.32522, 0.32572, 0.32566, 0.32589, 0.32784, 0.33413, 
        0.33446, 0.33346, 0.33577, 0.33635, 0.33684
    ]
    test_prices = [
    0.32341, 0.32356, 0.32416, 0.32442, 0.32416, 0.32376, 0.32372, 0.32372, 0.32307, 0.32311,
    0.3227, 0.32246, 0.32237, 0.32211, 0.32162, 0.3226, 0.32263, 0.32288, 0.3233, 0.32483,
    0.32446, 0.32418, 0.32445, 0.32479, 0.3252, 0.32502, 0.32489, 0.32487, 0.32481, 0.32412,
    0.32442, 0.32423, 0.3245, 0.3246, 0.32418, 0.32438, 0.32445, 0.32457, 0.32467, 0.32488,
    0.32496, 0.32511, 0.32502, 0.32517, 0.3248, 0.324, 0.32401, 0.32426, 0.32459, 0.32471,
    0.32557, 0.32601, 0.32582, 0.32537, 0.32555, 0.32557, 0.32546, 0.3258, 0.3258, 0.32569,
    0.32602, 0.32634, 0.32637, 0.32636, 0.32562, 0.32485, 0.32495, 0.32519, 0.32513, 0.32529,
    0.32569, 0.32574, 0.32557, 0.32572, 0.32585, 0.32589, 0.32576, 0.32579, 0.32582, 0.32582,
    0.32565, 0.32572, 0.32605, 0.32543, 0.32525, 0.32587, 0.32574, 0.32536, 0.32478, 0.32463,
    0.32523, 0.32526, 0.32532, 0.32447, 0.3244, 0.32434, 0.32446, 0.32485, 0.32485, 0.32451,
    0.32455, 0.3247, 0.32489, 0.32494, 0.3245, 0.32489, 0.3251, 0.32487, 0.32549, 0.32508,
    0.32561, 0.32784, 0.3316, 0.33098, 0.33343, 0.33439, 0.33616
]
    test_prices = [
    0.38382, 0.38555, 0.3849, 0.38525, 0.38512, 0.38646, 0.38616, 0.386, 0.38576, 0.38604,
    0.38596, 0.38612, 0.38524, 0.38528, 0.38484, 0.38563, 0.38577, 0.38573, 0.38575, 0.3856,
    0.38597, 0.38632, 0.38615, 0.38597, 0.38675, 0.38698, 0.3865, 0.38609, 0.38673, 0.38678,
    0.38687, 0.38719, 0.3879, 0.38781, 0.3878, 0.38676, 0.38639, 0.38666, 0.38699, 0.38697,
    0.38667, 0.38622, 0.386, 0.38646, 0.38697, 0.38766, 0.38757, 0.38717, 0.38606, 0.38606,
    0.3859, 0.38666, 0.38657, 0.38731, 0.38744, 0.38689, 0.3852, 0.38621, 0.38642, 0.38671,
    0.38628, 0.38569, 0.38444, 0.38305, 0.38075, 0.38088, 0.37809, 0.37398, 0.3729
]
    test_prices = [
    0.32341, 0.32356, 0.32416, 0.32442, 0.32416, 0.32376, 0.32372, 0.32372, 0.32307, 0.32311,
    0.3227, 0.32246, 0.32237, 0.32211, 0.32162, 0.3226, 0.32263, 0.32288, 0.3233, 0.32483,
    0.32446, 0.32418, 0.32445, 0.32479, 0.3252, 0.32502, 0.32489, 0.32487, 0.32481, 0.32412,
    0.32442, 0.32423, 0.3245, 0.3246, 0.32418, 0.32438, 0.32445, 0.32457, 0.32467, 0.32488,
    0.32496, 0.32511, 0.32502, 0.32517, 0.3248, 0.324, 0.32401, 0.32426, 0.32459, 0.32471,
    0.32557, 0.32601, 0.32582, 0.32537, 0.32555, 0.32557, 0.32546, 0.3258, 0.3258, 0.32569,
    0.32602, 0.32634, 0.32637, 0.32636, 0.32562, 0.32485, 0.32495, 0.32519, 0.32513, 0.32529,
    0.32569, 0.32574, 0.32557, 0.32572, 0.32585, 0.32589, 0.32576, 0.32579, 0.32582, 0.32582,
    0.32565, 0.32572, 0.32605, 0.32543, 0.32525, 0.32587, 0.32574, 0.32536, 0.32478, 0.32463,
    0.32523, 0.32526, 0.32532, 0.32447, 0.3244, 0.32434, 0.32446, 0.32485, 0.32485, 0.32451,
    0.32455, 0.3247, 0.32489, 0.32494, 0.3245, 0.32489, 0.3251, 0.32487, 0.32549, 0.32508,
    0.32561, 0.32784, 0.3316, 0.33098, 0.33343, 0.33439, 0.33616
]
    
    buysellHistory=load_json('buysellHistory.json')
    # print(buysellHistory[0][0])
    # print(buysellHistory[1][0])
    # print(buysellHistory[2][0])
    print(len(buysellHistory[0]))
    
    resultBuy=0
    for i in range(0,len(buysellHistory[0])):
        if buysellHistory[0][i]==1:
            test_prices=buysellHistory[1][i]
            
            
            print("\n","=== 回测 ===")
            print(f"价格数据点数: {len(test_prices)}")
            print(f"开仓价格: {test_prices[0]:.5f}")
            print(f"最终价格: {test_prices[-1]:.5f}")
            print(f"理论最大收益: {((test_prices[-1] - test_prices[0]) / test_prices[0] * 100):.2f}%\n")
            
            # 单策略回测
            print("=== 单策略回测 ===")
            backtest_result = backtest_strategy(test_prices, entry_index=0)
            resultBuy+=backtest_result['shouyi_result']
            print(f"历史总收益：{resultBuy}")
            break
    print(resultBuy)
    print(f"最终收益: {resultBuy:.2f}%")
    
            # # 策略比较
            # print("\n" + "="*50)
            # print("=== 策略参数比较 ===")
            # comparison_results = compare_strategies(test_prices, entry_index=0)
            
    # # 输出比较表格
    # print(f"\n{'策略类型':<10} {'最终收益':<10} {'最大浮盈':<10} {'持有天数':<10} {'是否成功':<10}")
    # print("-" * 55)
    # for name, result in comparison_results.items():
    #     print(f"{name:<10} {result['final_profit']:<10.2f} {result['max_profit']:<10.2f} "
    #           f"{result['holding_days']:<10} {'✓' if result['success'] else '✗':<10}")
    
    # # 绘制图表 (如果你安装了matplotlib)
    # try:
    #     plot_backtest_results(backtest_result)
    # except ImportError:
    #     print("\n注意: 需要安装matplotlib才能显示图表")
    #     print("安装命令: pip install matplotlib")
    
    # return backtest_result

if __name__ == "__main__":
    # 运行主函数
    result = main()
    
    # 你也可以直接传入自己的价格数据测试:
    # my_prices = [你的价格数据列表]
    # my_result = backtest_strategy(my_prices, entry_index=0)