from okx_sdkV1 import get_kline_data, OKXClient
from qushi import TrendDirection,KLineTrendAnalyzer
from MkKu import plot_capital_curve,get_local_kline_data,analyze_trend_changes,get_trade_decision_v2,calculate_trading_profit,plot_capital_curve_v1
from MKTrade import rolling_trade ,analyze_trading_performance,plot_trading_results
from Mkemail import  EmailSender
import time
import datetime

import pandas as pd
import numpy as np
from typing import List, Tuple,Dict, Any
import mplfinance as mpf
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from matplotlib.font_manager import FontProperties
from typing import Dict, List, Any
import warnings
warnings.filterwarnings('ignore', category=UserWarning)
from matplotlib.font_manager import FontProperties, findfont, FontManager

def setup_chinese_fonts():
    """配置中文字体"""
    plt.rcParams['font.sans-serif'] = [
        'SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 
        'WenQuanYi Micro Hei', 'sans-serif'
    ]
    plt.rcParams['axes.unicode_minus'] = False

def plot_kline_with_trade_signals(df: pd.DataFrame,
                                ohlc_cols: List[str] = ['open', 'high', 'low', 'close'],
                                time_col: str = 'open_time',
                                trade_col: str = 'tradeS',
                                state_col: str = 'emaStas',
                                title: str = 'K线图与交易信号',
                                figsize: tuple = (16, 10)):
    """
    绘制K线图并标记交易信号
    
    Args:
        df (pd.DataFrame): 包含K线和交易信号数据的DataFrame
        ohlc_cols (List[str]): OHLC四列的列名列表，默认['open', 'high', 'low', 'close']
        time_col (str): 时间列名，默认'open_time'
        trade_col (str): 交易信号列名，默认'tradeS'
        state_col (str): 状态列名，默认'emaStas'
        title (str): 图表标题
        figsize (tuple): 图表大小
    """
    
    # 设置中文字体
    setup_chinese_fonts()
    
    # 数据验证
    if df.empty:
        raise ValueError("DataFrame不能为空")
    
    required_cols = ohlc_cols + [time_col, trade_col]
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        raise ValueError(f"缺少必需的列: {missing_cols}")
    
    # 数据准备
    df_plot = df.copy()
    
    # 重命名OHLC列
    rename_map = {
        ohlc_cols[0]: 'Open',
        ohlc_cols[1]: 'High', 
        ohlc_cols[2]: 'Low',
        ohlc_cols[3]: 'Close'
    }
    df_plot.rename(columns=rename_map, inplace=True)
    
    # 设置时间索引
    if time_col in df_plot.columns:
        df_plot[time_col] = pd.to_datetime(df_plot[time_col])
        df_plot.set_index(time_col, inplace=True)
    else:
        # 如果没有时间列，使用虚拟时间索引
        dummy_index = pd.to_datetime(pd.date_range(start='2024-01-01', periods=len(df_plot)))
        df_plot.set_index(dummy_index, inplace=True)
    
    # 处理交易信号
    long_signals = df_plot[df_plot[trade_col] == 1]
    short_signals = df_plot[df_plot[trade_col] == -1]
    
    # 创建交易信号标记
    addplot_list = []
    
    # 做多信号 (绿色向上三角)
    if not long_signals.empty:
        long_markers = pd.Series(index=df_plot.index, dtype=float)
        long_markers.loc[long_signals.index] = long_signals['Low'] * 0.998  # 稍微低于最低价
        addplot_list.append(
            mpf.make_addplot(long_markers, type='scatter', markersize=100, 
                           marker='^', color='green', alpha=0.8)
        )
    
    # 做空信号 (红色向下三角)
    if not short_signals.empty:
        short_markers = pd.Series(index=df_plot.index, dtype=float)
        short_markers.loc[short_signals.index] = short_signals['High'] * 1.002  # 稍微高于最高价
        addplot_list.append(
            mpf.make_addplot(short_markers, type='scatter', markersize=100,
                           marker='v', color='red', alpha=0.8)
        )
    
    # 绘制K线图
    mc = mpf.make_marketcolors(up='g', down='r', inherit=True)
    s = mpf.make_mpf_style(marketcolors=mc, gridstyle=':')
    
    # 抑制警告
    with warnings.catch_warnings():
        warnings.simplefilter("ignore")
        
        try:
            if addplot_list:
                fig, axes = mpf.plot(
                    df_plot[['Open', 'High', 'Low', 'Close']], 
                    type='candle',
                    style=s,
                    addplot=addplot_list,
                    figsize=figsize,
                    returnfig=True,
                    show_nontrading=False
                )
            else:
                fig, axes = mpf.plot(
                    df_plot[['Open', 'High', 'Low', 'Close']], 
                    type='candle',
                    style=s,
                    figsize=figsize,
                    returnfig=True,
                    show_nontrading=False
                )
        except Exception as e:
            raise RuntimeError(f"绘制K线图失败: {e}")
    
    # 获取主图轴
    ax = axes[0]
    
    # 设置标题和标签
    ax.set_title(title, fontsize=14, pad=20, fontweight='bold')
    ax.set_ylabel('价格', fontsize=12)
    
    # 创建图例
    legend_elements = []
    
    if not long_signals.empty:
        legend_elements.append(
            plt.Line2D([0], [0], marker='^', color='w', markerfacecolor='green',
                      markersize=10, label='做多信号 (1)', linestyle='None')
        )
    
    if not short_signals.empty:
        legend_elements.append(
            plt.Line2D([0], [0], marker='v', color='w', markerfacecolor='red',
                      markersize=10, label='做空信号 (-1)', linestyle='None')
        )
    
    if legend_elements:
        ax.legend(handles=legend_elements, loc='upper left', fontsize=10)
    
    # 统计信息
    total_signals = len(long_signals) + len(short_signals)
    long_count = len(long_signals)
    short_count = len(short_signals)
    
    # 在图上添加统计信息
    stats_text = f"交易信号统计:\n做多: {long_count}次\n做空: {short_count}次\n总计: {total_signals}次"
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=9,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 优化布局
    plt.tight_layout()
    
    # 显示图表
    try:
        mpf.show()
    except Exception as e:
        print(f"显示图表时出错: {e}")
    
    # 返回统计信息
    return {
        'total_signals': total_signals,
        'long_signals': long_count,
        'short_signals': short_count,
        'long_signal_times': long_signals.index.tolist() if not long_signals.empty else [],
        'short_signal_times': short_signals.index.tolist() if not short_signals.empty else []
    }



def analyze_ema_state(
    ema_values: List[float] | Tuple[float, float, float],
    tolerance_percent: float = 0.005
) -> str:
    """
   
    """
    # 检查输入是否合法
    if not isinstance(ema_values, (list, tuple)) or len(ema_values) != 3:
        raise ValueError("输入参数 'ema_values' 必须是一个包含3个数字的列表或元组。")
    
    # 检查值是否为NaN
    if any(np.isnan(v) for v in ema_values):
        return "数据不足/无法计算"

    a, b, c = ema_values

    # --- 第一步：判定 A 和 B 的关系 ---
    # 防止 B 为 0 导致除法错误
    if b != 0 and abs(a - b) / abs(b) <= tolerance_percent:
        ab_state = "approx"  # A ≈ B
    elif a > b:
        ab_state = "greater"  # A > B
    else:
        ab_state = "less"  # A < B

    # --- 第二步：判定 B 和 C 的关系 ---
    # 防止 C 为 0 导致除法错误
    if c != 0 and abs(b - c) / abs(c) <= tolerance_percent:
        bc_state = "approx"  # B ≈ C
    elif b > c:
        bc_state = "greater"  # B > C
    else:
        bc_state = "less"  # B < C
        
    # --- 第三步：根据两个关系组合，从9宫格中匹配结果 ---
    
    # 规则 1-3: 短期动能强于中期 (A > B)
    if ab_state == "greater":
        if bc_state == "greater":   # A > B and B > C
            return "1. 强势多头 (A > B > C)"
        elif bc_state == "less":    # A > B and B < C
            return "2. 下跌中反弹"
        else:  # bc_state == "approx"  # A > B and B ≈ C
            return "3. 盘整后突破"

    # 规则 4-6: 短期动能弱于中期 (A < B)
    elif ab_state == "less":
        if bc_state == "greater":   # A < B and B > C
            return "4. 上涨中回调"
        elif bc_state == "less":    # A < B and B < C
            return "5. 强势空头 (C > B > A)"
        else:  # bc_state == "approx"  # A < B and B ≈ C
            return "6. 盘整后破位"
            
    # 规则 7-9: 短中期动能纠缠 (A ≈ B)
    else:  # ab_state == "approx"
        if bc_state == "greater":   # A ≈ B and B > C
            return "7. 上涨趋势减弱"
        elif bc_state == "less":    # A ≈ B and B < C
            return "8. 下跌趋势减弱"
        else:  # bc_state == "approx"  # A ≈ B and B ≈ C
            return "9. 极限盘整/无趋势"
        
def calculate_ema(data, period):
    """
    计算指数移动平均值 (EMA)
    
    参数:
        data: pandas Series 或 list，输入数据列
        period: int，EMA周期
    
    返回:
        pandas Series，EMA值序列，长度与输入数据相同
    """
    # 转换为pandas Series以便处理
    if not isinstance(data, pd.Series):
        data = pd.Series(data)
    
    data_length = len(data)
    
    # 长度不足，返回相同长度的0
    if data_length < period:
        return pd.Series([0.0] * data_length, index=data.index)
    
    # 计算平滑因子
    alpha = 2 / (period + 1)
    
    # 初始化结果序列
    ema_values = pd.Series([0.0] * data_length, index=data.index)
    
    # 使用前period个有效值的平均值作为初始EMA
    valid_data = data.dropna()
    if len(valid_data) < period:
        return pd.Series([0.0] * data_length, index=data.index)
    
    # 计算初始EMA（使用前period个值的简单平均）
    start_idx = period - 1
    ema_values.iloc[start_idx] = valid_data.iloc[:period].mean()
    
    # 递推计算后续EMA值
    for i in range(start_idx + 1, data_length):
        if pd.notna(data.iloc[i]):
            ema_values.iloc[i] = alpha * data.iloc[i] + (1 - alpha) * ema_values.iloc[i-1]
        else:
            ema_values.iloc[i] = ema_values.iloc[i-1]
    
    return ema_values


def calculate_ema_optimized(df, column, period, N=3):
 
    df_length = len(df)
    threshold = N * period
    
    # 情况1：df长度小于period，数据不足
    if df_length < period:
        # print(f"数据长度({df_length})小于EMA周期({period})，返回全0序列")
        return pd.Series([0.0] * df_length, index=df.index)
    
    # 情况2：df长度在[period, N*period]之间，使用全部数据
    elif df_length <= threshold:
        # print(f"数据长度({df_length})在合理范围内，使用全部数据计算EMA{period}")
        slice_df = df
    
    # 情况3：df长度大于N*period，进行切片优化
    else:
        # print(f"数据长度({df_length})大于阈值({threshold})，切片使用最后{threshold}个数据点")
        slice_df = df.iloc[-threshold:]
    
    # 计算EMA
    ema_result = calculate_ema(slice_df[column], period)
    
    # 如果进行了切片，需要将结果扩展到原始DataFrame的长度
    if len(slice_df) < df_length:
        # 创建与原始DataFrame同长度的结果序列
        full_result = pd.Series([0.0] * df_length, index=df.index)
        # 将切片计算的结果放到对应位置
        full_result.iloc[-len(slice_df):] = ema_result.values
        return full_result
    
    return ema_result


def check_and_setup_chinese_fonts():
    """
    检查并设置中文字体，避免字符缺失警告
    """
    # 抑制字体相关警告
    warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')
    
    # 检查可用的中文字体
    fm = FontManager()
    chinese_fonts = []
    
    # 常见中文字体列表
    font_candidates = [
        'SimHei',           # 黑体
        'Microsoft YaHei',  # 微软雅黑
        'SimSun',          # 宋体
        'KaiTi',           # 楷体
        'Arial Unicode MS', # Mac
        'PingFang SC',     # Mac
        'Hiragino Sans GB', # Mac
        'WenQuanYi Micro Hei', # Linux
        'Noto Sans CJK SC', # Google
        'Source Han Sans SC', # Adobe
    ]
    
    # 查找可用的中文字体
    for font_name in font_candidates:
        try:
            font_path = findfont(FontProperties(family=font_name))
            if font_path and font_name.lower() not in font_path.lower().replace('DejaVu', '').lower():
                chinese_fonts.append(font_name)
        except:
            continue
    
    # 如果没有找到中文字体，尝试手动指定路径
    if not chinese_fonts:
        manual_font_paths = [
            'C:/Windows/Fonts/simhei.ttf',     # Windows 黑体
            'C:/Windows/Fonts/msyh.ttc',       # Windows 微软雅黑
            'C:/Windows/Fonts/simsun.ttc',     # Windows 宋体
            '/System/Library/Fonts/Arial Unicode.ttc',  # Mac
            '/System/Library/Fonts/PingFang.ttc',       # Mac
            '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc', # Linux
        ]
        
        for font_path in manual_font_paths:
            try:
                import os
                if os.path.exists(font_path):
                    # 从路径提取字体名称
                    font_name = os.path.basename(font_path).split('.')[0]
                    chinese_fonts.append(font_name)
                    break
            except:
                continue
    
    # 设置字体优先级列表
    if chinese_fonts:
        font_list = chinese_fonts + ['sans-serif']
        print(f"找到中文字体: {chinese_fonts}")
    else:
        # 如果实在找不到，使用fallback方案
        font_list = ['sans-serif']
        print("警告: 未找到中文字体，将使用系统默认字体")
    
    # 全局设置字体
    plt.rcParams['font.sans-serif'] = font_list
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建字体属性对象
    try:
        if chinese_fonts:
            chinese_font = FontProperties(family=chinese_fonts[0], size=10)
        else:
            chinese_font = FontProperties(size=10)
    except:
        chinese_font = FontProperties(size=10)
    
    return chinese_font

def plot_states_with_fixed_fonts(df: pd.DataFrame,
                                ohlc_cols: list = ['open', 'high', 'low', 'close'],
                                state_col: str = 'emaStas',
                                title: str = '市场状态分析'):
    """
    修复了所有字体和警告问题的绘图函数
    """
    
    # 1. 设置中文字体
    chinese_font = check_and_setup_chinese_fonts()
    
    # 2. 数据验证
    if df.empty:
        raise ValueError("DataFrame不能为空")
    
    missing_cols = [col for col in ohlc_cols if col not in df.columns]
    if missing_cols:
        raise ValueError(f"缺少必需的OHLC列: {missing_cols}")
    
    if state_col not in df.columns:
        raise ValueError(f"状态列 '{state_col}' 不存在于DataFrame中")
    
    # 3. 数据准备
    df_plot = df.copy()
    rename_map = {
        ohlc_cols[0]: 'Open', ohlc_cols[1]: 'High',
        ohlc_cols[2]: 'Low', ohlc_cols[3]: 'Close'
    }
    df_plot.rename(columns=rename_map, inplace=True)
    dummy_index = pd.to_datetime(pd.date_range(start='2000-01-01', periods=len(df_plot)))
    df_plot.set_index(dummy_index, inplace=True)

    # 4. 状态映射
    state_map = {
        0: {'desc': '0. 无效/初始状态', 'color': (0.0, 0.0, 0.0, 0.0)},
        1: {'desc': '1. 强势多头', 'color': (0.0, 1.0, 0.0, 0.3)},
        2: {'desc': '2. 下跌中反弹', 'color': (1.0, 0.647, 0.0, 0.3)},
        3: {'desc': '3. 盘整后突破', 'color': (0.678, 0.847, 0.902, 0.4)},
        4: {'desc': '4. 上涨中回调', 'color': (0.0, 0.392, 0.0, 0.3)},
        5: {'desc': '5. 强势空头', 'color': (1.0, 0.0, 0.0, 0.3)},
        6: {'desc': '6. 盘整后破位', 'color': (1.0, 0.753, 0.796, 0.4)},
        7: {'desc': '7. 上涨趋势减弱', 'color': (1.0, 1.0, 0.0, 0.3)},
        8: {'desc': '8. 下跌趋势减弱', 'color': (0.502, 0.0, 0.502, 0.3)},
        9: {'desc': '9. 极限盘整/无趋势', 'color': (0.502, 0.502, 0.502, 0.3)}
    }

    # 5. 绘制K线图（修复警告）
    mc = mpf.make_marketcolors(up='g', down='r', inherit=True)
    s = mpf.make_mpf_style(marketcolors=mc, gridstyle=':')
    
    # 抑制mplfinance的弃用警告
    with warnings.catch_warnings():
        warnings.simplefilter("ignore", DeprecationWarning)
        warnings.simplefilter("ignore", UserWarning)
        
        try:
            fig, axes = mpf.plot(
                df_plot, 
                type='candle', 
                style=s,
                ylabel='Price', 
                figsize=(16, 10),
                returnfig=True, 
                xrotation=0, 
                show_nontrading=False  # 新参数，替代no_xgaps
            )
        except Exception as e:
            raise RuntimeError(f"绘制K线图失败: {e}")

    # 6. 状态数据处理
    def extract_state_number(state_str):
        if pd.isna(state_str):
            return None
        
        state_str = str(state_str).strip()
        if state_str.isdigit():
            return int(state_str)
        
        import re
        match = re.match(r'^(\d+)', state_str)
        if match:
            return int(match.group(1))
        return None
    
    processed_states = []
    for i, raw_state in enumerate(df_plot[state_col]):
        try:
            state = extract_state_number(raw_state)
            if state is not None and 0 <= state <= 9:
                processed_states.append(state)
            else:
                processed_states.append(None)
        except:
            processed_states.append(None)

    # 7. 绘制背景着色
    ax = axes[0]
    current_state = -1
    start_idx = 0
    
    for i, state in enumerate(processed_states):
        if state is None:
            continue
        
        if state != current_state:
            if current_state not in [-1, 0, None]:
                try:
                    ax.axvspan(start_idx - 0.5, i - 0.5,
                               color=state_map[current_state]['color'],
                               zorder=0)
                except:
                    pass
            current_state = state
            start_idx = i

    if current_state not in [-1, 0, None]:
        try:
            ax.axvspan(start_idx - 0.5, len(df_plot) - 0.5,
                       color=state_map[current_state]['color'],
                       zorder=0)
        except:
            pass

    # 8. 创建图例
    used_states = set(s for s in processed_states if s is not None and s != 0)
    if used_states:
        legend_patches = [
            mpatches.Patch(color=state_map[k]['color'], label=state_map[k]['desc'])
            for k in sorted(used_states) if k in state_map
        ]
        
        if legend_patches:
            fig.legend(handles=legend_patches, 
                      loc='upper left', 
                      fontsize=9, 
                      title='市场状态',
                      prop=chinese_font)

    # 9. 设置标题和标签
    ax.set_title(title, fontproperties=chinese_font, fontsize=14, pad=20, fontweight='bold')
    ax.set_xlabel('K线序列号', fontproperties=chinese_font, fontsize=11)
    ax.set_ylabel('价格', fontproperties=chinese_font, fontsize=11)
    
    # 10. 格式化坐标轴
    ax.xaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{int(x)}'))
    
    # 11. 设置刻度标签字体
    for label in ax.get_xticklabels() + ax.get_yticklabels():
        label.set_fontproperties(chinese_font)

    # 12. 优化布局并显示
    plt.tight_layout()
    
    # 抑制显示时的警告
    with warnings.catch_warnings():
        warnings.simplefilter("ignore")
        mpf.show()
       

        
if __name__ == "__main__":
    
    
    # 示例数据（实际使用时替换为真实数据）
    print("=== K线趋势分析系统 ===\n")
    print("=== OKX交易所SDK测试 ===\n")
    try:
        # 指定.env文件的路径
        dotenv_path = r'C:\Users\<USER>\Desktop\my_python\claudeV1\量化\123.env'
        client = OKXClient(dotenv_path=dotenv_path)
    except Exception as e:
        print(f"初始化失败: {e}")  
    symbol = 'DOGE-USDT-SWAP'
    timeframe = '3m'
    count=100##获取k线数
    ##邮箱
    sender = EmailSender()
    
    ##策略参数
    buy_lastSigle = 0
    buy_nowSigle = 0
    # 独立管理卖出信号的状态
    sell_lastSigle = 0
    sell_nowSigle = 0

    current_position = 'flat'  ##当前持仓状态 ('flat', 'long', 'short')。
    trade_direction = 'both'  ###交易方向控制 ('both', 'long_only', 'short_only')。
    tradeS = []
    tradePrice = []
    emaStas = [] # 确保在循环内每次迭代只添加一次最终状态

    # 根据用户提供的模板设置参数
    periods=[8, 16, 32]
    tolerance_percent=0.0015 ## analyze_ema_state灵敏度
    price_columns=['low', 'close', 'high'] # 根据用户提供的模板设置
    price_columns_reversed = price_columns[::-1] # 预先计算反转后的价格列
    
    
    # --- 根据 timeframe 计算每次循环的休眠秒数 ---
    timeframe_value = int(timeframe[:-1])
    timeframe_unit = timeframe[-1]

    sleep_seconds_per_interval = 0
    if timeframe_unit == 's':
        sleep_seconds_per_interval = timeframe_value
    elif timeframe_unit == 'm':
        sleep_seconds_per_interval = timeframe_value * 60
    elif timeframe_unit == 'h':
        sleep_seconds_per_interval = timeframe_value * 60 * 60
    elif timeframe_unit == 'd':
        sleep_seconds_per_interval = timeframe_value * 24 * 60 * 60
    else:
        print(f"警告: 无法识别的时间周期单位 '{timeframe_unit}'，将使用默认3分钟间隔进行模拟。")
        sleep_seconds_per_interval = 3 * 60 # 默认3分钟间隔

    print(f"系统将以 {timeframe_value}{timeframe_unit} (约 {sleep_seconds_per_interval} 秒) 为周期运行。\n")


##在线数据回测
    while True:
        # 计算下一个K线周期的开始时间
        now_utc8 = datetime.datetime.now(datetime.timezone(datetime.timedelta(hours=8))) # 北京时间
        
        # 计算当前时间落在哪个 K 线周期内，并确定下一个周期的开始时间
        # OKX的K线 open_time 是该K线周期开始的时间戳
        # 假设K线周期是整点或整分钟开始
        if timeframe_unit == 'm':
            minute_offset = now_utc8.minute % timeframe_value
            seconds_to_next_interval = (timeframe_value - minute_offset) * 60 - now_utc8.second - now_utc8.microsecond / 1e6
        elif timeframe_unit == 'h':
            hour_offset = now_utc8.hour % timeframe_value
            seconds_to_next_interval = (timeframe_value - hour_offset) * 3600 - now_utc8.minute * 60 - now_utc8.second - now_utc8.microsecond / 1e6
        elif timeframe_unit == 'd':
            # 日线需要考虑时区和天的开始
            seconds_to_next_interval = (datetime.timedelta(days=1) - datetime.timedelta(
                hours=now_utc8.hour, minutes=now_utc8.minute, seconds=now_utc8.second, microseconds=now_utc8.microsecond)).total_seconds()
        else: # 默认为秒，或无法处理的单位
            seconds_to_next_interval = sleep_seconds_per_interval - (now_utc8.second % sleep_seconds_per_interval) - now_utc8.microsecond / 1e6

        if seconds_to_next_interval < 0: # 如果已经过了下一个周期的开始时间，则立即进入下一个周期
            seconds_to_next_interval += sleep_seconds_per_interval
        
        # 确保等待时间不会过长或过短，至少等待一小段时间以避免CPU占用过高
        sleep_duration = max(1, seconds_to_next_interval) # 至少等待1秒
        
        next_run_timestamp_utc8 = now_utc8 + datetime.timedelta(seconds=sleep_duration)

        print(f"当前北京时间: {now_utc8.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"预计下一次获取数据和分析时间 (北京时间): {next_run_timestamp_utc8.strftime('%Y-%m-%d %H:%M:%S')}")
   
        try:
            slice_df= get_kline_data(client, symbol, timeframe=timeframe, count=count)
            
            # --- 独立计算买入信号相关状态和信号 ---
            LivemaS_buy = []
            for i in [0, 1, 2]:
                # 使用正常的 price_columns 顺序
                results_buy = calculate_ema_optimized(slice_df, price_columns[i], periods[i], N=3)
                LivemaS_buy.append(results_buy.iloc[-1])
            state_for_buy = analyze_ema_state(LivemaS_buy, tolerance_percent=tolerance_percent)

            # 根据新的状态更新买入信号的 lastSigle 和 nowSigle，并获取交易决策
            temp_buy_lastSigle = buy_nowSigle # 存储当前的 nowSigle 作为决策函数的 lastSigle
            buy_nowSigle = int(state_for_buy[:1]) # 更新 nowSigle
            trade_signal_buy, reason_buy = get_trade_decision_v2(temp_buy_lastSigle, buy_nowSigle, current_position=current_position, trade_direction=trade_direction)
            # --- 独立计算卖出信号相关状态和信号 ---
            LivemaS_sell = []
            for i in [0, 1, 2]:
                # 使用反转的 price_columns 顺序
                results_sell = calculate_ema_optimized(slice_df, price_columns_reversed[i], periods[i], N=3)
                LivemaS_sell.append(results_sell.iloc[-1])
            state_for_sell = analyze_ema_state(LivemaS_sell, tolerance_percent=tolerance_percent)

            # 根据新的状态更新卖出信号的 lastSigle 和 nowSigle，并获取交易决策
            temp_sell_lastSigle = sell_nowSigle # 存储当前的 nowSigle 作为决策函数的 lastSigle
            sell_nowSigle = int(state_for_sell[:1]) # 更新 nowSigle
            trade_signal_sell, reason_sell = get_trade_decision_v2(temp_sell_lastSigle, sell_nowSigle, current_position=current_position, trade_direction=trade_direction)
            # --- 最终决策逻辑：根据优先级处理信号 ---
            current_trade_signal = 0
            current_trade_price = 0
            current_ema_state = 0 # 默认状态为 0，表示“抛弃”了未导致交易的信号状态

            if trade_signal_buy == 'BUY':
                # 优先处理 BUY 信号
                current_trade_signal = 1
                current_trade_price = slice_df.iloc[-1]['close']
                current_ema_state = state_for_buy # 记录 BUY 信号时的状态
                print(f"时间：{slice_df.iloc[-1]['open_time']}：BUY {reason_buy}  {current_trade_price} ")
                sender.send_email(
                            print(f"时间：{slice_df.iloc[-1]['open_time']}：BUY {reason_buy}  {current_trade_price} "),
                            subject="buy信号报告"
                                    )

                if trade_signal_sell == 'SHORT':
                    sender.send_email(
                            print(f"时间：{slice_df.iloc[-1]['open_time']}：BUY {reason_buy}  {current_trade_price} "),
                            subject="发生买入和卖出信号冲突"
                                    )
                    # 发生买入和卖出信号冲突，打印警告
                    print(f"时间：{slice_df.iloc[-1]['open_time']}信号冲突：BUY {reason_buy}  {current_trade_price} ")
                    # 此时，BUY 信号优先，所以 current_trade_signal, current_trade_price, current_ema_state 保持为 BUY 的值

            elif trade_signal_sell == 'SHORT': # 只有在没有 BUY 信号时，才判断 SHORT 信号
                current_trade_signal = -1
                current_trade_price = slice_df.iloc[-1]['close']
                current_ema_state = state_for_sell # 记录 SHORT 信号时的状态
                print(f"时间：{slice_df.iloc[-1]['open_time']}：SHORT { reason_sell }  {current_trade_price} ")
                sender.send_email(
                            print(f"时间：{slice_df.iloc[-1]['open_time']}:SHORT { reason_sell }  {current_trade_price} "),
                            subject="SHORT 信号报告"
                                    )

            else: # 既没有 BUY 也没有 SHORT 信号
                current_trade_signal = 0
                current_trade_price = 0
                current_ema_state = 0 # 明确设置为 0，表示没有产生交易信号，因此“抛弃”了之前的分析状态
                print(f"时间：{slice_df.iloc[-1]['open_time']}： 没有产生交易信号{reason_buy}  {current_trade_price} ")
                
            # # 将当前 K 线的最终确定值添加到列表中（每轮循环只执行一次）用于未来的日志
            # tradeS.append(current_trade_signal)
            # tradePrice.append(current_trade_price)
            # emaStas.append(current_ema_state)
            
        except Exception as e:
            error_message = f"在第 {now_utc8.strftime('%Y-%m-%d %H:%M:%S')} 时刻运行中发生错误: {e}"
            print(f"错误: {error_message}")
            sender.send_email(error_message, subject=f"{symbol} 策略运行错误")
            # 可以在这里添加更详细的错误处理，例如记录日志，或决定是否退出循环
            time.sleep(10) # 发生错误后短暂等待，避免错误循环
        time_to_wait = (next_run_timestamp_utc8 - datetime.datetime.now(datetime.timezone(datetime.timedelta(hours=8)))).total_seconds()
        
        if time_to_wait > 0:
            print(f"等待 {time_to_wait:.2f} 秒，直到下一次运行...")
            time.sleep(time_to_wait)
        else:
            print("已经错过下一次运行时间，立即进行下一次运行。")
            # 考虑如果连续多次错过时间，是否需要警告或调整策略，例如，立即执行但不计入等待时间

    

    
    