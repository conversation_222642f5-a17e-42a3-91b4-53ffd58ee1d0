import matplotlib.pyplot as plt
import matplotlib.ticker as mticker # 导入 ticker 模块，用于控制刻度
from typing import Optional
import sqlite3
import os
from datetime import datetime, timedelta
import requests
import pandas as pd
import pandas_ta as ta
from scipy.ndimage import gaussian_filter1d # 需要安装 scipy
import scipy.stats as spstats
import numpy as np # 用于生成示例数据
def generate_plot(
    y_values: list,
    title: str = "数值趋势图",
    y_label: str = "数值",
    x_label: str = "数据点索引",
    filename: str = "plot_output.png"
):
    """
    根据提供的Y轴数值自动生成并保存一个折线图。

    Args:
        y_values (list): 用于Y轴的数值列表。可以是list, NumPy数组或Pandas Series。
        title (str): 图表的标题，默认为"数值趋势图"。
        y_label (str): Y轴的标签，默认为"数值"。
        x_label (str): X轴的标签，默认为"数据点索引"。
        filename (str): 保存图表的文件名（例如："my_chart.png"），默认为"plot_output.png"。
    """
    # 检查 y_values 的类型
    if not isinstance(y_values, (list, np.ndarray, pd.Series)):
        print("错误: y_values 必须是列表、NumPy数组或Pandas Series。")
        return

    # 检查 y_values 是否为空
    # 对于 list 和 NumPy 数组，len() == 0 即可
    # 对于 Pandas Series，len() == 0 也适用，或者使用 .empty 属性
    if len(y_values) == 0: # 这是一个更通用的检查空序列的方法
        print("错误: y_values 不能为空，它不包含任何数据点。")
        return

    # 自动生成X轴值，即数据的索引
    x_values = range(len(y_values))

    plt.figure(figsize=(10, 6)) # 设置图表大小
    # marker='o', linestyle='-' 添加点标记和连接线，markersize=4 设置点大小
    plt.plot(x_values, y_values, marker='o', linestyle='-', markersize=4)

    plt.title(title)      # 设置图表标题
    plt.xlabel(x_label)   # 设置X轴标签
    plt.ylabel(y_label)   # 设置Y轴标签
    plt.grid(True)        # 显示网格

    # 自动调整布局，防止标签重叠
    plt.tight_layout()

    # 保存图表到文件
    try:
        plt.savefig(filename)
        print(f"图表已成功保存为: {filename}")
    except Exception as e:
        print(f"保存图表时发生错误: {e}")
    finally:
        plt.close() # 关闭图表，释放内存


def get_local_kline_data(symbol, interval, start_time, end_time, db_path='Kdatas/kline.db'):
    """
    从本地SQLite数据库获取K线数据，支持日期和精确到秒的时间格式
    """
    if not os.path.isfile(db_path):
        raise FileNotFoundError(f"数据库文件不存在: {db_path}")
    table_name = f"{symbol.lower()}{interval.lower()}"

    def _parse_time(tstr):
        # 兼容多种格式
        tried_formats = [
            "%Y-%m-%d %H:%M:%S",
            "%Y-%m-%d",
            "%Y/%m/%d %H:%M:%S",
            "%Y/%m/%d"
        ]
        for fmt in tried_formats:
            try:
                return datetime.strptime(tstr, fmt)
            except:
                continue
        raise ValueError(f"时间格式错误: {tstr}")

    # 保留原始字符串，供SQL用
    def _normalize_time_str(tstr):
        # 自动补齐到"YYYY-MM-DD HH:MM:SS"
        dt = _parse_time(tstr)
        return dt.strftime('%Y-%m-%d %H:%M:%S')

    norm_start = _normalize_time_str(start_time)
    norm_end = _normalize_time_str(end_time)
    if norm_start > norm_end:
        raise ValueError("起始时间不能晚于结束时间")

    conn = sqlite3.connect(db_path)
    try:
        # 用完整时间字符串做条件
        query = f"""
        SELECT * FROM {table_name}
        WHERE open_time >= ?
          AND open_time <= ?
        ORDER BY open_time
        """
        params = (norm_start, norm_end)
        df = pd.read_sql(query, conn, params=params)
        if df.empty:
            min_time_q = f"SELECT min(open_time) FROM {table_name}"
            max_time_q = f"SELECT max(open_time) FROM {table_name}"
            min_time = conn.execute(min_time_q).fetchone()[0]
            max_time = conn.execute(max_time_q).fetchone()[0]
            if not min_time or not max_time:
                raise ValueError(f"本地数据库没有该symbol/interval的数据: {table_name}")
            raise ValueError(f"所选时间区间无数据。可用区间: {min_time} ~ {max_time}")
        else:
            return df
    finally:
        conn.close()
        
def test_print_kline_head(symbol, interval, db_path='Kdatas/kline.db', n=5):
    """
    测试输出数据库中指定表的前n行数据
    """
    table_name = f"{symbol.lower()}{interval.lower()}"
    conn = sqlite3.connect(db_path)
    try:
        df = pd.read_sql(f"SELECT * FROM {table_name} ORDER BY open_time LIMIT {n}", conn)
        print(f"表 {table_name} 的前 {n} 行数据：")
        print(df)
    finally:
        conn.close()
def generate_dual_axis_plotV1(
    y1_values, 
    y2_values, 
    title, 
    y1_label, 
    y2_label, 
    x_label, 
    filename, 
    y1_color='tab:blue', 
    y2_color='tab:red', 
    countS1=None, 
    countS2=None,
    signal_list=None,
    signal_markers=True,
    signal_colors=None
):
    """
    生成双轴图，并在Y1轴上标记做空和做多点
    
    Parameters:
    -----------
    y1_values: list/array - Y1轴数据（通常是RSI等指标）
    y2_values: list/array - Y2轴数据（通常是价格）
    title: str - 图表标题
    y1_label: str - Y1轴标签
    y2_label: str - Y2轴标签
    x_label: str - X轴标签
    filename: str - 保存文件名
    y1_color: str - Y1轴颜色，默认'tab:blue'
    y2_color: str - Y2轴颜色，默认'tab:red'
    countS1: list - 做多点列表（在Y1轴上标记）
    countS2: list - 做空点列表（在Y1轴上标记）
    signal_list: list - 信号列表，1表示做多，-1表示做空，0表示忽视
    signal_markers: bool - 是否显示信号标记，默认True
    signal_colors: dict - 信号颜色配置，默认{'long': 'green', 'short': 'red'}
    """
    
    # 设置默认信号颜色和标记符号（与countS1/countS2明显区分）
    if signal_colors is None:
        signal_colors = {
            'long': 'gold',        # 做多信号颜色（金色）
            'short': 'purple',     # 做空信号颜色（紫色）
            'long_marker': 's',    # 做多标记符号（正方形）
            'short_marker': 'D',   # 做空标记符号（菱形）
            'size': 60             # 标记大小
        }
    
    # 创建图形和主轴
    fig, ax1 = plt.subplots(figsize=(12, 8))
    
    # 创建X轴数据
    x_values = list(range(len(y1_values)))
    
    # 绘制Y1轴数据
    color = y1_color
    ax1.set_xlabel(x_label)
    ax1.set_ylabel(y1_label, color=color)
    line1 = ax1.plot(x_values, y1_values, color=color, linewidth=1.5, label=y1_label)
    ax1.tick_params(axis='y', labelcolor=color)
    ax1.grid(True, alpha=0.3)
    
    # 创建第二个Y轴
    ax2 = ax1.twinx()
    color = y2_color
    ax2.set_ylabel(y2_label, color=color)
    line2 = ax2.plot(x_values, y2_values, color=color, linewidth=1.5, label=y2_label)
    ax2.tick_params(axis='y', labelcolor=color)
    
    # 标记原有的countS1和countS2点（保持原有的三角形标记）
    if countS1 is not None:
        for idx in countS1:
            if 0 <= idx < len(y1_values):
                ax1.scatter(idx, y1_values[idx], color='lime', s=120, marker='^', 
                           edgecolors='darkgreen', linewidth=2, zorder=5, 
                           label='CountS1 做多 (▲)' if idx == countS1[0] else "")
    
    if countS2 is not None:
        for idx in countS2:
            if 0 <= idx < len(y1_values):
                ax1.scatter(idx, y1_values[idx], color='orangered', s=120, marker='v', 
                           edgecolors='darkred', linewidth=2, zorder=5,
                           label='CountS2 做空 (▼)' if idx == countS2[0] else "")
    
    # 处理新的信号列表
    if signal_list is not None and signal_markers:
        # 确保信号列表长度与y1_values匹配
        if len(signal_list) != len(y1_values):
            print(f"警告：信号列表长度({len(signal_list)})与Y1数据长度({len(y1_values)})不匹配")
            # 截取或填充到匹配长度
            if len(signal_list) > len(y1_values):
                signal_list = signal_list[:len(y1_values)]
            else:
                signal_list = signal_list + [0] * (len(y1_values) - len(signal_list))
        
        # 分别收集做多和做空信号的位置
        long_positions = []
        short_positions = []
        
        for i, signal in enumerate(signal_list):
            if signal == 1:  # 做多信号
                long_positions.append(i)
            elif signal == -1:  # 做空信号
                short_positions.append(i)
        
        # 绘制做多信号（正方形标记）
        if long_positions:
            long_y_values = [y1_values[i] for i in long_positions]
            ax1.scatter(long_positions, long_y_values, 
                       color=signal_colors['long'], s=signal_colors['size'], 
                       marker=signal_colors['long_marker'], 
                       edgecolors='darkorange', linewidth=2, zorder=6,
                       label='MACD-duo (■)', alpha=0.9)
        
        # 绘制做空信号（菱形标记）
        if short_positions:
            short_y_values = [y1_values[i] for i in short_positions]
            ax1.scatter(short_positions, short_y_values, 
                       color=signal_colors['short'], s=signal_colors['size'], 
                       marker=signal_colors['short_marker'], 
                       edgecolors='indigo', linewidth=2, zorder=6,
                       label='MACD-kong (◆)', alpha=0.9)
        
        # 打印信号统计
        print(f"信号统计：")
        print(f"  做多信号数量: {len(long_positions)}")
        print(f"  做空信号数量: {len(short_positions)}")
        print(f"  做多信号位置: {long_positions}")
        print(f"  做空信号位置: {short_positions}")
    
    # 设置标题
    plt.title(title, fontsize=14, fontweight='bold')
    
    # 创建图例
    lines1, labels1 = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left', 
               bbox_to_anchor=(0.02, 0.98), fontsize=10)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"图表已保存到: {filename}")
    
    # 显示图片
    plt.show()
    
    return fig, ax1, ax2

# 使用示例：
# generate_dual_axis_plotV1(
#     y1_values=a,
#     y2_values=b,
#     title=f"DOGEUSDT 15m (RSI & 价格)",
#     y1_label=f"RSI (周期: {my_pram['rsi_length']})",
#     y2_label="DOGEUSDT 价格",
#     x_label="K线索引",
#     filename="dogeusdt_rsi_price_dual_axis.png",
#     y1_color='tab:blue',
#     y2_color='tab:red',
#     countS2=[10, 25, 40],  # 做空点索引
#     countS1=[15, 30, 45]   # 做多点索引
# )

def generate_dual_axis_plot(
    y1_values: pd.Series,         # 左Y轴数值 (例如 RSI)
    y2_values: pd.Series,         # 右Y轴数值 (例如 价格)
    title: str = "双轴趋势图",
    y1_label: str = "左Y轴数值",
    y2_label: str = "右Y轴数值",
    x_label: str = "数据点索引",
    filename: str = "dual_axis_plot.png",
    y1_color: str = 'tab:blue',   # 左Y轴数据的颜色
    y2_color: str = 'tab:red'    # 右Y轴数据的颜色
):
    """
    根据提供的两组Y轴数值自动生成并保存一个带有双Y轴的折线图。

    Args:
        y1_values (pd.Series): 用于左Y轴的数值，通常是指标（如RSI）。
        y2_values (pd.Series): 用于右Y轴的数值，通常是价格。
        title (str): 图表的标题。
        y1_label (str): 左Y轴的标签。
        y2_label (str): 右Y轴的标签。
        x_label (str): X轴的标签。
        filename (str): 保存图表的文件名。
        y1_color (str): 左Y轴数据线的颜色。
        y2_color (str): 右Y轴数据线的颜色。
    """
    # 输入数据类型和非空检查
    if not isinstance(y1_values, pd.Series) or not isinstance(y2_values, pd.Series):
        print("错误: y1_values 和 y2_values 必须是 Pandas Series。")
        return
    
    if len(y1_values) == 0 or len(y2_values) == 0:
        print("错误: y1_values 或 y2_values 不能为空，它们不包含任何数据点。")
        return

    # 确保两个Series的长度相同，否则绘图会出问题
    if len(y1_values) != len(y2_values):
        print("警告: y1_values 和 y2_values 长度不一致。图表可能无法正确对齐。")
        # 我们可以选择截断或填充NaN，这里为了简单起见，仍然尝试绘制，但会发出警告
        min_len = min(len(y1_values), len(y2_values))
        y1_values = y1_values.iloc[:min_len]
        y2_values = y2_values.iloc[:min_len]


    x_values = range(len(y1_values)) # X轴通常是数据的索引

    fig, ax1 = plt.subplots(figsize=(12, 7)) # 创建图表和第一个Y轴

    # 绘制左Y轴数据 (例如 RSI)
    ax1.set_xlabel(x_label)
    ax1.set_ylabel(y1_label, color=y1_color)
    ax1.plot(x_values, y1_values, color=y1_color, label=y1_label)
    ax1.tick_params(axis='y', labelcolor=y1_color)
    ax1.grid(True, linestyle='--', alpha=0.6) # 左轴显示网格

    # 创建第二个Y轴 (右Y轴)，它与ax1共享X轴
    ax2 = ax1.twinx()
    # 绘制右Y轴数据 (例如 价格)
    ax2.set_ylabel(y2_label, color=y2_color)
    ax2.plot(x_values, y2_values, color=y2_color, label=y2_label)
    ax2.tick_params(axis='y', labelcolor=y2_color)

    # 设置图表标题
    plt.title(title)

    # 显示图例 (如果需要)
    # 可以将两个轴的图例合并
    # lines = ax1.get_lines() + ax2.get_lines()
    # labels = [l.get_label() for l in lines]
    # ax1.legend(lines, labels, loc='upper left')

    plt.tight_layout() # 自动调整布局，防止标签重叠

    # 保存图表
    try:
        plt.savefig(filename)
        print(f"双轴图表已成功保存为: {filename}")
    except Exception as e:
        print(f"保存图表时发生错误: {e}")
    finally:
        plt.close(fig) # 关闭图表，释放内存

def smooth_rsi_with_sma(rsi_series: pd.Series, length: int = 10, column_name: str = 'RSI_SMA') -> pd.Series:
    """
    使用简单移动平均 (SMA) 平滑 RSI 数值。

    Args:
        rsi_series (pd.Series): 包含原始 RSI 数值的 Pandas Series。
        length (int): SMA 的计算周期（窗口大小），默认为 10。
        column_name (str): 平滑后数据的新列名，默认为 'RSI_SMA'。

    Returns:
        pd.Series: 包含平滑后 RSI 数值的 Pandas Series。
    """
    if not isinstance(rsi_series, pd.Series):
        print("错误: rsi_series 必须是 Pandas Series。")
        return pd.Series()
    
    if length <= 0:
        print("错误: SMA 长度必须大于 0。")
        return rsi_series.copy()

    # 使用 pandas_ta 的 sma 函数
    # data: 要平滑的 Series
    # length: 窗口大小
    smoothed_rsi = ta.sma(rsi_series, length=length)
    smoothed_rsi.name = column_name # 设置Series的名称以便后续使用

    # print(f"RSI 数据已使用 SMA (周期: {length}) 进行平滑。")
    return smoothed_rsi

def smooth_rsi_with_ema(rsi_series: pd.Series, length: int = 10, column_name: str = 'RSI_EMA') -> pd.Series:
    """
    使用指数移动平均 (EMA) 平滑 RSI 数值。

    Args:
        rsi_series (pd.Series): 包含原始 RSI 数值的 Pandas Series。
        length (int): EMA 的计算周期，默认为 10。
        column_name (str): 平滑后数据的新列名，默认为 'RSI_EMA'。

    Returns:
        pd.Series: 包含平滑后 RSI 数值的 Pandas Series。
    """
    if not isinstance(rsi_series, pd.Series):
        print("错误: rsi_series 必须是 Pandas Series。")
        return pd.Series()

    if length <= 0:
        print("错误: EMA 长度必须大于 0。")
        return rsi_series.copy()
        
    # 使用 pandas_ta 的 ema 函数
    smoothed_rsi = ta.ema(rsi_series, length=length)
    smoothed_rsi.name = column_name # 设置Series的名称以便后续使用

    # print(f"RSI 数据已使用 EMA (周期: {length}) 进行平滑。")
    return smoothed_rsi

def smooth_rsi_with_gaussian(rsi_series: pd.Series, sigma: float = 2.0, column_name: str = 'RSI_Gaussian') -> pd.Series:
    """
    使用高斯平滑处理 RSI 数值。

    Args:
        rsi_series (pd.Series): 包含原始 RSI 数值的 Pandas Series。
        sigma (float): 高斯核的标准差，控制平滑程度。值越大，平滑效果越明显，默认为 2.0。
                       通常，sigma 值应该与数据点的间隔相匹配。
        column_name (str): 平滑后数据的新列名，默认为 'RSI_Gaussian'。

    Returns:
        pd.Series: 包含平滑后 RSI 数值的 Pandas Series。
    """
    if not isinstance(rsi_series, pd.Series):
        print("错误: rsi_series 必须是 Pandas Series。")
        return pd.Series()

    if sigma <= 0:
        print("错误: sigma 必须大于 0。")
        return rsi_series.copy()

    # 高斯平滑在处理 NaN 值时可能需要特别注意
    # 最佳实践是先填充或删除 NaN，这里我们直接处理，
    # scipy.ndimage.gaussian_filter1d 会将 NaN 视为0或跳过，具体行为取决于版本和实现
    # 为了安全，我们可以在平滑前将NaN填充为平均值或0，平滑后再恢复NaN（如果需要）
    # 但对于RSI这种开头有NaN的情况，通常直接处理即可，结果NaN会保留
    
    # 将 Series 转换为 NumPy 数组进行处理，因为 gaussian_filter1d 接受 NumPy 数组
    # 对于 NaN 值，gaussian_filter1d 默认会将其视为 0 进行卷积，
    # 或者如果使用 mode='constant', cval=0。
    # 更好的做法是，如果原始数据中存在 NaN，平滑后对应位置也应为 NaN。
    # 我们可以通过 mask 的方式处理。

    # 创建一个副本，确保原始 Series 不被修改
    smoothed_values = rsi_series.copy()
    
    # 识别 NaN 值的位置
    nan_mask = smoothed_values.isna()
    
    # 临时填充 NaN 值（例如用0或平均值，这里用0，因为它不会参与有效平滑的计算）
    # 或者更简单，直接对非NaN部分进行处理
    
    # 仅对非 NaN 的值进行高斯平滑
    # 注意：gaussian_filter1d 会对整个数组进行操作，边缘效应需注意
    smoothed_array = gaussian_filter1d(smoothed_values.fillna(0).values, sigma=sigma)
    
    # 将结果放回 Series
    smoothed_rsi = pd.Series(smoothed_array, index=rsi_series.index)
    
    # 将平滑前是 NaN 的位置，平滑后也设为 NaN (通常高斯平滑不会产生新的NaN)
    smoothed_rsi[nan_mask] = pd.NA # 或者 np.nan

    smoothed_rsi.name = column_name # 设置Series的名称

    # print(f"RSI 数据已使用高斯平滑 (Sigma: {sigma}) 进行平滑。")
    return smoothed_rsi


# --- 新函数：生成包含4个子图的组合图表 ---
# --- 新函数：生成包含4个子图的组合图表 ---
def generate_multi_rsi_smoothing_plot(
    kline_df: pd.DataFrame,
    rsi_length: int,
    sma_length: int = 5,
    ema_length: int = 5,
    gaussian_sigma: float = 2.0,
    title_prefix: str = "DOGEUSDT 15m",
    x_label: str = "K线索引", # <-- 确保这里有默认值或在调用时传入
    filename: str = "multi_rsi_smoothing_plot.png"
):
    # ... (前面的数据验证和计算平滑RSI的代码保持不变) ...

    original_rsi = kline_df['RSI']
    close_prices = kline_df['close']
    x_values = range(len(kline_df))

    sma_rsi = smooth_rsi_with_sma(original_rsi, length=sma_length)
    ema_rsi = smooth_rsi_with_ema(original_rsi, length=ema_length)
    gaussian_rsi = smooth_rsi_with_gaussian(original_rsi, sigma=gaussian_sigma)

    fig, axes = plt.subplots(nrows=4, ncols=1, figsize=(14, 20), sharex=True)

    # --- 子图 1: 原始RSI与价格双轴图 ---
    ax1 = axes[0]
    ax1_twin = ax1.twinx()

    ax1.set_ylabel(f"RSI (周期: {rsi_length})", color='tab:blue')
    # 明确为每条线添加label，这样get_lines()才能正确获取
    line1, = ax1.plot(x_values, original_rsi, color='tab:blue', label=f'RSI ({rsi_length})')
    ax1.tick_params(axis='y', labelcolor='tab:blue')
    ax1.set_ylim(0, 100) # RSI 通常在0-100之间

    ax1_twin.set_ylabel("价格", color='tab:red')
    # 明确为每条线添加label
    line2, = ax1_twin.plot(x_values, close_prices, color='tab:red', label='价格')
    ax1_twin.tick_params(axis='y', labelcolor='tab:red')

    ax1.set_title(f"{title_prefix} - 原始RSI与价格")
    ax1.grid(True, linestyle='--', alpha=0.6)
    
    # **修正图例合并方式**
    # 将 Line2D 对象和它们的标签手动传入 legend
    ax1.legend([line1, line2], [line1.get_label(), line2.get_label()], loc='upper left')


    # --- 子图 2: SMA平滑RSI图 ---
    ax2 = axes[1]
    ax2.plot(x_values, sma_rsi, color='green', label=f'RSI SMA ({sma_length})')
    ax2.set_ylabel("RSI (SMA)", color='green')
    ax2.tick_params(axis='y', labelcolor='green')
    ax2.set_title(f"{title_prefix} - RSI SMA (周期: {sma_length})")
    ax2.grid(True, linestyle='--', alpha=0.6)
    ax2.set_ylim(0, 100)
    ax2.legend(loc='upper left') # 这个子图直接调用legend即可

    # --- 子图 3: EMA平滑RSI图 ---
    ax3 = axes[2]
    ax3.plot(x_values, ema_rsi, color='purple', label=f'RSI EMA ({ema_length})')
    ax3.set_ylabel("RSI (EMA)", color='purple')
    ax3.tick_params(axis='y', labelcolor='purple')
    ax3.set_title(f"{title_prefix} - RSI EMA (周期: {ema_length})")
    ax3.grid(True, linestyle='--', alpha=0.6)
    ax3.set_ylim(0, 100)
    ax3.legend(loc='upper left')

    # --- 子图 4: 高斯平滑RSI图 ---
    ax4 = axes[3]
    ax4.plot(x_values, gaussian_rsi, color='darkorange', label=f'RSI Gaussian (Sigma: {gaussian_sigma})')
    ax4.set_ylabel("RSI (高斯平滑)", color='darkorange')
    ax4.tick_params(axis='y', labelcolor='darkorange')
    ax4.set_title(f"{title_prefix} - RSI 高斯平滑 (Sigma: {gaussian_sigma})")
    ax4.grid(True, linestyle='--', alpha=0.6)
    ax4.set_ylim(0, 100)
    ax4.legend(loc='upper left')

    ax4.set_xlabel(x_label) # 只有最下面的子图需要X轴标签

    plt.tight_layout(rect=[0, 0.03, 1, 0.98])
    fig.suptitle(f"{title_prefix} - RSI 及平滑对比", fontsize=16, y=0.99)

    try:
        plt.savefig(filename)
        print(f"组合图表已成功保存为: {filename}")
    except Exception as e:
        print(f"保存组合图表时发生错误: {e}")
    finally:
        plt.close(fig)
def analyze_numerical_series(data_series):
    data = pd.Series(data_series).dropna()
    if data.empty:
        return {}

    rounded = data.round().astype(int)
    rounded_unique_sorted = np.sort(rounded.unique())
    top_5_max = rounded_unique_sorted[::-1][:5]
    bottom_5_min = rounded_unique_sorted[:5]
    top_5_max_counts = [int((rounded == v).sum()) for v in top_5_max]
    bottom_5_min_counts = [int((rounded == v).sum()) for v in bottom_5_min]

    mean = float(data.mean())
    variance = float(data.var(ddof=0))
    sum_squared_diff = float(((data - mean) ** 2).sum())
    mse = float(((data - mean) ** 2).mean())
    std_dev = float(data.std(ddof=0))

    # 用 spstats.mode
    mode_vals = spstats.mode(rounded, keepdims=True)
    mode = [int(x) for x in np.atleast_1d(mode_vals.mode)]

    conf_1sigma = (mean - std_dev, mean + std_dev)
    conf_2sigma = (mean - 2*std_dev, mean + 2*std_dev)
    conf_3sigma = (mean - 3*std_dev, mean + 3*std_dev)

    n_1sigma = int(((data >= conf_1sigma[0]) & (data <= conf_1sigma[1])).sum())
    n_2sigma = int(((data >= conf_2sigma[0]) & (data <= conf_2sigma[1])).sum())
    n_3sigma = int(((data >= conf_3sigma[0]) & (data <= conf_3sigma[1])).sum())

    return {
        "top_5_max": top_5_max.tolist(),
        "top_5_max_counts": top_5_max_counts,
        "bottom_5_min": bottom_5_min.tolist(),
        "bottom_5_min_counts": bottom_5_min_counts,
        "mean": mean,
        "mode": mode,
        "variance": variance,
        "sum_squared_diff": sum_squared_diff,
        "mse": mse,
        "std_dev": std_dev,
        "conf_interval_1sigma": conf_1sigma,
        "n_1sigma": n_1sigma,
        "conf_interval_2sigma": conf_2sigma,
        "n_2sigma": n_2sigma,
        "conf_interval_3sigma": conf_3sigma,
        "n_3sigma": n_3sigma,
    }

def fast_rsi_numpy(prices, period=14):
    """更快的RSI计算"""
    delta = np.diff(prices)
    gain = np.where(delta > 0, delta, 0)
    loss = np.where(delta < 0, -delta, 0)
    
    # 简单移动平均
    if len(gain) >= period:
        avg_gain = pd.Series(gain).rolling(period).mean()
        avg_loss = pd.Series(loss).rolling(period).mean()
        rs = avg_gain / (avg_loss + 1e-8)
        rsi = 100 - (100 / (1 + rs))
        # 补齐长度
        result = np.full(len(prices), np.nan)
        result[period:] = rsi.dropna().values
        return result
    else:
        return np.full(len(prices), np.nan)
    

def fast_rsi_calculation(prices, period=14):
    """
    优化的RSI计算 - 使用numpy向量化操作
    比pandas_ta.rsi()更快，但保持相同的结果
    """
    prices = np.array(prices, dtype=np.float64)
    deltas = np.diff(prices)
    
    gains = np.where(deltas > 0, deltas, 0)
    losses = np.where(deltas < 0, -deltas, 0)
    
    # 使用指数移动平均而不是简单移动平均（更快）
    alpha = 1.0 / period
    
    # 初始化
    avg_gains = np.zeros_like(gains)
    avg_losses = np.zeros_like(losses)
    
    if len(gains) > 0:
        avg_gains[0] = gains[0]
        avg_losses[0] = losses[0]
        
        # 向量化的指数移动平均
        for i in range(1, len(gains)):
            avg_gains[i] = alpha * gains[i] + (1 - alpha) * avg_gains[i-1]
            avg_losses[i] = alpha * losses[i] + (1 - alpha) * avg_losses[i-1]
    
    # 避免除零
    rs = avg_gains / (avg_losses + 1e-10)
    rsi = 100 - (100 / (1 + rs))
    
    # 补齐长度以匹配原始价格数组
    result = np.full(len(prices), np.nan)
    result[1:] = rsi
    
    return result

def fast_gaussian_smooth(data, sigma):
    """
    优化的高斯平滑 - 使用更高效的实现
    """
    if len(data) < 3:
        return data
    
    # 使用较小的核大小以提高性能
    kernel_size = min(int(4 * sigma + 1), len(data))
    if kernel_size % 2 == 0:
        kernel_size += 1
    
    # 生成高斯核
    x = np.arange(kernel_size) - kernel_size // 2
    kernel = np.exp(-0.5 * (x / sigma) ** 2)
    kernel /= kernel.sum()
    
    # 处理边界
    half_kernel = kernel_size // 2
    padded = np.pad(data, half_kernel, mode='edge')
    
    # 卷积
    smoothed = np.convolve(padded, kernel, mode='valid')
    
    return smoothed
# -- calculate_trading_metrics_with_signals 函数 (未修改，但现在它会正确接收金额结果) ---

# --- calculate_trading_metrics_with_signals 函数 (已修改，以适应字符串 '交易结果') ---

# --- 核心辅助函数：执行单次模式的指标计算 ---
def _calculate_single_mode_metrics(
    df: pd.DataFrame, 
    initial_capital: float,
    trading_mode: str, # 'fixed_amount' 或 'percentage_of_capital'
    trade_amount_or_percentage: float # 每次投入的金额或百分比
) -> dict:
    """
    辅助函数：计算单一交易模式下的交易指标。
    """
    equity_curve = []
    current_capital = initial_capital
    position_size = 0  # 记录当前持仓量
    entry_price = np.nan  # 记录当前仓位的开仓价格
    trade_value_at_entry = np.nan # 记录开仓时实际投入的资金量
    
    trade_profits_list = [] # 存储每笔完成交易的盈亏金额

    for i, row in df.iterrows():
        signal = row['nowSingle']
        trade_result_str = row['交易结果'] # 字符串形式的交易结果
        pct_change = row['达成百分比'] # 百分比变化
        current_price = row['close']
        buy_price_for_row = row['buyPrice'] # 当前行的买入价格

        # 计算当前浮动盈亏 (用于构建资金曲线)
        floating_pnl = 0
        if position_size != 0 and not np.isnan(entry_price):
            if position_size > 0: # 多头仓位
                floating_pnl = (current_price - entry_price) * position_size
            else: # 空头仓位
                floating_pnl = (entry_price - current_price) * abs(position_size)
        
        # 将当前资金（包含浮动盈亏）添加到资金曲线
        equity_curve.append(current_capital + floating_pnl)

        # --- 处理交易完成（平仓）信号 ---
        if trade_result_str != '': # 如果有非空的 '交易结果' 字符串，表示一笔交易完成
            if pd.notna(pct_change) and pd.notna(trade_value_at_entry) and trade_value_at_entry != 0:
                # 盈亏金额 = 开仓时实际投入的资金 * (达成百分比 / 100)
                trade_profit_amount = trade_value_at_entry * (pct_change / 100)
                
                current_capital += trade_profit_amount
                trade_profits_list.append(trade_profit_amount) # 记录已完成的交易盈亏金额

                position_size = 0  # 平仓后无持仓
                entry_price = np.nan # 重置入场价格
                trade_value_at_entry = np.nan # 重置开仓时投入的资金
            else:
                # 警告：无法计算盈亏金额
                # 即使无法计算盈亏，也应该清仓位，避免错误状态
                position_size = 0
                entry_price = np.nan
                trade_value_at_entry = np.nan

        # --- 处理开仓信号 ---
        # 只有在没有持仓时才接受新的开仓信号
        if signal in [1, -1] and position_size == 0:
            current_entry_price = row['buyPrice'] if pd.notna(row['buyPrice']) else current_price
            
            if current_entry_price == 0 or np.isnan(current_entry_price):
                # print(f"警告：在索引 {i} 处遇到无效（零或NaN）买入价格 '{row['buyPrice']}'，无法开仓。")
                continue # 跳过此次开仓
            
            # 根据交易模式确定本次投入的资金量
            actual_trade_amount = 0
            if trading_mode == 'fixed_amount':
                actual_trade_amount = trade_amount_or_percentage
            elif trading_mode == 'percentage_of_capital':
                actual_trade_amount = current_capital * (trade_amount_or_percentage / 100)
            
            # 资金不足检查
            if actual_trade_amount > current_capital:
                # print(f"警告：在索引 {i} 处资金不足以投入 {actual_trade_amount:.2f}，当前资金 {current_capital:.2f}。跳过此次开仓。")
                continue # 资金不足，跳过此次开仓
            if actual_trade_amount <= 0: # 避免投入0或负数
                continue

            # 记录本次交易的实际投入资金，用于后续计算平仓盈亏
            trade_value_at_entry = actual_trade_amount 
            
            # 计算持仓量
            if signal == 1: # 做多
                position_size = actual_trade_amount / current_entry_price 
            else: # 做空
                position_size = - (actual_trade_amount / current_entry_price) 
            
            entry_price = current_entry_price # 记录开仓价格

    # ------------------- 指标计算 -------------------
    final_capital = current_capital 

    # 1. 胜率和交易次数
    total_trades = len(trade_profits_list)
    win_trades = sum(1 for p in trade_profits_list if p > 0)
    win_rate = (win_trades / total_trades) * 100 if total_trades > 0 else 0.0

    # 2. 夏普率
    equity_series = pd.Series(equity_curve)
    if len(equity_series) < 2: 
        sharpe_ratio = np.NaN
    else:
        returns = equity_series.pct_change().dropna()
        annualization_factor = np.sqrt(365) 
        sharpe_ratio = (returns.mean() / returns.std()) * annualization_factor if returns.std() != 0 else np.NaN

    # 3. 最大回撤
    if len(equity_series) < 1:
        max_drawdown = 0.0
    else:
        peak = equity_series.expanding(min_periods=1).max()
        drawdown = (equity_series - peak) / peak
        max_drawdown = drawdown.min() * 100

    # 总收益
    total_return_percentage = ((current_capital - initial_capital) / initial_capital) * 100

    metrics = {
        '胜率': win_rate,
        '交易次数': total_trades,
        '夏普率': sharpe_ratio,
        '最大回撤': max_drawdown,
        '总收益': total_return_percentage,
        '最终资金': current_capital 
    }
    return metrics


# --- 最终的 calculate_trading_metrics_with_signals 函数，同时返回两种模式结果 ---
def calculate_trading_metrics_with_signals(
    kline_df: pd.DataFrame, 
    initial_capital: float = 10000,
    fixed_trade_amount: float = 100, # 定投金额
    percentage_of_capital: float = 25 # 资金比例 (25%)
) -> dict:
   
    all_results = {}

    # --- 模式一：定投模式 ---
    # print("\n--- 正在计算定投模式 ($100) 的指标 ---")
    fixed_amount_metrics = _calculate_single_mode_metrics(
        df=kline_df.copy(), # 使用副本
        initial_capital=initial_capital,
        trading_mode='fixed_amount',
        trade_amount_or_percentage=fixed_trade_amount
    )
    all_results['fixed_amount_metrics'] = fixed_amount_metrics
    # print("定投模式计算完成。")


    # --- 模式二：资金比例模式 ---
    # print("\n--- 正在计算资金比例模式 (总资金的25%) 的指标 ---")
    percentage_capital_metrics = _calculate_single_mode_metrics(
        df=kline_df.copy(), # 使用副本
        initial_capital=initial_capital,
        trading_mode='percentage_of_capital',
        trade_amount_or_percentage=percentage_of_capital
    )
    all_results['percentage_of_capital_metrics'] = percentage_capital_metrics
    # print("资金比例模式计算完成。")

    return all_results

def plot_three_lists(list1, list2, list3):
    list1=list1.tolist()
    list2=list2.tolist()
    list3=list3.tolist()
    # 找到最大长度
    max_len = max(len(list1), len(list2), len(list3))
    
    # 用 nan 填充短列表
    def pad_list(lst):
        return lst + [np.nan] * (max_len - len(lst))
    
    list1 = pad_list(list1)
    list2 = pad_list(list2)
    list3 = pad_list(list3)
    
    # 转为numpy数组方便处理
    arr1 = np.array(list1)
    arr2 = np.array(list2)
    arr3 = np.array(list3)
    
    # 创建x轴索引
    x = np.arange(max_len)
    
    # 绘制
    plt.figure(figsize=(12, 6))
    
    # 只绘制非0且非nan的点
    def plot_filtered(arr, label, color):
        mask = (~np.isnan(arr)) & (arr != 0)
        plt.plot(x[mask], arr[mask], label=label, color=color, marker='o')
    
    plot_filtered(arr1, 'List 1', 'blue')
    plot_filtered(arr2, 'List 2', 'green')
    plot_filtered(arr3, 'List 3', 'red')
    
    plt.legend()
    plt.xlabel('Index')
    plt.ylabel('Value')
    plt.title('Plot of Three Lists with handling nan and zeros')
    plt.show()

def plot_with_dynamic_scale(list1, list2, list3):
    
    list1=list1.tolist()
    list2=list2.tolist()
    list3=list3.tolist()
    # 先找出第二个列表的最大值，忽略nan和0
    list2_array = np.array(list2)
    valid_mask = (~np.isnan(list2_array)) & (list2_array != 0)
    max_list2_value = np.max(list2_array[valid_mask])

    # 找出第一个列表的最大有效值
    list1_array = np.array(list1)
    valid_mask1 = (~np.isnan(list1_array)) & (list1_array != 0)
    max_list1_value = np.max(list1_array[valid_mask1])

    # 计算放大倍数，使第一个列表的最大值接近第二个列表的最大值
    if max_list1_value == 0:
        scale_factor = 1  # 避免除以0
    else:
        scale_factor = max_list2_value / max_list1_value

    # 填充短列表
    def pad_list(lst):
        return lst + [np.nan] * (max_len - len(lst))
    max_len = max(len(list1), len(list2), len(list3))
    list1 = pad_list(list1)
    list2 = pad_list(list2)
    list3 = pad_list(list3)

    arr1 = np.array(list1) * scale_factor  # 动态缩放
    arr2 = np.array(list2)
    arr3 = np.array(list3)

    x = np.arange(max_len)

    plt.figure(figsize=(12,6))
    def plot_filtered(arr, label, color):
        mask = (~np.isnan(arr)) & (arr != 0)
        plt.plot(x[mask], arr[mask], label=label, color=color, marker='o')
    
    plot_filtered(arr1, 'List 1 (scaled)', 'blue')
    plot_filtered(arr2, 'List 2', 'green')
    plot_filtered(arr3, 'List 3', 'red')

    plt.legend()
    plt.xlabel('Index')
    plt.ylabel('Value')
    plt.title('List 1 scaled to match List 2 max value')
    plt.show()

# 在X轴上指定特定点进行标记（比如用不同颜色、符号标记）

def plot_with_markers(x_list, y_list, mark_points=None):
    """
    绘制折线图并在指定点进行标记。
    
    参数：
    x_list (list): X轴坐标列表
    y_list (list): Y轴对应值列表
    mark_points (list): 需要标记的点的X轴位置列表（可选）
    """
    plt.plot(x_list, y_list, label='Data Line', marker='o')  # 绘制折线图
    
    if mark_points:
        # 获取对应的y值
        mark_y = []
        for mp in mark_points:
            # 找到x等于mp的点的索引
            if mp in x_list:
                idx = x_list.index(mp)
                mark_y.append(y_list[idx])
            else:
                # 如果点不在x_list中，可以忽略或者处理
                print(f"点 {mp} 不在X轴列表中")
        
        # 标记点
        plt.scatter(mark_points, mark_y, color='red', marker='x', s=100, label='Marked Points')
    
    plt.xlabel('X轴')
    plt.ylabel('Y值')
    plt.legend()
    plt.title('折线图与标记点')
    plt.show()

def plot_with_markersV1(x, y, markers1=None, markers2=None, filename=None):
    """
    绘制折线图，并在两个不同的点列表上进行标记。
    """
    plt.plot(x, y, label='Data Line', color='black', marker='o', markersize=1)

    # 标记第一个点集
    if markers1 is not None:
        y_marks1 = []
        for m in markers1:
            if m in x:
                idx = x.index(m)
                y_marks1.append(y[idx])
            else:
                # 如需处理不存在的点，可以选择忽略或提示
                print(f"点 {m} 不在X轴范围内")
        plt.scatter(markers1, y_marks1, color='red', marker='X', s=100, label='Top')

    # 标记第二个点集
    if markers2 is not None:
        y_marks2 = []
        for m in markers2:
            if m in x:
                idx = x.index(m)
                y_marks2.append(y[idx])
            else:
                print(f"点 {m} 不在X轴范围内")
        plt.scatter(markers2, y_marks2, color='blue', marker='^', s=100, label='Down')

    plt.xlabel('X轴')
    plt.ylabel('Y值')
    plt.legend()
    plt.title('升级版标记图')
    
    plt.grid(True, linestyle=':', alpha=0.6) # 添加网格线，使图表更易读
    
    # --- 新增功能：保存图表 ---
    # 检查是否提供了文件名
    if filename:
        try:
            # 在显示图表前保存它，确保保存的是完整的图
            # bbox_inches='tight' 会自动裁剪空白边缘，使图片更紧凑
            plt.savefig(filename, bbox_inches='tight', dpi=150)
            print(f"图表已成功保存到: {filename}")
        except Exception as e:
            print(f"保存文件时出错: {e}")
    plt.show()

##信号处理
def get_start_points(data_list: list[int]) -> list[int]:
    # 处理边界情况
    if not data_list:
        return []
    result = []
    i = 0
    n = len(data_list)
    while i < n:
        # j 指向当前连续序列的末尾
        j = i
        while j + 1 < n and data_list[j+1] == data_list[j] + 1:
            j += 1
        if i == j:
            # 这是一个单个的、不连续的数字，保持不变
            result.append(data_list[i])
        else:
            # 这是一个连续序列，取最后一个元素加1
            result.append(data_list[j] + 1)
        i = j + 1
        
    return result



def generate_dual_axis_plot_V3(
    y1_values: pd.Series,
    y2_values: pd.Series,
    hline_y: float,
    y1_label: str = "左Y轴",
    y2_label: str = "右Y轴",
    hline_label: str = "水平参考线",
    title: str = "双轴图",
    filename: str = "dual_axis_simple.png"
):
    """
    一个简洁的双轴图生成函数。
    - y1_values 绘制在左轴。
    - y2_values 绘制在右轴。
    - 根据 pandas 索引自动对齐。
    - hline_y 水平线根据 右侧Y轴(y2) 的刻度绘制。
    """
    # 1. 创建图表和左侧Y轴 (ax1)
    fig, ax1 = plt.subplots(figsize=(15, 8))

    # 2. 在左轴上绘制 y1
    ax1.set_ylabel(y1_label, color='blue', fontsize=12)
    ax1.plot(y1_values, color='blue', label=y1_label)
    ax1.tick_params(axis='y', labelcolor='blue')
    ax1.grid(True, linestyle='--', alpha=0.6)

    # 3. 创建共享X轴的右侧Y轴 (ax2)
    ax2 = ax1.twinx()

    # 4. 在右轴上绘制 y2
    ax2.set_ylabel(y2_label, color='red', fontsize=12)
    ax2.plot(y2_values, color='red', label=y2_label)
    ax2.tick_params(axis='y', labelcolor='red')

    # 5. 【关键】在右轴 (ax2) 上绘制水平线
    ax2.axhline(y=hline_y, color='green', linestyle='--', label=hline_label)

    # 6. 添加图例和标题
    # 合并图例
    lines, labels = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax2.legend(lines + lines2, labels + labels2, loc='upper left')
    
    plt.title(title, fontsize=16)
    fig.autofmt_xdate() # 自动优化日期显示
    plt.tight_layout()

    # 7. 保存图片
    try:
        plt.savefig(filename)
        print(f"图表已成功保存至: {filename}")
    except Exception as e:
        print(f"保存图表失败: {e}")
    finally:
        plt.close(fig)
        

def MACD_calculation(close_prices, fast_period=12, slow_period=26, signal_period=9):
    """
    计算MACD指标及其金叉死叉信号
    
    Parameters:
    -----------
    close_prices : pd.Series or list
        收盘价数据
    fast_period : int
        快速EMA周期，默认12
    slow_period : int
        慢速EMA周期，默认26
    signal_period : int
        信号线EMA周期，默认9
    
    Returns:
    --------
    dict : 包含MACD相关指标和信号的字典
        - 'macd_line': MACD线 (DIF)
        - 'signal_line': 信号线 (DEA)
        - 'histogram': MACD柱状图 (MACD - Signal)
        - 'golden_cross': 金叉信号 (True/False)
        - 'death_cross': 死叉信号 (True/False)
        - 'cross_points': 交叉点位置
    """
    
    # 确保输入是pandas Series
    if not isinstance(close_prices, pd.Series):
        close_prices = pd.Series(close_prices)
    
    # 计算快速和慢速EMA
    ema_fast = close_prices.ewm(span=fast_period).mean()
    ema_slow = close_prices.ewm(span=slow_period).mean()
    
    # 计算MACD线 (DIF)
    macd_line = ema_fast - ema_slow
    
    # 计算信号线 (DEA) - MACD线的EMA
    signal_line = macd_line.ewm(span=signal_period).mean()
    
    # 计算MACD柱状图
    histogram = macd_line - signal_line
    
    # 检测金叉和死叉
    golden_cross = pd.Series(False, index=close_prices.index)
    death_cross = pd.Series(False, index=close_prices.index)
    cross_points = []
    
    for i in range(1, len(macd_line)):
        # 金叉：MACD线从下方穿越信号线
        if (macd_line.iloc[i] > signal_line.iloc[i] and 
            macd_line.iloc[i-1] <= signal_line.iloc[i-1]):
            golden_cross.iloc[i] = True
            cross_points.append({
                'index': i,
                'type': 'golden_cross',
                'macd_value': macd_line.iloc[i],
                'signal_value': signal_line.iloc[i],
                'price': close_prices.iloc[i]
            })
        
        # 死叉：MACD线从上方穿越信号线
        elif (macd_line.iloc[i] < signal_line.iloc[i] and 
              macd_line.iloc[i-1] >= signal_line.iloc[i-1]):
            death_cross.iloc[i] = True
            cross_points.append({
                'index': i,
                'type': 'death_cross',
                'macd_value': macd_line.iloc[i],
                'signal_value': signal_line.iloc[i],
                'price': close_prices.iloc[i]
            })
    
    return {
        'macd_line': macd_line,
        'signal_line': signal_line,
        'histogram': histogram,
        'golden_cross': golden_cross,
        'death_cross': death_cross,
        'cross_points': cross_points
    }
    
    
def fast_macd_cross_check(close_prices, fast_period=12, slow_period=26, signal_period=9):
    """
    快速检测最后一个位置是否为MACD金叉或死叉
    
    Parameters:
    -----------
    close_prices : pd.Series or list
        收盘价数据
    fast_period : int
        快速EMA周期，默认12
    slow_period : int
        慢速EMA周期，默认26
    signal_period : int
        信号线EMA周期，默认9
    
    Returns:
    --------
    dict : 包含最后位置交叉信息的字典
        - 'is_golden_cross': 是否为金叉
        - 'is_death_cross': 是否为死叉
        - 'current_macd': 当前MACD值
        - 'current_signal': 当前信号线值
        - 'previous_macd': 前一个MACD值
        - 'previous_signal': 前一个信号线值
    """
    
    # 确保输入是pandas Series
    if not isinstance(close_prices, pd.Series):
        close_prices = pd.Series(close_prices)
    
    # 检查数据长度是否足够
    min_length = max(slow_period, fast_period) + signal_period
    if len(close_prices) < min_length:
        return {
            'is_golden_cross': False,
            'is_death_cross': False,
            'current_macd': None,
            'current_signal': None,
            'previous_macd': None,
            'previous_signal': None,
            'error': f'数据长度不足，需要至少{min_length}个数据点'
        }
    
    # 计算EMA
    ema_fast = close_prices.ewm(span=fast_period).mean()
    ema_slow = close_prices.ewm(span=slow_period).mean()
    
    # 计算MACD线
    macd_line = ema_fast - ema_slow
    
    # 计算信号线
    signal_line = macd_line.ewm(span=signal_period).mean()
    
    # 只获取最后两个值来判断交叉
    current_macd = macd_line.iloc[-1]
    current_signal = signal_line.iloc[-1]
    previous_macd = macd_line.iloc[-2]
    previous_signal = signal_line.iloc[-2]
    
    # 判断交叉类型
    is_golden_cross = (current_macd > current_signal and 
                      previous_macd <= previous_signal)
    
    is_death_cross = (current_macd < current_signal and 
                     previous_macd >= previous_signal)
    
    return {
        'is_golden_cross': is_golden_cross,
        'is_death_cross': is_death_cross,
        'current_macd': current_macd,
        'current_signal': current_signal,
        'previous_macd': previous_macd,
        'previous_signal': previous_signal,
        'cross_type': 'golden_cross' if is_golden_cross else ('death_cross' if is_death_cross else 'no_cross')
    }
    
def resample_kline(df: pd.DataFrame, rule: str) -> pd.DataFrame:
    """
    将K线数据从细粒度重采样到更粗的粒度 (例如从15分钟到30分钟)。

    参数:
    - df (pd.DataFrame): 原始K线数据。必须包含 'open', 'high', 'low', 'close', 'volume' 列，
                         并且其索引必须是 pandas 的 DatetimeIndex。
    - rule (str): pandas重采样规则字符串，例如 '30T' 代表30分钟, '4H' 代表4小时。

    返回:
    - pd.DataFrame: 重采样并聚合后的新周期K线数据。
    """
    if not isinstance(df.index, pd.DatetimeIndex):
        raise TypeError("输入DataFrame的索引必须是DatetimeIndex类型才能进行重采样。")

    # 定义标准的OHLCV聚合规则
    aggregation_rules = {
        'open': 'first',
        'high': 'max',
        'low': 'min',
        'close': 'last',
        'volume': 'sum'
    }

    # 执行重采样和聚合
    # label='right' 和 closed='right' 是为了确保K线的时间戳是其周期的结束时间，符合常规用法
    resampled_df = df.resample(rule, label='right', closed='right').agg(aggregation_rules)
    
    # 移除因没有交易活动而产生的空行
    resampled_df.dropna(subset=['close'], inplace=True)

    return resampled_df

def calculate_adx_v2(data: pd.DataFrame, window: int = 14) -> pd.DataFrame:
    """
    计算 ADX, +DI, 和 -DI 指标 (V2 版本)。
    
    此版本遵循关注点分离原则，仅返回一个包含指标结果的新DataFrame。
    返回的DataFrame将保留与输入数据相同的索引。

    参数:
    data (pd.DataFrame): 包含 'high', 'low', 'close' 列的 DataFrame。
    window (int): 计算周期，默认为 14。

    返回:
    pd.DataFrame: 一个新的DataFrame，包含 'adx', '+di', '-di' 三列和原始索引。
    """
    if not all(col in data.columns for col in ['high', 'low', 'close']):
        raise ValueError("输入的数据必须包含 'high', 'low', 'close' 列")

    df = data.copy()

    # --- 内部计算逻辑 (与之前版本完全相同) ---
    df['high_prev'] = df['high'].shift(1)
    df['low_prev'] = df['low'].shift(1)
    df['close_prev'] = df['close'].shift(1)

    df['tr'] = np.maximum(
        df['high'] - df['low'],
        np.maximum(abs(df['high'] - df['close_prev']), abs(df['low'] - df['close_prev']))
    )

    df['move_up'] = df['high'] - df['high_prev']
    df['move_down'] = df['low_prev'] - df['low']
    
    df['+dm'] = np.where((df['move_up'] > df['move_down']) & (df['move_up'] > 0), df['move_up'], 0)
    df['-dm'] = np.where((df['move_down'] > df['move_up']) & (df['move_down'] > 0), df['move_down'], 0)

    df['atr'] = df['tr'].ewm(alpha=1/window, adjust=False).mean()
    df['+dm_smooth'] = df['+dm'].ewm(alpha=1/window, adjust=False).mean()
    df['-dm_smooth'] = df['-dm'].ewm(alpha=1/window, adjust=False).mean()

    df['+di'] = (df['+dm_smooth'] / df['atr']) * 100
    df['-di'] = (df['-dm_smooth'] / df['atr']) * 100

    df['dx'] = (np.abs(df['+di'] - df['-di']) / (df['+di'] + df['-di'])) * 100
    df['adx'] = df['dx'].ewm(alpha=1/window, adjust=False).mean()
    
    # --- 返回值的改变 ---
    # 旧设计：返回修改后的原始DataFrame
    # return df 
    
    # 新设计：只返回包含结果的新DataFrame
    result_df = df[['adx', '+di', '-di']].copy()
    
    return result_df