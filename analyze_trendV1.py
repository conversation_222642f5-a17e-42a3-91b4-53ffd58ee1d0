#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
趋势回调识别系统
识别突破-回调-再突破 或 下跌-回调-再下跌模式
"""

import numpy as np
from typing import List, Tuple, Dict
import warnings
warnings.filterwarnings('ignore')


class TrendPullbackDetector:
    """趋势回调检测器"""
    
    def __init__(self, 
                 lookback_period: int = 20,          # 回看周期
                 rsi_threshold_bull: float = 50,     # 多头RSI阈值
                 rsi_threshold_bear: float = 50,     # 空头RSI阈值
                 price_retracement_pct: float = 0.382,  # 价格回撤比例(斐波那契)
                 min_trend_strength: float = 0.6,    # 最小趋势强度
                 smoothing_factor: float = 0.8):     # 高斯平滑因子
        
        self.lookback_period = lookback_period
        self.rsi_threshold_bull = rsi_threshold_bull
        self.rsi_threshold_bear = rsi_threshold_bear
        self.price_retracement_pct = price_retracement_pct
        self.min_trend_strength = min_trend_strength
        self.smoothing_factor = smoothing_factor
        
    def detect_pattern(self, 
                      prices: List[float], 
                      rsi_values: List[float], 
                      rsi_gaussian: List[float],
                      timeframe: str = "15m") -> Dict:
        """
        检测当前模式
        
        参数:
            prices: K线收盘价列表
            rsi_values: RSI值列表
            rsi_gaussian: RSI高斯平均值列表
            timeframe: 时间框架 (1m, 5m, 15m, 30m, 1h, 4h, 1d)
            
        返回:
            包含检测结果的字典
        """
        
        # 调整参数基于时间框架
        adjusted_params = self._adjust_params_by_timeframe(timeframe)
        
        # 转换为numpy数组
        prices = np.array(prices)
        rsi_values = np.array(rsi_values)
        rsi_gaussian = np.array(rsi_gaussian)
        
        # 计算价格特征
        price_features = self._calculate_price_features(prices, adjusted_params)
        
        # 计算RSI特征
        rsi_features = self._calculate_rsi_features(rsi_values, rsi_gaussian)
        
        # 识别模式
        pattern = self._identify_pattern(price_features, rsi_features, adjusted_params)
        
        # 计算置信度
        confidence = self._calculate_confidence(price_features, rsi_features, pattern)
        
        return {
            'pattern': pattern,
            'confidence': confidence,
            'price_features': price_features,
            'rsi_features': rsi_features,
            'timeframe': timeframe,
            'recommendation': self._get_recommendation(pattern, confidence)
        }
    
    def _adjust_params_by_timeframe(self, timeframe: str) -> Dict:
        """根据时间框架调整参数"""
        
        # 时间框架权重
        timeframe_weights = {
            '1m': {'noise_factor': 1.5, 'trend_discount': 0.7},
            '5m': {'noise_factor': 1.3, 'trend_discount': 0.8},
            '15m': {'noise_factor': 1.1, 'trend_discount': 0.9},
            '30m': {'noise_factor': 1.0, 'trend_discount': 0.95},
            '1h': {'noise_factor': 0.9, 'trend_discount': 1.0},
            '4h': {'noise_factor': 0.8, 'trend_discount': 1.05},
            '1d': {'noise_factor': 0.7, 'trend_discount': 1.1}
        }
        
        weights = timeframe_weights.get(timeframe, timeframe_weights['15m'])
        
        return {
            'lookback': int(self.lookback_period * weights['noise_factor']),
            'rsi_bull': self.rsi_threshold_bull,
            'rsi_bear': self.rsi_threshold_bear,
            'retracement': self.price_retracement_pct,
            'trend_strength': self.min_trend_strength * weights['trend_discount']
        }
    
    def _calculate_price_features(self, prices: np.ndarray, params: Dict) -> Dict:
        """计算价格特征"""
        
        lookback = min(params['lookback'], len(prices) - 1)
        
        # 最近的高点和低点
        recent_high = np.max(prices[-lookback:])
        recent_low = np.min(prices[-lookback:])
        current_price = prices[-1]
        
        # 计算趋势
        price_change = (current_price - prices[-lookback]) / prices[-lookback]
        
        # 识别回调
        if price_change > 0:  # 上升趋势
            # 从最近高点的回撤
            retracement_from_high = (recent_high - current_price) / (recent_high - recent_low) if recent_high != recent_low else 0
            is_pullback = 0 < retracement_from_high < params['retracement']
            trend_type = 'bullish'
        else:  # 下降趋势
            # 从最近低点的反弹
            rebound_from_low = (current_price - recent_low) / (recent_high - recent_low) if recent_high != recent_low else 0
            is_pullback = 0 < rebound_from_low < params['retracement']
            trend_type = 'bearish'
        
        # 计算支撑阻力
        support_levels = self._find_support_resistance(prices, 'support')
        resistance_levels = self._find_support_resistance(prices, 'resistance')
        
        return {
            'trend_type': trend_type,
            'price_change_pct': price_change * 100,
            'is_pullback': is_pullback,
            'recent_high': recent_high,
            'recent_low': recent_low,
            'current_price': current_price,
            'retracement_level': retracement_from_high if trend_type == 'bullish' else rebound_from_low,
            'support_levels': support_levels,
            'resistance_levels': resistance_levels
        }
    
    def _calculate_rsi_features(self, rsi_values: np.ndarray, rsi_gaussian: np.ndarray) -> Dict:
        """计算RSI特征"""
        
        current_rsi = rsi_values[-1]
        current_gaussian = rsi_gaussian[-1]
        
        # RSI趋势
        rsi_trend = np.polyfit(range(len(rsi_values[-10:])), rsi_values[-10:], 1)[0]
        gaussian_trend = np.polyfit(range(len(rsi_gaussian[-10:])), rsi_gaussian[-10:], 1)[0]
        
        # RSI位置
        rsi_position = 'overbought' if current_rsi > 70 else ('oversold' if current_rsi < 30 else 'neutral')
        
        # RSI与高斯均线的关系
        rsi_vs_gaussian = current_rsi - current_gaussian
        
        # 检测背离
        divergence = self._detect_divergence(rsi_values, rsi_gaussian)
        
        return {
            'current_rsi': current_rsi,
            'current_gaussian': current_gaussian,
            'rsi_trend': rsi_trend,
            'gaussian_trend': gaussian_trend,
            'rsi_position': rsi_position,
            'rsi_vs_gaussian': rsi_vs_gaussian,
            'divergence': divergence,
            'rsi_above_50': current_rsi > 50,
            'gaussian_above_50': current_gaussian > 50
        }
    
    def _find_support_resistance(self, prices: np.ndarray, level_type: str) -> List[float]:
        """寻找支撑/阻力位"""
        
        window = 5
        levels = []
        
        for i in range(window, len(prices) - window):
            if level_type == 'support':
                if all(prices[i] <= prices[i-j] for j in range(1, window+1)) and \
                   all(prices[i] <= prices[i+j] for j in range(1, window+1)):
                    levels.append(prices[i])
            else:  # resistance
                if all(prices[i] >= prices[i-j] for j in range(1, window+1)) and \
                   all(prices[i] >= prices[i+j] for j in range(1, window+1)):
                    levels.append(prices[i])
        
        # 去除相近的水平
        if levels:
            levels = sorted(levels)
            filtered_levels = [levels[0]]
            for level in levels[1:]:
                if abs(level - filtered_levels[-1]) / filtered_levels[-1] > 0.01:
                    filtered_levels.append(level)
            return filtered_levels[-3:]  # 返回最近的3个水平
        
        return []
    
    def _detect_divergence(self, rsi_values: np.ndarray, rsi_gaussian: np.ndarray) -> str:
        """检测RSI背离"""
        
        if len(rsi_values) < 20:
            return 'none'
        
        # 简化的背离检测
        price_trend = np.polyfit(range(10), rsi_values[-10:], 1)[0]
        gaussian_trend = np.polyfit(range(10), rsi_gaussian[-10:], 1)[0]
        
        if price_trend > 0 and gaussian_trend < 0:
            return 'bearish_divergence'
        elif price_trend < 0 and gaussian_trend > 0:
            return 'bullish_divergence'
        
        return 'none'
    
    def _identify_pattern(self, price_features: Dict, rsi_features: Dict, params: Dict) -> str:
        """识别模式"""
        
        # 突破-回调-再突破模式
        if (price_features['trend_type'] == 'bullish' and 
            price_features['is_pullback'] and
            rsi_features['rsi_above_50'] and
            rsi_features['gaussian_above_50'] and
            rsi_features['rsi_trend'] > 0):
            return 'breakout_pullback_continuation'
        
        # 下跌-回调-再下跌模式
        elif (price_features['trend_type'] == 'bearish' and
              price_features['is_pullback'] and
              not rsi_features['rsi_above_50'] and
              not rsi_features['gaussian_above_50'] and
              rsi_features['rsi_trend'] < 0):
            return 'decline_pullback_continuation'
        
        # 可能的反转模式
        elif (price_features['trend_type'] == 'bullish' and
              rsi_features['divergence'] == 'bearish_divergence'):
            return 'potential_bearish_reversal'
        
        elif (price_features['trend_type'] == 'bearish' and
              rsi_features['divergence'] == 'bullish_divergence'):
            return 'potential_bullish_reversal'
        
        # 区间震荡
        elif abs(price_features['price_change_pct']) < 2:
            return 'ranging'
        
        # 趋势延续（无明显回调）
        elif price_features['trend_type'] == 'bullish' and not price_features['is_pullback']:
            return 'bullish_trending'
        
        elif price_features['trend_type'] == 'bearish' and not price_features['is_pullback']:
            return 'bearish_trending'
        
        return 'undefined'
    
    def _calculate_confidence(self, price_features: Dict, rsi_features: Dict, pattern: str) -> float:
        """计算置信度"""
        
        confidence = 0.5  # 基础置信度
        
        # 根据模式调整置信度
        if pattern in ['breakout_pullback_continuation', 'decline_pullback_continuation']:
            # 价格特征
            if price_features['is_pullback']:
                confidence += 0.1
            
            # RSI特征
            if abs(rsi_features['rsi_trend']) > 0.5:
                confidence += 0.1
            
            if abs(rsi_features['rsi_vs_gaussian']) < 5:
                confidence += 0.1
            
            # 趋势强度
            if abs(price_features['price_change_pct']) > 3:
                confidence += 0.1
            
            # 支撑/阻力
            if pattern == 'breakout_pullback_continuation':
                if price_features['current_price'] > max(price_features['resistance_levels'] or [0]):
                    confidence += 0.1
            else:
                if price_features['current_price'] < min(price_features['support_levels'] or [float('inf')]):
                    confidence += 0.1
        
        return min(confidence, 0.95)  # 最大置信度95%
    
    def _get_recommendation(self, pattern: str, confidence: float) -> str:
        """获取操作建议"""
        
        if confidence < 0.6:
            return "信号不明确，建议观望"
        
        recommendations = {
            'breakout_pullback_continuation': "突破回调形态确认，可考虑在回调结束后做多",
            'decline_pullback_continuation': "下跌回调形态确认，可考虑在反弹结束后做空",
            'potential_bearish_reversal': "潜在看跌反转，注意减仓或做空机会",
            'potential_bullish_reversal': "潜在看涨反转，注意建仓或做多机会",
            'ranging': "区间震荡，高抛低吸",
            'bullish_trending': "强势上升趋势，持有多单",
            'bearish_trending': "强势下降趋势，持有空单",
            'undefined': "形态不明确，建议观望"
        }
        
        return recommendations.get(pattern, "形态不明确，建议观望")


def main():
    """主函数 - 用于测试"""
    
    # 模拟数据
    # 突破回调再突破的情况
    prices_bull = [100, 102, 104, 106, 108, 110, 109, 108, 107, 108, 109, 111, 113, 115, 114, 113, 114, 116, 118, 120]
    rsi_bull = [45, 48, 52, 55, 58, 62, 60, 58, 56, 57, 59, 63, 65, 68, 66, 64, 65, 68, 70, 72]
    rsi_gaussian_bull = [45, 47, 50, 53, 56, 59, 60, 59, 58, 58, 60, 62, 64, 66, 67, 66, 67, 69, 71, 71]
    
    # 下跌回调再下跌的情况
    prices_bear = [120, 118, 116, 114, 112, 110, 111, 112, 113, 112, 111, 109, 107, 105, 106, 107, 106, 104, 102, 100]
    rsi_bear = [55, 52, 48, 45, 42, 38, 40, 42, 44, 43, 41, 37, 35, 32, 34, 36, 35, 32, 30, 28]
    rsi_gaussian_bear = [55, 53, 50, 47, 44, 41, 40, 41, 42, 42, 40, 38, 36, 34, 33, 34, 33, 31, 29, 29]
    
    # 创建检测器
    detector = TrendPullbackDetector(
        lookback_period=20,
        rsi_threshold_bull=50,
        rsi_threshold_bear=50,
        price_retracement_pct=0.382,
        min_trend_strength=0.6,
        smoothing_factor=0.8
    )
    
    print("=" * 60)
    print("趋势回调识别系统测试")
    print("=" * 60)
    
    # 测试突破回调情况
    print("\n测试1: 突破-回调-再突破模式")
    print("-" * 40)
    result_bull = detector.detect_pattern(prices_bull, rsi_bull, rsi_gaussian_bull, "15m")
    print(f"识别模式: {result_bull['pattern']}")
    print(f"置信度: {result_bull['confidence']:.2%}")
    print(f"建议: {result_bull['recommendation']}")
    print(f"价格趋势: {result_bull['price_features']['trend_type']}")
    print(f"当前RSI: {result_bull['rsi_features']['current_rsi']:.2f}")
    
    # 测试下跌回调情况
    print("\n测试2: 下跌-回调-再下跌模式")
    print("-" * 40)
    result_bear = detector.detect_pattern(prices_bear, rsi_bear, rsi_gaussian_bear, "15m")
    print(f"识别模式: {result_bear['pattern']}")
    print(f"置信度: {result_bear['confidence']:.2%}")
    print(f"建议: {result_bear['recommendation']}")
    print(f"价格趋势: {result_bear['price_features']['trend_type']}")
    print(f"当前RSI: {result_bear['rsi_features']['current_rsi']:.2f}")
    
    # 测试不同时间框架
    print("\n测试3: 不同时间框架对比")
    print("-" * 40)
    for tf in ['1m', '5m', '15m', '30m', '1h']:
        result = detector.detect_pattern(prices_bull, rsi_bull, rsi_gaussian_bull, tf)
        print(f"{tf}: {result['pattern']} (置信度: {result['confidence']:.2%})")


if __name__ == "__main__":
    main()