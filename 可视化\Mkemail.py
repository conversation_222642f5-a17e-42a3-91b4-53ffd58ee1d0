import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import Head<PERSON>
from typing import List, Optional
from dotenv import load_dotenv

class EmailSender:
    def __init__(self):
        # 加载环境变量
        load_dotenv(r'C:\Users\<USER>\Desktop\my_python\claudeV1\量化\123.env')
        
        # 从环境变量读取配置
        self.email_sender = os.getenv('EMAIL_SENDER')
        self.email_password = os.getenv('EMAIL_PASSWORD')
        self.email_receiver = os.getenv('EMAIL_RECEIVER')
        self.email_port = int(os.getenv('EMAIL_PORT', 587))
        self.email_host = os.getenv('EMAIL_HOST')
        
        # 验证必要配置
        if not all([self.email_sender, self.email_password, self.email_receiver, self.email_host]):
            raise ValueError("缺少必要的邮箱配置信息，请检查环境变量")
    
    def send_email(self, *messages: str, subject: str = "邮件通知", 
                   receiver: Optional[str] = None) -> bool:
        """
        发送邮件
        
        Args:
            *messages: 可变数量的字符串消息
            subject: 邮件主题
            receiver: 接收者邮箱（可选，默认使用环境变量中的接收者）
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 确定接收者
            to_email = receiver or self.email_receiver
            
            # 创建邮件对象
            msg = MIMEMultipart()
            msg['From'] = Header(self.email_sender, 'utf-8')
            msg['To'] = Header(to_email, 'utf-8')
            msg['Subject'] = Header(subject, 'utf-8')
            
            # 合并所有消息
            content = '\n\n'.join(messages)
            
            # 添加邮件正文
            msg.attach(MIMEText(content, 'plain', 'utf-8'))
            
            # 创建SMTP连接
            server = smtplib.SMTP(self.email_host, self.email_port)
            server.starttls()  # 启用TLS加密
            server.login(self.email_sender, self.email_password)
            
            # 发送邮件
            server.sendmail(self.email_sender, [to_email], msg.as_string())
            server.quit()
            
            print(f"邮件发送成功！发送至：{to_email}")
            return True
            
        except Exception as e:
            print(f"邮件发送失败：{str(e)}")
            return False

# 使用示例
if __name__ == "__main__":
    # 创建邮件发送器实例
    sender = EmailSender()
    
    # 示例1：发送单个字符串
    sender.send_email("这是一条测试消息")
    
    # 示例2：发送多个字符串
    sender.send_email(
        "第一条消息：系统启动成功",
        "第二条消息：数据库连接正常",
        "第三条消息：所有服务运行正常",
        subject="系统状态报告"
    )
    
    # 示例3：发送给不同的接收者
    sender.send_email(
        "紧急通知：系统维护将于今晚进行",
        "预计维护时间：2小时",
        subject="系统维护通知",
        receiver="<EMAIL>"
    )
    
    # 示例4：使用变量传递多个消息
    messages = [
        "用户登录异常检测",
        "IP地址：*************",
        "时间：2024-01-15 10:30:00",
        "建议：请立即检查"
    ]
    sender.send_email(*messages, subject="安全警告")