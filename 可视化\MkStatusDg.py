import pandas as pd
import numpy as np
from okx_sdkV1 import get_kline_data, OKXClient
from MkStatus import calculate_ema,calculate_bollinger_bands,calculate_adx


def analyze_market_state(df, window=5):
    """
    市场状态诊断矩阵分析函数
    
    参数:
    df (DataFrame): 包含技术指标的K线数据
    window (int): 计算斜率和趋势的窗口期，默认为5
    
    返回:
    dict: 包含市场状态分析结果的字典
    """
    if df.empty or len(df) < window:
        return {"错误": "数据不足，无法进行分析"}
    
    # 获取最新数据
    latest = df.iloc[-1]
    recent_data = df.tail(window)
    
    # 1. 移动平均线分析
    ema_analysis = analyze_ema_pattern(df, window)
    
    # 2. 布林带分析
    bollinger_analysis = analyze_bollinger_bands(df, window)
    
    # 3. ADX分析
    adx_analysis = analyze_adx_pattern(df, window)
    
    # 4. 综合市场状态判断
    market_state = determine_market_state(ema_analysis, bollinger_analysis, adx_analysis)
    
    # 5. 生成可执行的解读
    actionable_insight = generate_actionable_insight(market_state, latest)
    
    return {
        "当前价格": latest['close'],
        "移动平均线分析": ema_analysis,
        "布林带分析": bollinger_analysis,
        "ADX分析": adx_analysis,
        "市场状态": market_state,
        "可执行解读": actionable_insight,
        "分析时间": latest.name if hasattr(latest, 'name') else "最新数据"
    }

def analyze_ema_pattern(df, window=5):
    """分析EMA均线形态"""
    latest = df.iloc[-1]
    recent_data = df.tail(window)
    
    # 获取EMA值 - 根据实际列名调整
    ema_10 = latest.get('EMA_10', latest.get('ema_kuai', 0))
    ema_20 = latest.get('EMA_20', latest.get('ema_zhong', 0))
    ema_50 = latest.get('EMA_50', latest.get('ema_man', 0))
    
    # 判断排列形态
    if ema_10 > ema_20 > ema_50:
        arrangement = "多头排列"
    elif ema_10 < ema_20 < ema_50:
        arrangement = "空头排列"
    else:
        arrangement = "均线缠绕"
    
    # 计算均线斜率（使用线性回归）
    def calculate_slope(data):
        if len(data) < 2:
            return 0
        x = np.arange(len(data))
        slope = np.polyfit(x, data, 1)[0]
        return slope
    
    ema_10_slope = calculate_slope(recent_data.get('EMA_10', recent_data.get('ema_kuai', [0]*window)))
    ema_20_slope = calculate_slope(recent_data.get('EMA_20', recent_data.get('ema_zhong', [0]*window)))
    ema_50_slope = calculate_slope(recent_data.get('EMA_50', recent_data.get('ema_man', [0]*window)))
    
    # 判断斜率强度
    slope_threshold = 0.0001  # 根据实际情况调整
    if abs(ema_10_slope) > slope_threshold:
        slope_strength = "陡峭"
    else:
        slope_strength = "平缓"
    
    # 计算均线间距
    spacing_10_20 = abs(ema_10 - ema_20) / ema_20 * 100
    spacing_20_50 = abs(ema_20 - ema_50) / ema_50 * 100
    
    spacing_clarity = "清晰" if spacing_10_20 > 0.1 and spacing_20_50 > 0.1 else "模糊"
    
    return {
        "排列形态": arrangement,
        "斜率强度": slope_strength,
        "均线间距": spacing_clarity,
        "EMA_10": round(ema_10, 5),
        "EMA_20": round(ema_20, 5),
        "EMA_50": round(ema_50, 5),
        "10-20间距(%)": round(spacing_10_20, 3),
        "20-50间距(%)": round(spacing_20_50, 3)
    }

def analyze_bollinger_bands(df, window=5):
    """分析布林带状态"""
    latest = df.iloc[-1]
    recent_data = df.tail(window)
    
    # 获取布林带数据
    bb_upper = latest.get('BB_Upper_20', latest.get('BB_upper', 0))
    bb_lower = latest.get('BB_Lower_20', latest.get('BB_down', 0))
    bb_middle = (bb_upper + bb_lower) / 2
    current_price = latest['close']
    
    # 计算布林带宽度
    bb_width = (bb_upper - bb_lower) / bb_middle * 100
    
    # 判断开口状态
    if len(recent_data) >= 2:
        prev_upper = recent_data.iloc[-2].get('BB_Upper_20', recent_data.iloc[-2].get('BB_upper', 0))
        prev_lower = recent_data.iloc[-2].get('BB_Lower_20', recent_data.iloc[-2].get('BB_down', 0))
        prev_width = (prev_upper - prev_lower) / ((prev_upper + prev_lower) / 2) * 100
        
        if bb_width > prev_width * 1.02:
            opening_direction = "向上扩张"
        elif bb_width < prev_width * 0.98:
            opening_direction = "向下收缩"
        else:
            opening_direction = "水平运行"
    else:
        opening_direction = "数据不足"
    
    # 判断价格位置
    upper_threshold = bb_upper - (bb_upper - bb_middle) * 0.2
    lower_threshold = bb_lower + (bb_middle - bb_lower) * 0.2
    
    if current_price >= upper_threshold:
        price_position = "上轨附近(贴边走)"
    elif current_price <= lower_threshold:
        price_position = "下轨附近"
    else:
        price_position = "中轨附近"
    
    # 判断挤压状态
    squeeze_threshold = 2.0  # 根据实际情况调整
    is_squeezed = "是" if bb_width < squeeze_threshold else "否"
    
    return {
        "开口方向": opening_direction,
        "价格位置": price_position,
        "布林带宽度(%)": round(bb_width, 3),
        "是否挤压": is_squeezed,
        "上轨": round(bb_upper, 5),
        "中轨": round(bb_middle, 5),
        "下轨": round(bb_lower, 5)
    }

def analyze_adx_pattern(df, window=5):
    """分析ADX指标"""
    latest = df.iloc[-1]
    recent_data = df.tail(window)
    
    # 获取ADX值
    adx_value = latest.get('ADX_14', latest.get('ADX', 0))
    
    # 判断ADX趋势
    if len(recent_data) >= 2:
        adx_trend = "上升" if adx_value > recent_data.iloc[-2].get('ADX_14', recent_data.iloc[-2].get('ADX', 0)) else "下降"
    else:
        adx_trend = "数据不足"
    
    # 判断趋势强度
    if adx_value > 40:
        trend_strength = "极强"
    elif adx_value > 25:
        trend_strength = "强"
    elif adx_value > 15:
        trend_strength = "中等"
    else:
        trend_strength = "弱"
    
    # 获取DI值（如果有的话）
    di_plus = latest.get('DI_plus', 0)
    di_minus = latest.get('DI_minus', 0)
    
    if di_plus > 0 and di_minus > 0:
        di_direction = "+DI > -DI" if di_plus > di_minus else "-DI > +DI"
    else:
        di_direction = "DI数据不可用"
    
    return {
        "ADX值": round(adx_value, 2),
        "趋势强度": trend_strength,
        "ADX趋势": adx_trend,
        "DI方向": di_direction
    }

def determine_market_state(ema_analysis, bollinger_analysis, adx_analysis):
    """综合判断市场状态"""
    
    # 提取关键指标
    ema_arrangement = ema_analysis["排列形态"]
    ema_slope = ema_analysis["斜率强度"]
    ema_spacing = ema_analysis["均线间距"]
    
    bb_opening = bollinger_analysis["开口方向"]
    bb_price_pos = bollinger_analysis["价格位置"]
    bb_squeezed = bollinger_analysis["是否挤压"]
    
    adx_value = adx_analysis["ADX值"]
    adx_strength = adx_analysis["趋势强度"]
    
    # 判断逻辑
    if (ema_arrangement == "多头排列" and 
        ema_slope == "陡峭" and 
        ema_spacing == "清晰" and
        bb_opening == "向上扩张" and
        "上轨" in bb_price_pos and
        adx_value > 25):
        return "强劲上升趋势"
    
    elif (ema_arrangement == "空头排列" and 
          ema_slope == "陡峭" and 
          ema_spacing == "清晰" and
          bb_opening == "向下收缩" and
          "下轨" in bb_price_pos and
          adx_value > 25):
        return "强劲下降趋势"
    
    elif (ema_arrangement == "均线缠绕" and
          bb_opening == "水平运行" and
          bb_squeezed == "是" and
          adx_value < 25):
        return "盘整/积蓄能量"
    
    elif (adx_value > 40 and adx_analysis["ADX趋势"] == "下降"):
        return "趋势衰竭"
    
    else:
        return "过渡状态"

def generate_actionable_insight(market_state, latest_data):
    """生成可执行的解读建议"""
    
    insights = {
        "强劲上升趋势": {
            "状态描述": "趋势已确认，市场处于强劲上升通道",
            "交易建议": "在回调时寻找金字塔式加仓机会",
            "风险提示": "注意突破失败后的回调风险",
            "关键位置": f"当前价格: {latest_data['close']:.5f}"
        },
        
        "强劲下降趋势": {
            "状态描述": "趋势已确认，市场处于强劲下降通道", 
            "交易建议": "在反弹时寻找金字塔式做空机会",
            "风险提示": "注意超跌反弹的风险",
            "关键位置": f"当前价格: {latest_data['close']:.5f}"
        },
        
        "盘整/积蓄能量": {
            "状态描述": "市场正在为下一次突破做准备",
            "交易建议": "禁止交易，耐心等待突破信号",
            "风险提示": "假突破风险较高，需要确认",
            "关键位置": f"当前价格: {latest_data['close']:.5f}"
        },
        
        "趋势衰竭": {
            "状态描述": "当前趋势显示衰竭迹象",
            "交易建议": "现有头寸应考虑获利了结，不应再开立新的顺势仓位",
            "风险提示": "趋势反转风险增加",
            "关键位置": f"当前价格: {latest_data['close']:.5f}"
        },
        
        "过渡状态": {
            "状态描述": "市场处于过渡阶段，信号不够明确",
            "交易建议": "观望为主，等待更明确的信号",
            "风险提示": "方向不明确，避免重仓操作",
            "关键位置": f"当前价格: {latest_data['close']:.5f}"
        }
    }
    
    return insights.get(market_state, {
        "状态描述": "未知市场状态",
        "交易建议": "建议人工分析",
        "风险提示": "请谨慎操作",
        "关键位置": f"当前价格: {latest_data['close']:.5f}"
    })

def print_market_analysis(analysis_result):
    """美化打印市场分析结果"""
    print("\n" + "="*60)
    print("           市场状态诊断矩阵分析报告")
    print("="*60)
    
    print(f"\n📊 当前价格: {analysis_result['当前价格']:.5f}")
    print(f"⏰ 分析时间: {analysis_result['分析时间']}")
    
    print(f"\n🎯 市场状态: {analysis_result['市场状态']}")
    print("-" * 40)
    
    # 移动平均线分析
    ema = analysis_result['移动平均线分析']
    print(f"\n📈 移动平均线分析:")
    print(f"   排列形态: {ema['排列形态']}")
    print(f"   斜率强度: {ema['斜率强度']}")
    print(f"   均线间距: {ema['均线间距']}")
    print(f"   EMA值: 快线={ema['EMA_10']:.5f}, 中线={ema['EMA_20']:.5f}, 慢线={ema['EMA_50']:.5f}")
    
    # 布林带分析
    bb = analysis_result['布林带分析']
    print(f"\n📊 布林带分析:")
    print(f"   开口方向: {bb['开口方向']}")
    print(f"   价格位置: {bb['价格位置']}")
    print(f"   布林带宽度: {bb['布林带宽度(%)']}%")
    print(f"   是否挤压: {bb['是否挤压']}")
    
    # ADX分析
    adx = analysis_result['ADX分析']
    print(f"\n📈 ADX分析:")
    print(f"   ADX值: {adx['ADX值']}")
    print(f"   趋势强度: {adx['趋势强度']}")
    print(f"   ADX趋势: {adx['ADX趋势']}")
    
    # 可执行解读
    insight = analysis_result['可执行解读']
    print(f"\n💡 可执行解读:")
    print(f"   状态描述: {insight['状态描述']}")
    print(f"   交易建议: {insight['交易建议']}")
    print(f"   风险提示: {insight['风险提示']}")
    print(f"   关键位置: {insight['关键位置']}")
    
    print("\n" + "="*60)

# 示例用法函数
def example_usage():
    print("=== OKX交易所SDK测试 ===\n")
    try:
        # 指定.env文件的路径
        dotenv_path = r'C:\Users\<USER>\Desktop\my_python\claudeV1\量化\123.env'
        client = OKXClient(dotenv_path=dotenv_path)
    except Exception as e:
        print(f"初始化失败: {e}")
        return
    symbol = 'DOGE-USDT-SWAP'
    timeframe = '1m'
  
    
    # 在获取K线数据和计算技术指标后
    df2 = get_kline_data(client, symbol, timeframe='1m', count=150)
    df2 = df2.iloc[:-1]
    calculate_ema(df2, periods=[10, 20, 50], names=['ema_kuai', 'ema_zhong', 'ema_man'])
    calculate_bollinger_bands(df2, period=20, std_dev=2)
    calculate_adx(df2, period=14)
    
    # 进行市场状态分析
    market_analysis = analyze_market_state(df2, window=5)
    print_market_analysis(market_analysis)
   

if __name__ == "__main__":
    example_usage()